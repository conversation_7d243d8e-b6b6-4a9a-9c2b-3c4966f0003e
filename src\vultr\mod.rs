use crate::{config::Config, utils::retry::RetryClient};
use anyhow::Result;
use reqwest::{header::HeaderMap, Client, Response};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use tracing::{error, info, instrument};
use futures::TryStreamExt;

pub mod client;
pub mod models;

pub use client::VultrClient;
pub use models::*;

#[derive(Debug, Clone)]
pub struct VultrApiClient {
    client: RetryClient,
    api_key: String,
    base_url: String,
}

impl VultrApiClient {
    pub fn new(api_key: String, config: &Config) -> Result<Self> {
        let mut headers = HeaderMap::new();
        headers.insert("Authorization", format!("Bearer {}", api_key).parse()?);
        headers.insert("Content-Type", "application/json".parse()?);

        let client = Client::builder()
            .default_headers(headers)
            .timeout(std::time::Duration::from_secs(30))
            .build()?;

        let retry_client = RetryClient::new(client, config.retry_config.clone());

        Ok(Self {
            client: retry_client,
            api_key,
            base_url: "https://api.vultr.com/v2".to_string(),
        })
    }

    #[instrument(skip(self))]
    pub async fn get_account(&self) -> Result<VultrAccount> {
        let url = format!("{}/account", self.base_url);
        let response = self.client.get(&url).await?;

        let account_response: VultrAccountResponse = response.json().await?;
        Ok(account_response.account)
    }

    #[instrument(skip(self))]
    pub async fn get_account_bgp(&self) -> Result<VultrAccountBGP> {
        let url = format!("{}/account/bgp", self.base_url);
        let response = self.client.get(&url).await?;

        let bgp_response: VultrAccountBGPResponse = response.json().await?;
        Ok(bgp_response.bgp)
    }

    #[instrument(skip(self))]
    pub async fn get_account_bandwidth(&self) -> Result<VultrAccountBandwidth> {
        let url = format!("{}/account/bandwidth", self.base_url);
        let response = self.client.get(&url).await?;

        let bandwidth_response: VultrAccountBandwidthResponse = response.json().await?;
        Ok(bandwidth_response.bandwidth)
    }

    #[instrument(skip(self))]
    pub async fn list_instances(&self) -> Result<Vec<VultrInstanceDetailed>> {
        let url = format!("{}/instances", self.base_url);
        let response = self.client.get(&url).await?;
        
        let instances_response: VultrInstancesResponse = response.json().await?;
        Ok(instances_response.instances)
    }

    #[instrument(skip(self))]
    pub async fn get_instance(&self, instance_id: &str) -> Result<VultrInstance> {
        let url = format!("{}/instances/{}", self.base_url, instance_id);
        let response = self.client.get(&url).await?;
        
        let instance_response: VultrInstanceResponse = response.json().await?;
        Ok(instance_response.instance)
    }

    #[instrument(skip(self, request))]
    pub async fn create_instance(&self, request: CreateVultrInstanceRequest) -> Result<VultrInstance> {
        let url = format!("{}/instances", self.base_url);
        let response = self.client.post(&url).json(&request).send().await?;
        
        if !response.status().is_success() {
            let error_text = response.text().await?;
            error!("Failed to create instance: {}", error_text);
            return Err(anyhow::anyhow!("Failed to create instance: {}", error_text));
        }
        
        let instance_response: VultrInstanceResponse = response.json().await?;
        info!("Instance created successfully: {}", instance_response.instance.id);
        Ok(instance_response.instance)
    }

    #[instrument(skip(self))]
    pub async fn delete_instance(&self, instance_id: &str) -> Result<()> {
        let url = format!("{}/instances/{}", self.base_url, instance_id);
        let response = self.client.delete(&url).await?;
        
        if !response.status().is_success() {
            let error_text = response.text().await?;
            error!("Failed to delete instance: {}", error_text);
            return Err(anyhow::anyhow!("Failed to delete instance: {}", error_text));
        }
        
        info!("Instance deleted successfully: {}", instance_id);
        Ok(())
    }

    #[instrument(skip(self))]
    pub async fn start_instance(&self, instance_id: &str) -> Result<()> {
        let url = format!("{}/instances/{}/start", self.base_url, instance_id);
        let response = self.client.post(&url).send().await?;
        
        if !response.status().is_success() {
            let error_text = response.text().await?;
            error!("Failed to start instance: {}", error_text);
            return Err(anyhow::anyhow!("Failed to start instance: {}", error_text));
        }
        
        info!("Instance started successfully: {}", instance_id);
        Ok(())
    }

    #[instrument(skip(self))]
    pub async fn stop_instance(&self, instance_id: &str) -> Result<()> {
        let url = format!("{}/instances/{}/halt", self.base_url, instance_id);
        let response = self.client.post(&url).send().await?;
        
        if !response.status().is_success() {
            let error_text = response.text().await?;
            error!("Failed to stop instance: {}", error_text);
            return Err(anyhow::anyhow!("Failed to stop instance: {}", error_text));
        }
        
        info!("Instance stopped successfully: {}", instance_id);
        Ok(())
    }

    #[instrument(skip(self))]
    pub async fn restart_instance(&self, instance_id: &str) -> Result<()> {
        let url = format!("{}/instances/{}/reboot", self.base_url, instance_id);
        let response = self.client.post(&url).send().await?;
        
        if !response.status().is_success() {
            let error_text = response.text().await?;
            error!("Failed to restart instance: {}", error_text);
            return Err(anyhow::anyhow!("Failed to restart instance: {}", error_text));
        }
        
        info!("Instance restarted successfully: {}", instance_id);
        Ok(())
    }

    #[instrument(skip(self))]
    pub async fn list_plans(&self) -> Result<Vec<VultrPlan>> {
        let url = format!("{}/plans", self.base_url);
        let response = self.client.get(&url).await?;
        
        let plans_response: VultrPlansResponse = response.json().await?;
        Ok(plans_response.plans)
    }

    #[instrument(skip(self))]
    pub async fn list_regions(&self) -> Result<Vec<VultrRegion>> {
        let url = format!("{}/regions", self.base_url);
        let response = self.client.get(&url).await?;
        
        let regions_response: VultrRegionsResponse = response.json().await?;
        Ok(regions_response.regions)
    }

    #[instrument(skip(self))]
    pub async fn list_os(&self) -> Result<Vec<VultrOS>> {
        let url = format!("{}/os", self.base_url);
        let response = self.client.get(&url).await?;

        let os_response: VultrOSResponse = response.json().await?;
        Ok(os_response.os)
    }

    // Backup endpoints
    #[instrument(skip(self))]
    pub async fn list_backups(&self) -> Result<Vec<VultrBackup>> {
        let url = format!("{}/backups", self.base_url);
        let response = self.client.get(&url).await?;

        let backups_response: VultrBackupsResponse = response.json().await?;
        Ok(backups_response.backups)
    }

    #[instrument(skip(self))]
    pub async fn get_backup(&self, backup_id: &str) -> Result<VultrBackup> {
        let url = format!("{}/backups/{}", self.base_url, backup_id);
        let response = self.client.get(&url).await?;

        let backup: VultrBackup = response.json().await?;
        Ok(backup)
    }

    // Bare Metal endpoints
    #[instrument(skip(self))]
    pub async fn list_bare_metal(&self) -> Result<Vec<VultrBareMetal>> {
        let url = format!("{}/bare-metals", self.base_url);
        let response = self.client.get(&url).await?;

        let bare_metal_response: VultrBareMetalResponse = response.json().await?;
        Ok(bare_metal_response.bare_metals)
    }

    #[instrument(skip(self))]
    pub async fn get_bare_metal(&self, bare_metal_id: &str) -> Result<VultrBareMetal> {
        let url = format!("{}/bare-metals/{}", self.base_url, bare_metal_id);
        let response = self.client.get(&url).await?;

        let bare_metal: VultrBareMetal = response.json().await?;
        Ok(bare_metal)
    }

    #[instrument(skip(self))]
    pub async fn create_bare_metal(&self, request: CreateBareMetalRequest) -> Result<VultrBareMetal> {
        let url = format!("{}/bare-metals", self.base_url);
        let response = self.client.post(&url).json(&request).send().await?;

        let bare_metal: VultrBareMetal = response.json().await?;
        Ok(bare_metal)
    }

    #[instrument(skip(self))]
    pub async fn update_bare_metal(&self, baremetal_id: &str, request: UpdateBareMetalRequest) -> Result<VultrBareMetal> {
        let url = format!("{}/bare-metals/{}", self.base_url, baremetal_id);
        let response = self.client.patch(&url).json(&request).send().await?;

        let bare_metal: VultrBareMetal = response.json().await?;
        Ok(bare_metal)
    }

    #[instrument(skip(self))]
    pub async fn delete_bare_metal(&self, baremetal_id: &str) -> Result<()> {
        let url = format!("{}/bare-metals/{}", self.base_url, baremetal_id);
        let response = self.client.delete(&url).await?;
        Ok(())
    }

    #[instrument(skip(self))]
    pub async fn get_bare_metal_ipv4(&self, baremetal_id: &str) -> Result<Vec<BareMetalIpv4Info>> {
        let url = format!("{}/bare-metals/{}/ipv4", self.base_url, baremetal_id);
        let response = self.client.get(&url).await?;

        let ipv4_response: VultrBareMetalIpv4Response = response.json().await?;
        Ok(ipv4_response.ipv4s)
    }

    #[instrument(skip(self))]
    pub async fn get_bare_metal_ipv6(&self, baremetal_id: &str) -> Result<Vec<BareMetalIpv6Info>> {
        let url = format!("{}/bare-metals/{}/ipv6", self.base_url, baremetal_id);
        let response = self.client.get(&url).await?;

        let ipv6_response: VultrBareMetalIpv6Response = response.json().await?;
        Ok(ipv6_response.ipv6s)
    }

    #[instrument(skip(self))]
    pub async fn get_bare_metal_bandwidth(&self, baremetal_id: &str) -> Result<BareMetalBandwidth> {
        let url = format!("{}/bare-metals/{}/bandwidth", self.base_url, baremetal_id);
        let response = self.client.get(&url).await?;

        let bandwidth: BareMetalBandwidth = response.json().await?;
        Ok(bandwidth)
    }

    #[instrument(skip(self))]
    pub async fn get_bare_metal_user_data(&self, baremetal_id: &str) -> Result<BareMetalUserData> {
        let url = format!("{}/bare-metals/{}/user-data", self.base_url, baremetal_id);
        let response = self.client.get(&url).await?;

        let user_data: BareMetalUserData = response.json().await?;
        Ok(user_data)
    }

    #[instrument(skip(self))]
    pub async fn get_bare_metal_upgrades(&self, baremetal_id: &str) -> Result<BareMetalUpgrades> {
        let url = format!("{}/bare-metals/{}/upgrades", self.base_url, baremetal_id);
        let response = self.client.get(&url).await?;

        let upgrades: BareMetalUpgrades = response.json().await?;
        Ok(upgrades)
    }

    #[instrument(skip(self))]
    pub async fn get_bare_metal_vnc(&self, baremetal_id: &str) -> Result<BareMetalVncInfo> {
        let url = format!("{}/bare-metals/{}/vnc", self.base_url, baremetal_id);
        let response = self.client.get(&url).await?;

        let vnc_info: BareMetalVncInfo = response.json().await?;
        Ok(vnc_info)
    }

    #[instrument(skip(self))]
    pub async fn list_bare_metal_vpcs(&self, baremetal_id: &str) -> Result<Vec<BareMetalVpcInfo>> {
        let url = format!("{}/bare-metals/{}/vpcs", self.base_url, baremetal_id);
        let response = self.client.get(&url).await?;

        let vpc_response: VultrBareMetalVpcsResponse = response.json().await?;
        Ok(vpc_response.vpcs)
    }

    // Block Storage endpoints
    #[instrument(skip(self))]
    pub async fn list_block_storage(&self) -> Result<Vec<VultrBlockStorage>> {
        let url = format!("{}/blocks", self.base_url);
        let response = self.client.get(&url).await?;

        let blocks_response: VultrBlockStorageResponse = response.json().await?;
        Ok(blocks_response.blocks)
    }

    #[instrument(skip(self))]
    pub async fn get_block_storage(&self, block_id: &str) -> Result<VultrBlockStorage> {
        let url = format!("{}/blocks/{}", self.base_url, block_id);
        let response = self.client.get(&url).await?;

        let block: VultrBlockStorage = response.json().await?;
        Ok(block)
    }

    #[instrument(skip(self, request))]
    pub async fn create_block_storage(&self, request: serde_json::Value) -> Result<VultrBlockStorage> {
        let url = format!("{}/blocks", self.base_url);
        let response = self.client.post(&url).json(&request).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            error!("Failed to create block storage: {}", error_text);
            return Err(anyhow::anyhow!("Failed to create block storage: {}", error_text));
        }

        let block: VultrBlockStorage = response.json().await?;
        info!("Block storage created successfully: {}", block.id);
        Ok(block)
    }

    #[instrument(skip(self))]
    pub async fn delete_block_storage(&self, block_id: &str) -> Result<()> {
        let url = format!("{}/blocks/{}", self.base_url, block_id);
        let response = self.client.delete(&url).await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            error!("Failed to delete block storage: {}", error_text);
            return Err(anyhow::anyhow!("Failed to delete block storage: {}", error_text));
        }

        info!("Block storage deleted successfully: {}", block_id);
        Ok(())
    }

    #[instrument(skip(self, request))]
    pub async fn update_block_storage(&self, block_id: &str, request: UpdateBlockStorageRequest) -> Result<VultrBlockStorage> {
        let url = format!("{}/blocks/{}", self.base_url, block_id);
        let response = self.client.put(&url).json(&request).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to update block storage: {}", error_text));
        }

        let block: VultrBlockStorage = response.json().await?;
        Ok(block)
    }

    #[instrument(skip(self, request))]
    pub async fn attach_block_storage(&self, block_id: &str, request: AttachBlockStorageRequest) -> Result<()> {
        let url = format!("{}/blocks/{}/attach", self.base_url, block_id);
        let response = self.client.post(&url).json(&request).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to attach block storage: {}", error_text));
        }

        Ok(())
    }

    #[instrument(skip(self, request))]
    pub async fn detach_block_storage(&self, block_id: &str, request: DetachBlockStorageRequest) -> Result<()> {
        let url = format!("{}/blocks/{}/detach", self.base_url, block_id);
        let response = self.client.post(&url).json(&request).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to detach block storage: {}", error_text));
        }

        Ok(())
    }

    // CDN endpoints
    #[instrument(skip(self))]
    pub async fn list_cdns(&self) -> Result<Vec<VultrCDN>> {
        let url = format!("{}/cdns", self.base_url);
        let response = self.client.get(&url).await?;

        let cdns_response: VultrCDNResponse = response.json().await?;
        Ok(cdns_response.cdns)
    }

    // DNS endpoints
    #[instrument(skip(self))]
    pub async fn list_dns_domains(&self) -> Result<Vec<VultrDNSDomain>> {
        let url = format!("{}/domains", self.base_url);
        let response = self.client.get(&url).await?;

        let dns_response: VultrDNSResponse = response.json().await?;
        Ok(dns_response.domains)
    }

    #[instrument(skip(self))]
    pub async fn get_dns_records(&self, domain: &str) -> Result<Vec<VultrDNSRecord>> {
        let url = format!("{}/domains/{}/records", self.base_url, domain);
        let response = self.client.get(&url).await?;

        let records: Vec<VultrDNSRecord> = response.json().await?;
        Ok(records)
    }

    // Firewall endpoints
    #[instrument(skip(self))]
    pub async fn list_firewall_groups(&self) -> Result<Vec<VultrFirewallGroup>> {
        let url = format!("{}/firewalls", self.base_url);
        let response = self.client.get(&url).await?;

        let firewalls: Vec<VultrFirewallGroup> = response.json().await?;
        Ok(firewalls)
    }

    #[instrument(skip(self))]
    pub async fn get_firewall_rules(&self, firewall_group_id: &str) -> Result<Vec<VultrFirewallRule>> {
        let url = format!("{}/firewalls/{}/rules", self.base_url, firewall_group_id);
        let response = self.client.get(&url).await?;

        let rules: Vec<VultrFirewallRule> = response.json().await?;
        Ok(rules)
    }

    // Kubernetes endpoints
    #[instrument(skip(self))]
    pub async fn list_kubernetes_clusters(&self) -> Result<Vec<VultrKubernetesCluster>> {
        let url = format!("{}/kubernetes/clusters", self.base_url);
        let response = self.client.get(&url).await?;

        let clusters: Vec<VultrKubernetesCluster> = response.json().await?;
        Ok(clusters)
    }

    #[instrument(skip(self))]
    pub async fn get_kubernetes_cluster(&self, cluster_id: &str) -> Result<VultrKubernetesCluster> {
        let url = format!("{}/kubernetes/clusters/{}", self.base_url, cluster_id);
        let response = self.client.get(&url).await?;

        let cluster: VultrKubernetesCluster = response.json().await?;
        Ok(cluster)
    }

    // Load Balancer endpoints
    #[instrument(skip(self))]
    pub async fn list_load_balancers(&self) -> Result<Vec<VultrLoadBalancer>> {
        let url = format!("{}/load-balancers", self.base_url);
        let response = self.client.get(&url).await?;

        let load_balancers: Vec<VultrLoadBalancer> = response.json().await?;
        Ok(load_balancers)
    }

    #[instrument(skip(self))]
    pub async fn get_load_balancer(&self, lb_id: &str) -> Result<VultrLoadBalancer> {
        let url = format!("{}/load-balancers/{}", self.base_url, lb_id);
        let response = self.client.get(&url).await?;

        let load_balancer: VultrLoadBalancer = response.json().await?;
        Ok(load_balancer)
    }

    // Managed Database endpoints
    #[instrument(skip(self))]
    pub async fn list_databases(&self) -> Result<Vec<VultrManagedDatabase>> {
        let url = format!("{}/databases", self.base_url);
        let response = self.client.get(&url).await?;

        let databases: Vec<VultrManagedDatabase> = response.json().await?;
        Ok(databases)
    }

    #[instrument(skip(self))]
    pub async fn get_database(&self, database_id: &str) -> Result<VultrManagedDatabase> {
        let url = format!("{}/databases/{}", self.base_url, database_id);
        let response = self.client.get(&url).await?;

        let database: VultrManagedDatabase = response.json().await?;
        Ok(database)
    }

    // Object Storage (S3) endpoints
    #[instrument(skip(self))]
    pub async fn list_object_storage(&self) -> Result<Vec<VultrObjectStorage>> {
        let url = format!("{}/object-storage", self.base_url);
        let response = self.client.get(&url).await?;

        let object_storage: Vec<VultrObjectStorage> = response.json().await?;
        Ok(object_storage)
    }

    // VPC endpoints
    #[instrument(skip(self))]
    pub async fn list_vpcs(&self) -> Result<Vec<VultrVPC>> {
        let url = format!("{}/vpcs", self.base_url);
        let response = self.client.get(&url).await?;

        let vpcs: Vec<VultrVPC> = response.json().await?;
        Ok(vpcs)
    }

    #[instrument(skip(self))]
    pub async fn list_vpc2(&self) -> Result<Vec<VultrVPC2>> {
        let url = format!("{}/vpc2", self.base_url);
        let response = self.client.get(&url).await?;

        let vpc2s: Vec<VultrVPC2> = response.json().await?;
        Ok(vpc2s)
    }

    // Reserved IP endpoints
    #[instrument(skip(self))]
    pub async fn list_reserved_ips(&self) -> Result<Vec<VultrReservedIP>> {
        let url = format!("{}/reserved-ips", self.base_url);
        let response = self.client.get(&url).await?;

        let reserved_ips: Vec<VultrReservedIP> = response.json().await?;
        Ok(reserved_ips)
    }

    // Snapshot endpoints
    #[instrument(skip(self))]
    pub async fn list_snapshots(&self) -> Result<Vec<VultrSnapshot>> {
        let url = format!("{}/snapshots", self.base_url);
        let response = self.client.get(&url).await?;

        let snapshots: Vec<VultrSnapshot> = response.json().await?;
        Ok(snapshots)
    }

    #[instrument(skip(self))]
    pub async fn get_snapshot(&self, snapshot_id: &str) -> Result<VultrSnapshot> {
        let url = format!("{}/snapshots/{}", self.base_url, snapshot_id);
        let response = self.client.get(&url).await?;

        let snapshot: VultrSnapshot = response.json().await?;
        Ok(snapshot)
    }

    // Sub-Account endpoints
    #[instrument(skip(self))]
    pub async fn list_sub_accounts(&self) -> Result<Vec<VultrSubAccount>> {
        let url = format!("{}/subaccounts", self.base_url);
        let response = self.client.get(&url).await?;

        let sub_accounts: Vec<VultrSubAccount> = response.json().await?;
        Ok(sub_accounts)
    }

    // SSH Key endpoints
    #[instrument(skip(self))]
    pub async fn list_ssh_keys(&self) -> Result<Vec<VultrSSHKey>> {
        let url = format!("{}/ssh-keys", self.base_url);
        let response = self.client.get(&url).await?;

        let ssh_keys_response: VultrSSHKeysResponse = response.json().await?;
        Ok(ssh_keys_response.ssh_keys)
    }

    #[instrument(skip(self))]
    pub async fn get_ssh_key(&self, ssh_key_id: &str) -> Result<VultrSSHKey> {
        let url = format!("{}/ssh-keys/{}", self.base_url, ssh_key_id);
        let response = self.client.get(&url).await?;

        let ssh_key: VultrSSHKey = response.json().await?;
        Ok(ssh_key)
    }

    #[instrument(skip(self, request))]
    pub async fn create_ssh_key(&self, request: CreateSSHKeyRequest) -> Result<VultrSSHKey> {
        let url = format!("{}/ssh-keys", self.base_url);
        let response = self.client.post(&url).json(&request).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to create SSH key: {}", error_text));
        }

        let ssh_key: VultrSSHKey = response.json().await?;
        Ok(ssh_key)
    }

    // Startup Script endpoints
    #[instrument(skip(self))]
    pub async fn list_startup_scripts(&self) -> Result<Vec<VultrStartupScript>> {
        let url = format!("{}/startup-scripts", self.base_url);
        let response = self.client.get(&url).await?;

        let startup_scripts: Vec<VultrStartupScript> = response.json().await?;
        Ok(startup_scripts)
    }

    #[instrument(skip(self))]
    pub async fn get_startup_script(&self, script_id: &str) -> Result<VultrStartupScript> {
        let url = format!("{}/startup-scripts/{}", self.base_url, script_id);
        let response = self.client.get(&url).await?;

        let startup_script: VultrStartupScript = response.json().await?;
        Ok(startup_script)
    }

    #[instrument(skip(self, request))]
    pub async fn create_startup_script(&self, request: CreateStartupScriptRequest) -> Result<VultrStartupScript> {
        let url = format!("{}/startup-scripts", self.base_url);
        let response = self.client.post(&url).json(&request).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to create startup script: {}", error_text));
        }

        let startup_script: VultrStartupScript = response.json().await?;
        Ok(startup_script)
    }

    // ISO endpoints
    #[instrument(skip(self))]
    pub async fn list_isos(&self) -> Result<Vec<VultrISO>> {
        let url = format!("{}/iso", self.base_url);
        let response = self.client.get(&url).await?;

        let isos: Vec<VultrISO> = response.json().await?;
        Ok(isos)
    }

    #[instrument(skip(self))]
    pub async fn get_iso(&self, iso_id: &str) -> Result<VultrISO> {
        let url = format!("{}/iso/{}", self.base_url, iso_id);
        let response = self.client.get(&url).await?;

        let iso: VultrISO = response.json().await?;
        Ok(iso)
    }

    #[instrument(skip(self, request))]
    pub async fn create_iso(&self, request: CreateISORequest) -> Result<VultrISO> {
        let url = format!("{}/iso", self.base_url);
        let response = self.client.post(&url).json(&request).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to create ISO: {}", error_text));
        }

        let iso: VultrISO = response.json().await?;
        Ok(iso)
    }

    // Application endpoints
    #[instrument(skip(self))]
    pub async fn list_applications(&self) -> Result<Vec<VultrApplication>> {
        let url = format!("{}/applications", self.base_url);
        let response = self.client.get(&url).await?;

        let applications: Vec<VultrApplication> = response.json().await?;
        Ok(applications)
    }

    // Marketplace endpoints
    #[instrument(skip(self))]
    pub async fn list_marketplace_apps(&self) -> Result<Vec<VultrMarketplaceApp>> {
        let url = format!("{}/marketplace/apps", self.base_url);
        let response = self.client.get(&url).await?;

        let marketplace_apps: Vec<VultrMarketplaceApp> = response.json().await?;
        Ok(marketplace_apps)
    }

    // Billing endpoints
    #[instrument(skip(self))]
    pub async fn get_billing_history(&self) -> Result<Vec<VultrBillingHistory>> {
        let url = format!("{}/billing/history", self.base_url);
        let response = self.client.get(&url).await?;

        let billing_history: Vec<VultrBillingHistory> = response.json().await?;
        Ok(billing_history)
    }

    #[instrument(skip(self))]
    pub async fn list_invoices(&self) -> Result<Vec<VultrInvoice>> {
        let url = format!("{}/billing/invoices", self.base_url);
        let response = self.client.get(&url).await?;

        let invoices: Vec<VultrInvoice> = response.json().await?;
        Ok(invoices)
    }

    // Storage Gateway endpoints
    #[instrument(skip(self))]
    pub async fn list_storage_gateways(&self) -> Result<Vec<VultrStorageGateway>> {
        let url = format!("{}/storage-gateways", self.base_url);
        let response = self.client.get(&url).await?;

        let storage_gateways: Vec<VultrStorageGateway> = response.json().await?;
        Ok(storage_gateways)
    }

    // User endpoints
    #[instrument(skip(self))]
    pub async fn list_users(&self) -> Result<Vec<VultrUser>> {
        let url = format!("{}/users", self.base_url);
        let response = self.client.get(&url).await?;

        let users: Vec<VultrUser> = response.json().await?;
        Ok(users)
    }

    // Container Registry endpoints
    #[instrument(skip(self))]
    pub async fn list_container_registries(&self) -> Result<Vec<VultrContainerRegistry>> {
        let url = format!("{}/registry", self.base_url);
        let response = self.client.get(&url).await?;

        let registries: Vec<VultrContainerRegistry> = response.json().await?;
        Ok(registries)
    }

    #[instrument(skip(self))]
    pub async fn get_container_registry(&self, registry_id: &str) -> Result<VultrContainerRegistry> {
        let url = format!("{}/registry/{}", self.base_url, registry_id);
        let response = self.client.get(&url).await?;

        let registry: VultrContainerRegistry = response.json().await?;
        Ok(registry)
    }



    // Additional Billing endpoints
    #[instrument(skip(self))]
    pub async fn get_invoice_items(&self, invoice_id: &str) -> Result<Vec<VultrInvoiceItem>> {
        let url = format!("{}/billing/invoices/{}/items", self.base_url, invoice_id);
        let response = self.client.get(&url).await?;

        let invoice_items_response: VultrInvoiceItemsResponse = response.json().await?;
        Ok(invoice_items_response.invoice_items)
    }

    #[instrument(skip(self))]
    pub async fn get_pending_charges(&self) -> Result<VultrPendingCharges> {
        let url = format!("{}/billing/pending-charges", self.base_url);
        let response = self.client.get(&url).await?;

        let pending_charges_response: VultrPendingChargesResponse = response.json().await?;
        Ok(pending_charges_response.billing)
    }

    // CDN Pull Zone endpoints
    #[instrument(skip(self))]
    pub async fn list_pull_zones(&self) -> Result<Vec<VultrPullZone>> {
        let url = format!("{}/cdns/pull-zones", self.base_url);
        let response = self.client.get(&url).await?;

        let pull_zones_response: VultrPullZoneResponse = response.json().await?;
        Ok(pull_zones_response.pullzones)
    }

    #[instrument(skip(self, request))]
    pub async fn create_pull_zone(&self, request: CreatePullZoneRequest) -> Result<VultrPullZone> {
        let url = format!("{}/cdns/pull-zones", self.base_url);
        let response = self.client.post(&url).json(&request).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to create pull zone: {}", error_text));
        }

        let pull_zone: VultrPullZone = response.json().await?;
        Ok(pull_zone)
    }

    #[instrument(skip(self))]
    pub async fn get_pull_zone(&self, pullzone_id: &str) -> Result<VultrPullZone> {
        let url = format!("{}/cdns/pull-zones/{}", self.base_url, pullzone_id);
        let response = self.client.get(&url).await?;

        let pull_zone: VultrPullZone = response.json().await?;
        Ok(pull_zone)
    }

    #[instrument(skip(self, request))]
    pub async fn update_pull_zone(&self, pullzone_id: &str, request: UpdatePullZoneRequest) -> Result<VultrPullZone> {
        let url = format!("{}/cdns/pull-zones/{}", self.base_url, pullzone_id);
        let response = self.client.put(&url).json(&request).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to update pull zone: {}", error_text));
        }

        let pull_zone: VultrPullZone = response.json().await?;
        Ok(pull_zone)
    }

    #[instrument(skip(self))]
    pub async fn delete_pull_zone(&self, pullzone_id: &str) -> Result<()> {
        let url = format!("{}/cdns/pull-zones/{}", self.base_url, pullzone_id);
        let response = self.client.delete(&url).await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to delete pull zone: {}", error_text));
        }

        Ok(())
    }

    #[instrument(skip(self, request))]
    pub async fn purge_pull_zone(&self, pullzone_id: &str, request: PurgePullZoneRequest) -> Result<()> {
        let url = format!("{}/cdns/pull-zones/{}/purge", self.base_url, pullzone_id);
        let response = self.client.post(&url).json(&request).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to purge pull zone: {}", error_text));
        }

        Ok(())
    }

    // CDN Push Zone endpoints
    #[instrument(skip(self))]
    pub async fn list_push_zones(&self) -> Result<Vec<VultrPushZone>> {
        let url = format!("{}/cdns/push-zones", self.base_url);
        let response = self.client.get(&url).await?;

        let push_zones_response: VultrPushZoneResponse = response.json().await?;
        Ok(push_zones_response.pushzones)
    }

    #[instrument(skip(self, request))]
    pub async fn create_push_zone(&self, request: CreatePushZoneRequest) -> Result<VultrPushZone> {
        let url = format!("{}/cdns/push-zones", self.base_url);
        let response = self.client.post(&url).json(&request).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to create push zone: {}", error_text));
        }

        let push_zone: VultrPushZone = response.json().await?;
        Ok(push_zone)
    }

    #[instrument(skip(self))]
    pub async fn get_push_zone(&self, pushzone_id: &str) -> Result<VultrPushZone> {
        let url = format!("{}/cdns/push-zones/{}", self.base_url, pushzone_id);
        let response = self.client.get(&url).await?;

        let push_zone: VultrPushZone = response.json().await?;
        Ok(push_zone)
    }

    #[instrument(skip(self, request))]
    pub async fn update_push_zone(&self, pushzone_id: &str, request: UpdatePushZoneRequest) -> Result<VultrPushZone> {
        let url = format!("{}/cdns/push-zones/{}", self.base_url, pushzone_id);
        let response = self.client.put(&url).json(&request).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to update push zone: {}", error_text));
        }

        let push_zone: VultrPushZone = response.json().await?;
        Ok(push_zone)
    }

    #[instrument(skip(self))]
    pub async fn delete_push_zone(&self, pushzone_id: &str) -> Result<()> {
        let url = format!("{}/cdns/push-zones/{}", self.base_url, pushzone_id);
        let response = self.client.delete(&url).await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to delete push zone: {}", error_text));
        }

        Ok(())
    }

    #[instrument(skip(self))]
    pub async fn get_push_zone_files(&self, pushzone_id: &str) -> Result<Vec<VultrPushZoneFile>> {
        let url = format!("{}/cdns/push-zones/{}/files", self.base_url, pushzone_id);
        let response = self.client.get(&url).await?;

        let files_response: VultrPushZoneFilesResponse = response.json().await?;
        Ok(files_response.files)
    }

    #[instrument(skip(self))]
    pub async fn delete_push_zone_file(&self, pushzone_id: &str, file_name: &str) -> Result<()> {
        let url = format!("{}/cdns/push-zones/{}/files/{}", self.base_url, pushzone_id, file_name);
        let response = self.client.delete(&url).await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to delete push zone file: {}", error_text));
        }

        Ok(())
    }

    // Enhanced Container Registry endpoints
    #[instrument(skip(self, request))]
    pub async fn create_registry(&self, request: CreateRegistryRequest) -> Result<VultrContainerRegistry> {
        let url = format!("{}/registry", self.base_url);
        let response = self.client.post(&url).json(&request).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to create registry: {}", error_text));
        }

        let registry: VultrContainerRegistry = response.json().await?;
        Ok(registry)
    }

    #[instrument(skip(self, request))]
    pub async fn update_registry(&self, registry_id: &str, request: UpdateRegistryRequest) -> Result<VultrContainerRegistry> {
        let url = format!("{}/registry/{}", self.base_url, registry_id);
        let response = self.client.put(&url).json(&request).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to update registry: {}", error_text));
        }

        let registry: VultrContainerRegistry = response.json().await?;
        Ok(registry)
    }

    #[instrument(skip(self))]
    pub async fn delete_registry(&self, registry_id: &str) -> Result<()> {
        let url = format!("{}/registry/{}", self.base_url, registry_id);
        let response = self.client.delete(&url).await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to delete registry: {}", error_text));
        }

        Ok(())
    }

    #[instrument(skip(self))]
    pub async fn list_registry_replications(&self, registry_id: &str) -> Result<Vec<VultrRegistryReplication>> {
        let url = format!("{}/registry/{}/replication", self.base_url, registry_id);
        let response = self.client.get(&url).await?;

        let replications_response: VultrRegistryReplicationResponse = response.json().await?;
        Ok(replications_response.replications)
    }

    #[instrument(skip(self, request))]
    pub async fn create_registry_replication(&self, registry_id: &str, request: CreateReplicationRequest) -> Result<VultrRegistryReplication> {
        let url = format!("{}/registry/{}/replication", self.base_url, registry_id);
        let response = self.client.post(&url).json(&request).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to create replication: {}", error_text));
        }

        let replication: VultrRegistryReplication = response.json().await?;
        Ok(replication)
    }

    #[instrument(skip(self))]
    pub async fn get_registry_replication(&self, registry_id: &str, replication_id: &str) -> Result<VultrRegistryReplication> {
        let url = format!("{}/registry/{}/replication/{}", self.base_url, registry_id, replication_id);
        let response = self.client.get(&url).await?;

        let replication: VultrRegistryReplication = response.json().await?;
        Ok(replication)
    }

    #[instrument(skip(self))]
    pub async fn delete_registry_replication(&self, registry_id: &str, replication_id: &str) -> Result<()> {
        let url = format!("{}/registry/{}/replication/{}", self.base_url, registry_id, replication_id);
        let response = self.client.delete(&url).await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to delete replication: {}", error_text));
        }

        Ok(())
    }

    #[instrument(skip(self))]
    pub async fn list_registry_repositories(&self, registry_id: &str) -> Result<Vec<VultrRegistryRepository>> {
        let url = format!("{}/registry/{}/repository", self.base_url, registry_id);
        let response = self.client.get(&url).await?;

        let repositories_response: VultrRegistryRepositoryResponse = response.json().await?;
        Ok(repositories_response.repositories)
    }

    #[instrument(skip(self))]
    pub async fn get_registry_repository(&self, registry_id: &str, repository_image: &str) -> Result<VultrRegistryRepository> {
        let url = format!("{}/registry/{}/repository/{}", self.base_url, registry_id, repository_image);
        let response = self.client.get(&url).await?;

        let repository: VultrRegistryRepository = response.json().await?;
        Ok(repository)
    }

    #[instrument(skip(self, request))]
    pub async fn update_registry_repository(&self, registry_id: &str, repository_image: &str, request: UpdateRepositoryRequest) -> Result<VultrRegistryRepository> {
        let url = format!("{}/registry/{}/repository/{}", self.base_url, registry_id, repository_image);
        let response = self.client.put(&url).json(&request).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to update repository: {}", error_text));
        }

        let repository: VultrRegistryRepository = response.json().await?;
        Ok(repository)
    }

    #[instrument(skip(self))]
    pub async fn delete_registry_repository(&self, registry_id: &str, repository_image: &str) -> Result<()> {
        let url = format!("{}/registry/{}/repository/{}", self.base_url, registry_id, repository_image);
        let response = self.client.delete(&url).await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to delete repository: {}", error_text));
        }

        Ok(())
    }

    #[instrument(skip(self))]
    pub async fn create_registry_docker_credentials(&self, registry_id: &str) -> Result<VultrDockerCredentials> {
        let url = format!("{}/registry/{}/docker-credentials", self.base_url, registry_id);
        let response = self.client.post(&url).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to create docker credentials: {}", error_text));
        }

        let credentials: VultrDockerCredentials = response.json().await?;
        Ok(credentials)
    }

    #[instrument(skip(self))]
    pub async fn create_registry_kubernetes_docker_credentials(&self, registry_id: &str) -> Result<VultrKubernetesDockerCredentials> {
        let url = format!("{}/registry/{}/kubernetes-docker-credentials", self.base_url, registry_id);
        let response = self.client.post(&url).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to create kubernetes docker credentials: {}", error_text));
        }

        let credentials: VultrKubernetesDockerCredentials = response.json().await?;
        Ok(credentials)
    }

    #[instrument(skip(self, request))]
    pub async fn update_registry_password(&self, registry_id: &str, request: UpdateRegistryPasswordRequest) -> Result<()> {
        let url = format!("{}/registry/{}/password", self.base_url, registry_id);
        let response = self.client.put(&url).json(&request).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to update registry password: {}", error_text));
        }

        Ok(())
    }

    #[instrument(skip(self))]
    pub async fn list_registry_robots(&self, registry_id: &str) -> Result<Vec<VultrRegistryRobot>> {
        let url = format!("{}/registry/{}/robot", self.base_url, registry_id);
        let response = self.client.get(&url).await?;

        let robots_response: VultrRegistryRobotResponse = response.json().await?;
        Ok(robots_response.robots)
    }

    #[instrument(skip(self))]
    pub async fn get_registry_robot(&self, registry_id: &str, robot_name: &str) -> Result<VultrRegistryRobot> {
        let url = format!("{}/registry/{}/robot/{}", self.base_url, registry_id, robot_name);
        let response = self.client.get(&url).await?;

        let robot: VultrRegistryRobot = response.json().await?;
        Ok(robot)
    }

    #[instrument(skip(self, request))]
    pub async fn update_registry_robot(&self, registry_id: &str, robot_name: &str, request: UpdateRobotRequest) -> Result<VultrRegistryRobot> {
        let url = format!("{}/registry/{}/robot/{}", self.base_url, registry_id, robot_name);
        let response = self.client.put(&url).json(&request).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to update robot: {}", error_text));
        }

        let robot: VultrRegistryRobot = response.json().await?;
        Ok(robot)
    }

    #[instrument(skip(self))]
    pub async fn delete_registry_robot(&self, registry_id: &str, robot_name: &str) -> Result<()> {
        let url = format!("{}/registry/{}/robot/{}", self.base_url, registry_id, robot_name);
        let response = self.client.delete(&url).await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to delete robot: {}", error_text));
        }

        Ok(())
    }

    #[instrument(skip(self))]
    pub async fn list_registry_repository_artifacts(&self, registry_id: &str, repository_image: &str) -> Result<Vec<VultrRegistryArtifact>> {
        let url = format!("{}/registry/{}/repository/{}/artifact", self.base_url, registry_id, repository_image);
        let response = self.client.get(&url).await?;

        let artifacts_response: VultrRegistryArtifactResponse = response.json().await?;
        Ok(artifacts_response.artifacts)
    }

    #[instrument(skip(self))]
    pub async fn get_registry_repository_artifact(&self, registry_id: &str, repository_image: &str, artifact_digest: &str) -> Result<VultrRegistryArtifact> {
        let url = format!("{}/registry/{}/repository/{}/artifact/{}", self.base_url, registry_id, repository_image, artifact_digest);
        let response = self.client.get(&url).await?;

        let artifact: VultrRegistryArtifact = response.json().await?;
        Ok(artifact)
    }

    #[instrument(skip(self))]
    pub async fn delete_registry_repository_artifact(&self, registry_id: &str, repository_image: &str, artifact_digest: &str) -> Result<()> {
        let url = format!("{}/registry/{}/repository/{}/artifact/{}", self.base_url, registry_id, repository_image, artifact_digest);
        let response = self.client.delete(&url).await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to delete artifact: {}", error_text));
        }

        Ok(())
    }

    #[instrument(skip(self))]
    pub async fn list_registry_regions(&self) -> Result<Vec<VultrRegistryRegion>> {
        let url = format!("{}/registry/region/list", self.base_url);
        let response = self.client.get(&url).await?;

        let regions_response: VultrRegistryRegionResponse = response.json().await?;
        Ok(regions_response.regions)
    }

    #[instrument(skip(self))]
    pub async fn list_registry_plans(&self) -> Result<Vec<VultrRegistryPlan>> {
        let url = format!("{}/registry/plan/list", self.base_url);
        let response = self.client.get(&url).await?;

        let plans_response: VultrRegistryPlanResponse = response.json().await?;
        Ok(plans_response.plans)
    }



    #[instrument(skip(self, request))]
    pub async fn create_dns_domain(&self, request: CreateDNSDomainRequest) -> Result<VultrDNSDomainDetailed> {
        let url = format!("{}/domains", self.base_url);
        let response = self.client.post(&url).json(&request).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to create DNS domain: {}", error_text));
        }

        let domain: VultrDNSDomainDetailed = response.json().await?;
        Ok(domain)
    }

    #[instrument(skip(self))]
    pub async fn get_dns_domain(&self, domain: &str) -> Result<VultrDNSDomainDetailed> {
        let url = format!("{}/domains/{}", self.base_url, domain);
        let response = self.client.get(&url).await?;

        let domain: VultrDNSDomainDetailed = response.json().await?;
        Ok(domain)
    }

    #[instrument(skip(self))]
    pub async fn delete_dns_domain(&self, domain: &str) -> Result<()> {
        let url = format!("{}/domains/{}", self.base_url, domain);
        let response = self.client.delete(&url).await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to delete DNS domain: {}", error_text));
        }

        Ok(())
    }

    #[instrument(skip(self, request))]
    pub async fn update_dns_domain(&self, domain: &str, request: UpdateDNSDomainRequest) -> Result<()> {
        let url = format!("{}/domains/{}", self.base_url, domain);
        let response = self.client.put(&url).json(&request).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to update DNS domain: {}", error_text));
        }

        Ok(())
    }

    #[instrument(skip(self))]
    pub async fn get_dns_domain_soa(&self, domain: &str) -> Result<VultrDNSSOA> {
        let url = format!("{}/domains/{}/soa", self.base_url, domain);
        let response = self.client.get(&url).await?;

        let soa_response: VultrDNSSOAResponse = response.json().await?;
        Ok(soa_response.dns_soa)
    }

    #[instrument(skip(self, request))]
    pub async fn update_dns_domain_soa(&self, domain: &str, request: UpdateDNSSOARequest) -> Result<()> {
        let url = format!("{}/domains/{}/soa", self.base_url, domain);
        let response = self.client.patch(&url).json(&request).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to update DNS SOA: {}", error_text));
        }

        Ok(())
    }

    #[instrument(skip(self))]
    pub async fn get_dns_domain_dnssec(&self, domain: &str) -> Result<Vec<String>> {
        let url = format!("{}/domains/{}/dnssec", self.base_url, domain);
        let response = self.client.get(&url).await?;

        let dnssec_response: VultrDNSSecResponse = response.json().await?;
        Ok(dnssec_response.dns_sec)
    }

    #[instrument(skip(self))]
    pub async fn list_dns_domain_records(&self, domain: &str) -> Result<Vec<VultrDNSRecordDetailed>> {
        let url = format!("{}/domains/{}/records", self.base_url, domain);
        let response = self.client.get(&url).await?;

        let records_response: VultrDNSRecordsResponse = response.json().await?;
        Ok(records_response.records)
    }

    #[instrument(skip(self, request))]
    pub async fn create_dns_domain_record(&self, domain: &str, request: CreateDNSRecordRequest) -> Result<VultrDNSRecordDetailed> {
        let url = format!("{}/domains/{}/records", self.base_url, domain);
        let response = self.client.post(&url).json(&request).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to create DNS record: {}", error_text));
        }

        let record: VultrDNSRecordDetailed = response.json().await?;
        Ok(record)
    }

    #[instrument(skip(self))]
    pub async fn get_dns_domain_record(&self, domain: &str, record_id: &str) -> Result<VultrDNSRecordDetailed> {
        let url = format!("{}/domains/{}/records/{}", self.base_url, domain, record_id);
        let response = self.client.get(&url).await?;

        let record: VultrDNSRecordDetailed = response.json().await?;
        Ok(record)
    }

    #[instrument(skip(self, request))]
    pub async fn update_dns_domain_record(&self, domain: &str, record_id: &str, request: UpdateDNSRecordRequest) -> Result<()> {
        let url = format!("{}/domains/{}/records/{}", self.base_url, domain, record_id);
        let response = self.client.patch(&url).json(&request).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to update DNS record: {}", error_text));
        }

        Ok(())
    }

    #[instrument(skip(self))]
    pub async fn delete_dns_domain_record(&self, domain: &str, record_id: &str) -> Result<()> {
        let url = format!("{}/domains/{}/records/{}", self.base_url, domain, record_id);
        let response = self.client.delete(&url).await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to delete DNS record: {}", error_text));
        }

        Ok(())
    }



    #[instrument(skip(self, request))]
    pub async fn create_firewall_group(&self, request: CreateFirewallGroupRequest) -> Result<VultrFirewallGroupDetailed> {
        let url = format!("{}/firewalls", self.base_url);
        let response = self.client.post(&url).json(&request).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to create firewall group: {}", error_text));
        }

        let firewall_group: VultrFirewallGroupDetailed = response.json().await?;
        Ok(firewall_group)
    }

    #[instrument(skip(self))]
    pub async fn get_firewall_group(&self, firewall_group_id: &str) -> Result<VultrFirewallGroupDetailed> {
        let url = format!("{}/firewalls/{}", self.base_url, firewall_group_id);
        let response = self.client.get(&url).await?;

        let firewall_group: VultrFirewallGroupDetailed = response.json().await?;
        Ok(firewall_group)
    }

    #[instrument(skip(self, request))]
    pub async fn update_firewall_group(&self, firewall_group_id: &str, request: UpdateFirewallGroupRequest) -> Result<()> {
        let url = format!("{}/firewalls/{}", self.base_url, firewall_group_id);
        let response = self.client.put(&url).json(&request).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to update firewall group: {}", error_text));
        }

        Ok(())
    }

    #[instrument(skip(self))]
    pub async fn delete_firewall_group(&self, firewall_group_id: &str) -> Result<()> {
        let url = format!("{}/firewalls/{}", self.base_url, firewall_group_id);
        let response = self.client.delete(&url).await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to delete firewall group: {}", error_text));
        }

        Ok(())
    }

    #[instrument(skip(self))]
    pub async fn list_firewall_group_rules(&self, firewall_group_id: &str) -> Result<Vec<VultrFirewallRuleDetailed>> {
        let url = format!("{}/firewalls/{}/rules", self.base_url, firewall_group_id);
        let response = self.client.get(&url).await?;

        let firewall_rules_response: VultrFirewallRulesResponse = response.json().await?;
        Ok(firewall_rules_response.firewall_rules)
    }

    #[instrument(skip(self, request))]
    pub async fn create_firewall_group_rule(&self, firewall_group_id: &str, request: CreateFirewallRuleRequest) -> Result<VultrFirewallRuleDetailed> {
        let url = format!("{}/firewalls/{}/rules", self.base_url, firewall_group_id);
        let response = self.client.post(&url).json(&request).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to create firewall rule: {}", error_text));
        }

        let firewall_rule: VultrFirewallRuleDetailed = response.json().await?;
        Ok(firewall_rule)
    }

    #[instrument(skip(self))]
    pub async fn get_firewall_group_rule(&self, firewall_group_id: &str, firewall_rule_id: &str) -> Result<VultrFirewallRuleDetailed> {
        let url = format!("{}/firewalls/{}/rules/{}", self.base_url, firewall_group_id, firewall_rule_id);
        let response = self.client.get(&url).await?;

        let firewall_rule: VultrFirewallRuleDetailed = response.json().await?;
        Ok(firewall_rule)
    }

    #[instrument(skip(self))]
    pub async fn delete_firewall_group_rule(&self, firewall_group_id: &str, firewall_rule_id: &str) -> Result<()> {
        let url = format!("{}/firewalls/{}/rules/{}", self.base_url, firewall_group_id, firewall_rule_id);
        let response = self.client.delete(&url).await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to delete firewall rule: {}", error_text));
        }

        Ok(())
    }

    // Enhanced Instance endpoints
    #[instrument(skip(self))]
    pub async fn list_instances_detailed(&self) -> Result<Vec<VultrInstanceDetailed>> {
        let url = format!("{}/instances", self.base_url);
        let response = self.client.get(&url).await?;

        let instances_response: VultrInstancesResponse = response.json().await?;
        Ok(instances_response.instances)
    }

    #[instrument(skip(self, request))]
    pub async fn create_instance_detailed(&self, request: CreateInstanceRequest) -> Result<VultrInstanceDetailed> {
        let url = format!("{}/instances", self.base_url);
        let response = self.client.post(&url).json(&request).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to create instance: {}", error_text));
        }

        let instance: VultrInstanceDetailed = response.json().await?;
        Ok(instance)
    }

    #[instrument(skip(self))]
    pub async fn get_instance_detailed(&self, instance_id: &str) -> Result<VultrInstanceDetailed> {
        let url = format!("{}/instances/{}", self.base_url, instance_id);
        let response = self.client.get(&url).await?;

        let instance: VultrInstanceDetailed = response.json().await?;
        Ok(instance)
    }

    #[instrument(skip(self, request))]
    pub async fn update_instance_detailed(&self, instance_id: &str, request: UpdateInstanceRequest) -> Result<VultrInstanceDetailed> {
        let url = format!("{}/instances/{}", self.base_url, instance_id);
        let response = self.client.patch(&url).json(&request).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to update instance: {}", error_text));
        }

        let instance: VultrInstanceDetailed = response.json().await?;
        Ok(instance)
    }

    #[instrument(skip(self))]
    pub async fn delete_instance_detailed(&self, instance_id: &str) -> Result<()> {
        let url = format!("{}/instances/{}", self.base_url, instance_id);
        let response = self.client.delete(&url).await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to delete instance: {}", error_text));
        }

        Ok(())
    }

    #[instrument(skip(self))]
    pub async fn halt_instance(&self, instance_id: &str) -> Result<()> {
        let url = format!("{}/instances/{}/halt", self.base_url, instance_id);
        let response = self.client.post(&url).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to halt instance: {}", error_text));
        }

        Ok(())
    }

    #[instrument(skip(self, request))]
    pub async fn halt_instances(&self, request: BulkInstanceActionRequest) -> Result<()> {
        let url = format!("{}/instances/halt", self.base_url);
        let response = self.client.post(&url).json(&request).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to halt instances: {}", error_text));
        }

        Ok(())
    }

    #[instrument(skip(self))]
    pub async fn reboot_instance(&self, instance_id: &str) -> Result<()> {
        let url = format!("{}/instances/{}/reboot", self.base_url, instance_id);
        let response = self.client.post(&url).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to reboot instance: {}", error_text));
        }

        Ok(())
    }

    #[instrument(skip(self, request))]
    pub async fn reboot_instances(&self, request: BulkInstanceActionRequest) -> Result<()> {
        let url = format!("{}/instances/reboot", self.base_url);
        let response = self.client.post(&url).json(&request).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to reboot instances: {}", error_text));
        }

        Ok(())
    }



    #[instrument(skip(self, request))]
    pub async fn start_instances(&self, request: BulkInstanceActionRequest) -> Result<()> {
        let url = format!("{}/instances/start", self.base_url);
        let response = self.client.post(&url).json(&request).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to start instances: {}", error_text));
        }

        Ok(())
    }

    #[instrument(skip(self, request))]
    pub async fn reinstall_instance(&self, instance_id: &str, request: Option<serde_json::Value>) -> Result<VultrInstanceDetailed> {
        let url = format!("{}/instances/{}/reinstall", self.base_url, instance_id);
        let response = if let Some(req) = request {
            self.client.post(&url).json(&req).send().await?
        } else {
            self.client.post(&url).send().await?
        };

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to reinstall instance: {}", error_text));
        }

        let instance: VultrInstanceDetailed = response.json().await?;
        Ok(instance)
    }

    #[instrument(skip(self))]
    pub async fn get_instance_bandwidth(&self, instance_id: &str) -> Result<HashMap<String, InstanceBandwidth>> {
        let url = format!("{}/instances/{}/bandwidth", self.base_url, instance_id);
        let response = self.client.get(&url).await?;

        let bandwidth_response: InstanceBandwidthResponse = response.json().await?;
        Ok(bandwidth_response.bandwidth)
    }

    #[instrument(skip(self))]
    pub async fn get_instance_neighbors(&self, instance_id: &str) -> Result<Vec<InstanceNeighbor>> {
        let url = format!("{}/instances/{}/neighbors", self.base_url, instance_id);
        let response = self.client.get(&url).await?;

        let neighbors_response: InstanceNeighborsResponse = response.json().await?;
        Ok(neighbors_response.neighbors)
    }

    #[instrument(skip(self))]
    pub async fn list_instance_vpcs(&self, instance_id: &str) -> Result<Vec<InstanceVPC>> {
        let url = format!("{}/instances/{}/vpcs", self.base_url, instance_id);
        let response = self.client.get(&url).await?;

        let vpcs_response: InstanceVPCsResponse = response.json().await?;
        Ok(vpcs_response.vpcs)
    }

    #[instrument(skip(self))]
    pub async fn get_instance_iso_status(&self, instance_id: &str) -> Result<InstanceISOStatus> {
        let url = format!("{}/instances/{}/iso", self.base_url, instance_id);
        let response = self.client.get(&url).await?;

        let iso_status: InstanceISOStatus = response.json().await?;
        Ok(iso_status)
    }

    #[instrument(skip(self, request))]
    pub async fn attach_instance_iso(&self, instance_id: &str, request: AttachISORequest) -> Result<()> {
        let url = format!("{}/instances/{}/iso/attach", self.base_url, instance_id);
        let response = self.client.post(&url).json(&request).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to attach ISO: {}", error_text));
        }

        Ok(())
    }

    #[instrument(skip(self))]
    pub async fn detach_instance_iso(&self, instance_id: &str) -> Result<()> {
        let url = format!("{}/instances/{}/iso/detach", self.base_url, instance_id);
        let response = self.client.post(&url).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to detach ISO: {}", error_text));
        }

        Ok(())
    }

    #[instrument(skip(self, request))]
    pub async fn attach_instance_vpc(&self, instance_id: &str, request: AttachVPCRequest) -> Result<()> {
        let url = format!("{}/instances/{}/vpcs/attach", self.base_url, instance_id);
        let response = self.client.post(&url).json(&request).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to attach VPC: {}", error_text));
        }

        Ok(())
    }

    #[instrument(skip(self, request))]
    pub async fn detach_instance_vpc(&self, instance_id: &str, request: DetachVPCRequest) -> Result<()> {
        let url = format!("{}/instances/{}/vpcs/detach", self.base_url, instance_id);
        let response = self.client.post(&url).json(&request).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to detach VPC: {}", error_text));
        }

        Ok(())
    }

    #[instrument(skip(self, request))]
    pub async fn create_instance_backup_schedule(&self, instance_id: &str, request: CreateBackupScheduleRequest) -> Result<()> {
        let url = format!("{}/instances/{}/backup-schedule", self.base_url, instance_id);
        let response = self.client.post(&url).json(&request).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to create backup schedule: {}", error_text));
        }

        Ok(())
    }

    #[instrument(skip(self))]
    pub async fn get_instance_backup_schedule(&self, instance_id: &str) -> Result<InstanceBackupSchedule> {
        let url = format!("{}/instances/{}/backup-schedule", self.base_url, instance_id);
        let response = self.client.get(&url).await?;

        let backup_schedule: InstanceBackupSchedule = response.json().await?;
        Ok(backup_schedule)
    }

    #[instrument(skip(self, request))]
    pub async fn restore_instance(&self, instance_id: &str, request: RestoreInstanceRequest) -> Result<()> {
        let url = format!("{}/instances/{}/restore", self.base_url, instance_id);
        let response = self.client.post(&url).json(&request).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to restore instance: {}", error_text));
        }

        Ok(())
    }

    #[instrument(skip(self))]
    pub async fn get_instance_ipv4(&self, instance_id: &str) -> Result<Vec<InstanceIPv4>> {
        let url = format!("{}/instances/{}/ipv4", self.base_url, instance_id);
        let response = self.client.get(&url).await?;

        let ipv4_response: InstanceIPv4Response = response.json().await?;
        Ok(ipv4_response.ipv4s)
    }

    #[instrument(skip(self, request))]
    pub async fn create_instance_ipv4(&self, instance_id: &str, request: CreateInstanceIPv4Request) -> Result<InstanceIPv4> {
        let url = format!("{}/instances/{}/ipv4", self.base_url, instance_id);
        let response = self.client.post(&url).json(&request).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to create IPv4: {}", error_text));
        }

        let ipv4: InstanceIPv4 = response.json().await?;
        Ok(ipv4)
    }

    #[instrument(skip(self))]
    pub async fn delete_instance_ipv4(&self, instance_id: &str, ipv4: &str) -> Result<()> {
        let url = format!("{}/instances/{}/ipv4/{}", self.base_url, instance_id, ipv4);
        let response = self.client.delete(&url).await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to delete IPv4: {}", error_text));
        }

        Ok(())
    }

    #[instrument(skip(self))]
    pub async fn get_instance_ipv6(&self, instance_id: &str) -> Result<Vec<InstanceIPv6>> {
        let url = format!("{}/instances/{}/ipv6", self.base_url, instance_id);
        let response = self.client.get(&url).await?;

        let ipv6_response: InstanceIPv6Response = response.json().await?;
        Ok(ipv6_response.ipv6s)
    }

    #[instrument(skip(self, request))]
    pub async fn create_instance_reverse_ipv6(&self, instance_id: &str, request: CreateReverseIPv6Request) -> Result<()> {
        let url = format!("{}/instances/{}/ipv6/reverse", self.base_url, instance_id);
        let response = self.client.post(&url).json(&request).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to create reverse IPv6: {}", error_text));
        }

        Ok(())
    }

    #[instrument(skip(self))]
    pub async fn list_instance_ipv6_reverse(&self, instance_id: &str) -> Result<Vec<InstanceIPv6Reverse>> {
        let url = format!("{}/instances/{}/ipv6/reverse", self.base_url, instance_id);
        let response = self.client.get(&url).await?;

        let reverse_response: InstanceIPv6ReverseResponse = response.json().await?;
        Ok(reverse_response.reverse_ipv6s)
    }

    #[instrument(skip(self))]
    pub async fn delete_instance_reverse_ipv6(&self, instance_id: &str, ipv6: &str) -> Result<()> {
        let url = format!("{}/instances/{}/ipv6/reverse/{}", self.base_url, instance_id, ipv6);
        let response = self.client.delete(&url).await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to delete reverse IPv6: {}", error_text));
        }

        Ok(())
    }

    #[instrument(skip(self, request))]
    pub async fn create_instance_reverse_ipv4(&self, instance_id: &str, request: CreateReverseIPv4Request) -> Result<()> {
        let url = format!("{}/instances/{}/ipv4/reverse", self.base_url, instance_id);
        let response = self.client.post(&url).json(&request).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to create reverse IPv4: {}", error_text));
        }

        Ok(())
    }

    #[instrument(skip(self))]
    pub async fn set_instance_default_reverse_ipv4(&self, instance_id: &str, ip: &str) -> Result<()> {
        let url = format!("{}/instances/{}/ipv4/reverse/default", self.base_url, instance_id);
        let request = serde_json::json!({ "ip": ip });
        let response = self.client.post(&url).json(&request).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to set default reverse IPv4: {}", error_text));
        }

        Ok(())
    }

    #[instrument(skip(self))]
    pub async fn get_instance_userdata(&self, instance_id: &str) -> Result<InstanceUserData> {
        let url = format!("{}/instances/{}/user-data", self.base_url, instance_id);
        let response = self.client.get(&url).await?;

        let userdata: InstanceUserData = response.json().await?;
        Ok(userdata)
    }

    #[instrument(skip(self))]
    pub async fn get_instance_upgrades(&self, instance_id: &str) -> Result<InstanceUpgrades> {
        let url = format!("{}/instances/{}/upgrades", self.base_url, instance_id);
        let response = self.client.get(&url).await?;

        let upgrades: InstanceUpgrades = response.json().await?;
        Ok(upgrades)
    }

    #[instrument(skip(self))]
    pub async fn get_instance_job(&self, instance_id: &str, job_id: &str) -> Result<InstanceJob> {
        let url = format!("{}/instances/{}/jobs/{}", self.base_url, instance_id, job_id);
        let response = self.client.get(&url).await?;

        let job: InstanceJob = response.json().await?;
        Ok(job)
    }



    #[instrument(skip(self))]
    pub async fn delete_iso(&self, iso_id: &str) -> Result<()> {
        let url = format!("{}/iso/{}", self.base_url, iso_id);
        let response = self.client.delete(&url).await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to delete ISO: {}", error_text));
        }

        Ok(())
    }

    #[instrument(skip(self))]
    pub async fn list_public_isos(&self) -> Result<Vec<VultrPublicISO>> {
        let url = format!("{}/iso-public", self.base_url);
        let response = self.client.get(&url).await?;

        let public_isos_response: VultrPublicISOsResponse = response.json().await?;
        Ok(public_isos_response.public_isos)
    }

    // Enhanced Kubernetes endpoints
    #[instrument(skip(self, request))]
    pub async fn create_kubernetes_cluster(&self, request: CreateKubernetesClusterRequest) -> Result<VultrKubernetesClusterDetailed> {
        let url = format!("{}/kubernetes/clusters", self.base_url);
        let response = self.client.post(&url).json(&request).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to create Kubernetes cluster: {}", error_text));
        }

        let cluster: VultrKubernetesClusterDetailed = response.json().await?;
        Ok(cluster)
    }



    #[instrument(skip(self, request))]
    pub async fn update_kubernetes_cluster(&self, vke_id: &str, request: UpdateKubernetesClusterRequest) -> Result<VultrKubernetesClusterDetailed> {
        let url = format!("{}/kubernetes/clusters/{}", self.base_url, vke_id);
        let response = self.client.put(&url).json(&request).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to update Kubernetes cluster: {}", error_text));
        }

        let cluster: VultrKubernetesClusterDetailed = response.json().await?;
        Ok(cluster)
    }

    #[instrument(skip(self))]
    pub async fn delete_kubernetes_cluster(&self, vke_id: &str) -> Result<()> {
        let url = format!("{}/kubernetes/clusters/{}", self.base_url, vke_id);
        let response = self.client.delete(&url).await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to delete Kubernetes cluster: {}", error_text));
        }

        Ok(())
    }

    #[instrument(skip(self))]
    pub async fn delete_kubernetes_cluster_with_resources(&self, vke_id: &str) -> Result<()> {
        let url = format!("{}/kubernetes/clusters/{}/delete-with-linked-resources", self.base_url, vke_id);
        let response = self.client.delete(&url).await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to delete Kubernetes cluster with resources: {}", error_text));
        }

        Ok(())
    }

    #[instrument(skip(self))]
    pub async fn get_kubernetes_resources(&self, vke_id: &str) -> Result<VultrKubernetesResources> {
        let url = format!("{}/kubernetes/clusters/{}/resources", self.base_url, vke_id);
        let response = self.client.get(&url).await?;

        let resources: VultrKubernetesResources = response.json().await?;
        Ok(resources)
    }

    #[instrument(skip(self))]
    pub async fn get_kubernetes_available_upgrades(&self, vke_id: &str) -> Result<VultrKubernetesUpgrades> {
        let url = format!("{}/kubernetes/clusters/{}/available-upgrades", self.base_url, vke_id);
        let response = self.client.get(&url).await?;

        let upgrades: VultrKubernetesUpgrades = response.json().await?;
        Ok(upgrades)
    }

    #[instrument(skip(self, request))]
    pub async fn start_kubernetes_cluster_upgrade(&self, vke_id: &str, request: StartKubernetesUpgradeRequest) -> Result<()> {
        let url = format!("{}/kubernetes/clusters/{}/upgrades", self.base_url, vke_id);
        let response = self.client.post(&url).json(&request).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to start Kubernetes upgrade: {}", error_text));
        }

        Ok(())
    }

    #[instrument(skip(self, request))]
    pub async fn create_nodepool(&self, vke_id: &str, request: CreateNodePoolRequest) -> Result<VultrNodePoolDetailed> {
        let url = format!("{}/kubernetes/clusters/{}/node-pools", self.base_url, vke_id);
        let response = self.client.post(&url).json(&request).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to create node pool: {}", error_text));
        }

        let nodepool: VultrNodePoolDetailed = response.json().await?;
        Ok(nodepool)
    }

    #[instrument(skip(self))]
    pub async fn get_nodepools(&self, vke_id: &str) -> Result<Vec<VultrNodePoolDetailed>> {
        let url = format!("{}/kubernetes/clusters/{}/node-pools", self.base_url, vke_id);
        let response = self.client.get(&url).await?;

        let nodepools_response: VultrNodePoolsResponse = response.json().await?;
        Ok(nodepools_response.node_pools)
    }

    #[instrument(skip(self))]
    pub async fn get_nodepool(&self, vke_id: &str, nodepool_id: &str) -> Result<VultrNodePoolDetailed> {
        let url = format!("{}/kubernetes/clusters/{}/node-pools/{}", self.base_url, vke_id, nodepool_id);
        let response = self.client.get(&url).await?;

        let nodepool: VultrNodePoolDetailed = response.json().await?;
        Ok(nodepool)
    }

    #[instrument(skip(self, request))]
    pub async fn update_nodepool(&self, vke_id: &str, nodepool_id: &str, request: UpdateNodePoolRequest) -> Result<VultrNodePoolDetailed> {
        let url = format!("{}/kubernetes/clusters/{}/node-pools/{}", self.base_url, vke_id, nodepool_id);
        let response = self.client.patch(&url).json(&request).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to update node pool: {}", error_text));
        }

        let nodepool: VultrNodePoolDetailed = response.json().await?;
        Ok(nodepool)
    }

    #[instrument(skip(self))]
    pub async fn delete_nodepool(&self, vke_id: &str, nodepool_id: &str) -> Result<()> {
        let url = format!("{}/kubernetes/clusters/{}/node-pools/{}", self.base_url, vke_id, nodepool_id);
        let response = self.client.delete(&url).await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to delete node pool: {}", error_text));
        }

        Ok(())
    }

    #[instrument(skip(self))]
    pub async fn delete_nodepool_instance(&self, vke_id: &str, nodepool_id: &str, node_id: &str) -> Result<()> {
        let url = format!("{}/kubernetes/clusters/{}/node-pools/{}/nodes/{}", self.base_url, vke_id, nodepool_id, node_id);
        let response = self.client.delete(&url).await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to delete node pool instance: {}", error_text));
        }

        Ok(())
    }

    #[instrument(skip(self))]
    pub async fn recycle_nodepool_instance(&self, vke_id: &str, nodepool_id: &str, node_id: &str) -> Result<()> {
        let url = format!("{}/kubernetes/clusters/{}/node-pools/{}/nodes/{}/recycle", self.base_url, vke_id, nodepool_id, node_id);
        let response = self.client.post(&url).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to recycle node pool instance: {}", error_text));
        }

        Ok(())
    }

    #[instrument(skip(self))]
    pub async fn get_kubernetes_cluster_config(&self, vke_id: &str) -> Result<VultrKubernetesConfig> {
        let url = format!("{}/kubernetes/clusters/{}/config", self.base_url, vke_id);
        let response = self.client.get(&url).await?;

        let config: VultrKubernetesConfig = response.json().await?;
        Ok(config)
    }

    #[instrument(skip(self))]
    pub async fn get_kubernetes_versions(&self) -> Result<VultrKubernetesVersions> {
        let url = format!("{}/kubernetes/versions", self.base_url);
        let response = self.client.get(&url).await?;

        let versions: VultrKubernetesVersions = response.json().await?;
        Ok(versions)
    }



    #[instrument(skip(self, request))]
    pub async fn create_load_balancer(&self, request: CreateLoadBalancerRequest) -> Result<VultrLoadBalancerDetailed> {
        let url = format!("{}/load-balancers", self.base_url);
        let response = self.client.post(&url).json(&request).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to create load balancer: {}", error_text));
        }

        let load_balancer: VultrLoadBalancerDetailed = response.json().await?;
        Ok(load_balancer)
    }



    #[instrument(skip(self, request))]
    pub async fn update_load_balancer(&self, load_balancer_id: &str, request: UpdateLoadBalancerRequest) -> Result<()> {
        let url = format!("{}/load-balancers/{}", self.base_url, load_balancer_id);
        let response = self.client.patch(&url).json(&request).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to update load balancer: {}", error_text));
        }

        Ok(())
    }

    #[instrument(skip(self))]
    pub async fn delete_load_balancer(&self, load_balancer_id: &str) -> Result<()> {
        let url = format!("{}/load-balancers/{}", self.base_url, load_balancer_id);
        let response = self.client.delete(&url).await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to delete load balancer: {}", error_text));
        }

        Ok(())
    }

    #[instrument(skip(self))]
    pub async fn delete_load_balancer_ssl(&self, load_balancer_id: &str) -> Result<()> {
        let url = format!("{}/load-balancers/{}/ssl", self.base_url, load_balancer_id);
        let response = self.client.delete(&url).await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to delete load balancer SSL: {}", error_text));
        }

        Ok(())
    }

    #[instrument(skip(self))]
    pub async fn delete_load_balancer_auto_ssl(&self, load_balancer_id: &str) -> Result<()> {
        let url = format!("{}/load-balancers/{}/ssl/auto", self.base_url, load_balancer_id);
        let response = self.client.delete(&url).await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to delete load balancer auto SSL: {}", error_text));
        }

        Ok(())
    }

    #[instrument(skip(self, request))]
    pub async fn create_load_balancer_forwarding_rule(&self, load_balancer_id: &str, request: CreateForwardingRuleRequest) -> Result<VultrForwardingRuleDetailed> {
        let url = format!("{}/load-balancers/{}/forwarding-rules", self.base_url, load_balancer_id);
        let response = self.client.post(&url).json(&request).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to create forwarding rule: {}", error_text));
        }

        let forwarding_rule: VultrForwardingRuleDetailed = response.json().await?;
        Ok(forwarding_rule)
    }

    #[instrument(skip(self))]
    pub async fn get_load_balancer_forwarding_rule(&self, load_balancer_id: &str, forwarding_rule_id: &str) -> Result<VultrForwardingRuleDetailed> {
        let url = format!("{}/load-balancers/{}/forwarding-rules/{}", self.base_url, load_balancer_id, forwarding_rule_id);
        let response = self.client.get(&url).await?;

        let forwarding_rule: VultrForwardingRuleDetailed = response.json().await?;
        Ok(forwarding_rule)
    }

    #[instrument(skip(self))]
    pub async fn delete_load_balancer_forwarding_rule(&self, load_balancer_id: &str, forwarding_rule_id: &str) -> Result<()> {
        let url = format!("{}/load-balancers/{}/forwarding-rules/{}", self.base_url, load_balancer_id, forwarding_rule_id);
        let response = self.client.delete(&url).await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to delete forwarding rule: {}", error_text));
        }

        Ok(())
    }

    #[instrument(skip(self))]
    pub async fn list_load_balancer_firewall_rules(&self, load_balancer_id: &str) -> Result<Vec<VultrLoadBalancerFirewallRule>> {
        let url = format!("{}/load-balancers/{}/firewall-rules", self.base_url, load_balancer_id);
        let response = self.client.get(&url).await?;

        let firewall_rules_response: VultrLoadBalancerFirewallRulesResponse = response.json().await?;
        Ok(firewall_rules_response.firewall_rules)
    }

    #[instrument(skip(self))]
    pub async fn get_load_balancer_firewall_rule(&self, load_balancer_id: &str, firewall_rule_id: &str) -> Result<VultrLoadBalancerFirewallRule> {
        let url = format!("{}/load-balancers/{}/firewall-rules/{}", self.base_url, load_balancer_id, firewall_rule_id);
        let response = self.client.get(&url).await?;

        let firewall_rule: VultrLoadBalancerFirewallRule = response.json().await?;
        Ok(firewall_rule)
    }

    // Managed Database endpoints
    #[instrument(skip(self))]
    pub async fn list_database_plans(&self) -> Result<Vec<VultrDatabasePlan>> {
        let url = format!("{}/databases/plans", self.base_url);
        let response = self.client.get(&url).await?;

        let plans_response: VultrDatabasePlansResponse = response.json().await?;
        Ok(plans_response.plans)
    }

    #[instrument(skip(self))]
    pub async fn list_managed_databases(&self) -> Result<Vec<VultrManagedDatabase>> {
        let url = format!("{}/databases", self.base_url);
        let response = self.client.get(&url).await?;

        let databases_response: VultrManagedDatabasesResponse = response.json().await?;
        Ok(databases_response.databases)
    }

    #[instrument(skip(self, request))]
    pub async fn create_managed_database(&self, request: CreateManagedDatabaseRequest) -> Result<VultrManagedDatabase> {
        let url = format!("{}/databases", self.base_url);
        let response = self.client.post(&url).json(&request).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to create managed database: {}", error_text));
        }

        let database: VultrManagedDatabase = response.json().await?;
        Ok(database)
    }

    #[instrument(skip(self))]
    pub async fn get_managed_database(&self, database_id: &str) -> Result<VultrManagedDatabase> {
        let url = format!("{}/databases/{}", self.base_url, database_id);
        let response = self.client.get(&url).await?;

        let database: VultrManagedDatabase = response.json().await?;
        Ok(database)
    }

    #[instrument(skip(self, request))]
    pub async fn update_managed_database(&self, database_id: &str, request: UpdateManagedDatabaseRequest) -> Result<VultrManagedDatabase> {
        let url = format!("{}/databases/{}", self.base_url, database_id);
        let response = self.client.put(&url).json(&request).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to update managed database: {}", error_text));
        }

        let database: VultrManagedDatabase = response.json().await?;
        Ok(database)
    }

    #[instrument(skip(self))]
    pub async fn delete_managed_database(&self, database_id: &str) -> Result<()> {
        let url = format!("{}/databases/{}", self.base_url, database_id);
        let response = self.client.delete(&url).await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to delete managed database: {}", error_text));
        }

        Ok(())
    }

    #[instrument(skip(self))]
    pub async fn get_database_usage(&self, database_id: &str) -> Result<VultrDatabaseUsage> {
        let url = format!("{}/databases/{}/usage", self.base_url, database_id);
        let response = self.client.get(&url).await?;

        let usage: VultrDatabaseUsage = response.json().await?;
        Ok(usage)
    }

    // Database User endpoints
    #[instrument(skip(self))]
    pub async fn list_database_users(&self, database_id: &str) -> Result<Vec<VultrDatabaseUser>> {
        let url = format!("{}/databases/{}/users", self.base_url, database_id);
        let response = self.client.get(&url).await?;

        let users_response: VultrDatabaseUsersResponse = response.json().await?;
        Ok(users_response.users)
    }

    #[instrument(skip(self, request))]
    pub async fn create_database_user(&self, database_id: &str, request: CreateDatabaseUserRequest) -> Result<VultrDatabaseUser> {
        let url = format!("{}/databases/{}/users", self.base_url, database_id);
        let response = self.client.post(&url).json(&request).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to create database user: {}", error_text));
        }

        let user: VultrDatabaseUser = response.json().await?;
        Ok(user)
    }

    #[instrument(skip(self))]
    pub async fn get_database_user(&self, database_id: &str, username: &str) -> Result<VultrDatabaseUser> {
        let url = format!("{}/databases/{}/users/{}", self.base_url, database_id, username);
        let response = self.client.get(&url).await?;

        let user: VultrDatabaseUser = response.json().await?;
        Ok(user)
    }

    #[instrument(skip(self, request))]
    pub async fn update_database_user(&self, database_id: &str, username: &str, request: UpdateDatabaseUserRequest) -> Result<VultrDatabaseUser> {
        let url = format!("{}/databases/{}/users/{}", self.base_url, database_id, username);
        let response = self.client.put(&url).json(&request).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to update database user: {}", error_text));
        }

        let user: VultrDatabaseUser = response.json().await?;
        Ok(user)
    }

    #[instrument(skip(self))]
    pub async fn delete_database_user(&self, database_id: &str, username: &str) -> Result<()> {
        let url = format!("{}/databases/{}/users/{}", self.base_url, database_id, username);
        let response = self.client.delete(&url).await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to delete database user: {}", error_text));
        }

        Ok(())
    }

    #[instrument(skip(self, request))]
    pub async fn set_database_user_acl(&self, database_id: &str, username: &str, request: SetDatabaseUserACLRequest) -> Result<()> {
        let url = format!("{}/databases/{}/users/{}/access-control", self.base_url, database_id, username);
        let response = self.client.put(&url).json(&request).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to set database user ACL: {}", error_text));
        }

        Ok(())
    }

    // Database DB endpoints
    #[instrument(skip(self))]
    pub async fn list_database_dbs(&self, database_id: &str) -> Result<Vec<VultrDatabaseDB>> {
        let url = format!("{}/databases/{}/dbs", self.base_url, database_id);
        let response = self.client.get(&url).await?;

        let dbs_response: VultrDatabaseDBsResponse = response.json().await?;
        Ok(dbs_response.dbs)
    }

    #[instrument(skip(self, request))]
    pub async fn create_database_db(&self, database_id: &str, request: CreateDatabaseDBRequest) -> Result<VultrDatabaseDB> {
        let url = format!("{}/databases/{}/dbs", self.base_url, database_id);
        let response = self.client.post(&url).json(&request).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to create database DB: {}", error_text));
        }

        let db: VultrDatabaseDB = response.json().await?;
        Ok(db)
    }

    #[instrument(skip(self))]
    pub async fn get_database_db(&self, database_id: &str, db_name: &str) -> Result<VultrDatabaseDB> {
        let url = format!("{}/databases/{}/dbs/{}", self.base_url, database_id, db_name);
        let response = self.client.get(&url).await?;

        let db: VultrDatabaseDB = response.json().await?;
        Ok(db)
    }

    #[instrument(skip(self))]
    pub async fn delete_database_db(&self, database_id: &str, db_name: &str) -> Result<()> {
        let url = format!("{}/databases/{}/dbs/{}", self.base_url, database_id, db_name);
        let response = self.client.delete(&url).await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to delete database DB: {}", error_text));
        }

        Ok(())
    }

    // Database Topic endpoints (Kafka)
    #[instrument(skip(self))]
    pub async fn list_database_topics(&self, database_id: &str) -> Result<Vec<VultrDatabaseTopic>> {
        let url = format!("{}/databases/{}/topics", self.base_url, database_id);
        let response = self.client.get(&url).await?;

        let topics_response: VultrDatabaseTopicsResponse = response.json().await?;
        Ok(topics_response.topics)
    }

    #[instrument(skip(self, request))]
    pub async fn create_database_topic(&self, database_id: &str, request: CreateDatabaseTopicRequest) -> Result<VultrDatabaseTopic> {
        let url = format!("{}/databases/{}/topics", self.base_url, database_id);
        let response = self.client.post(&url).json(&request).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to create database topic: {}", error_text));
        }

        let topic: VultrDatabaseTopic = response.json().await?;
        Ok(topic)
    }

    #[instrument(skip(self))]
    pub async fn get_database_topic(&self, database_id: &str, topic_name: &str) -> Result<VultrDatabaseTopic> {
        let url = format!("{}/databases/{}/topics/{}", self.base_url, database_id, topic_name);
        let response = self.client.get(&url).await?;

        let topic: VultrDatabaseTopic = response.json().await?;
        Ok(topic)
    }

    #[instrument(skip(self, request))]
    pub async fn update_database_topic(&self, database_id: &str, topic_name: &str, request: UpdateDatabaseTopicRequest) -> Result<VultrDatabaseTopic> {
        let url = format!("{}/databases/{}/topics/{}", self.base_url, database_id, topic_name);
        let response = self.client.put(&url).json(&request).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to update database topic: {}", error_text));
        }

        let topic: VultrDatabaseTopic = response.json().await?;
        Ok(topic)
    }

    #[instrument(skip(self))]
    pub async fn delete_database_topic(&self, database_id: &str, topic_name: &str) -> Result<()> {
        let url = format!("{}/databases/{}/topics/{}", self.base_url, database_id, topic_name);
        let response = self.client.delete(&url).await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to delete database topic: {}", error_text));
        }

        Ok(())
    }

    // Database Quota endpoints (Kafka)
    #[instrument(skip(self))]
    pub async fn list_database_quotas(&self, database_id: &str) -> Result<Vec<VultrDatabaseQuota>> {
        let url = format!("{}/databases/{}/quotas", self.base_url, database_id);
        let response = self.client.get(&url).await?;

        let quotas_response: VultrDatabaseQuotasResponse = response.json().await?;
        Ok(quotas_response.quotas)
    }

    #[instrument(skip(self, request))]
    pub async fn create_database_quota(&self, database_id: &str, request: CreateDatabaseQuotaRequest) -> Result<VultrDatabaseQuota> {
        let url = format!("{}/databases/{}/quotas", self.base_url, database_id);
        let response = self.client.post(&url).json(&request).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to create database quota: {}", error_text));
        }

        let quota: VultrDatabaseQuota = response.json().await?;
        Ok(quota)
    }

    #[instrument(skip(self))]
    pub async fn get_database_quota(&self, database_id: &str, client_id: &str, user: &str) -> Result<VultrDatabaseQuota> {
        let url = format!("{}/databases/{}/quotas?client_id={}&user={}", self.base_url, database_id, client_id, user);
        let response = self.client.get(&url).await?;

        let quota: VultrDatabaseQuota = response.json().await?;
        Ok(quota)
    }

    #[instrument(skip(self))]
    pub async fn delete_database_quota(&self, database_id: &str, client_id: &str, user: &str) -> Result<()> {
        let url = format!("{}/databases/{}/quotas?client_id={}&user={}", self.base_url, database_id, client_id, user);
        let response = self.client.delete(&url).await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to delete database quota: {}", error_text));
        }

        Ok(())
    }

    // Database Connector endpoints (Kafka)
    #[instrument(skip(self))]
    pub async fn list_database_available_connectors(&self, database_id: &str) -> Result<Vec<String>> {
        let url = format!("{}/databases/{}/connectors/available", self.base_url, database_id);
        let response = self.client.get(&url).await?;

        let connectors: Vec<String> = response.json().await?;
        Ok(connectors)
    }

    #[instrument(skip(self))]
    pub async fn get_database_connector_configuration_schema(&self, database_id: &str, connector_name: &str) -> Result<serde_json::Value> {
        let url = format!("{}/databases/{}/connectors/available/{}/configuration", self.base_url, database_id, connector_name);
        let response = self.client.get(&url).await?;

        let schema: serde_json::Value = response.json().await?;
        Ok(schema)
    }

    #[instrument(skip(self))]
    pub async fn list_database_connectors(&self, database_id: &str) -> Result<Vec<VultrDatabaseConnector>> {
        let url = format!("{}/databases/{}/connectors", self.base_url, database_id);
        let response = self.client.get(&url).await?;

        let connectors_response: VultrDatabaseConnectorsResponse = response.json().await?;
        Ok(connectors_response.connectors)
    }

    #[instrument(skip(self, request))]
    pub async fn create_database_connector(&self, database_id: &str, request: CreateDatabaseConnectorRequest) -> Result<VultrDatabaseConnector> {
        let url = format!("{}/databases/{}/connectors", self.base_url, database_id);
        let response = self.client.post(&url).json(&request).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to create database connector: {}", error_text));
        }

        let connector: VultrDatabaseConnector = response.json().await?;
        Ok(connector)
    }

    #[instrument(skip(self))]
    pub async fn get_database_connector(&self, database_id: &str, connector_name: &str) -> Result<VultrDatabaseConnector> {
        let url = format!("{}/databases/{}/connectors/{}", self.base_url, database_id, connector_name);
        let response = self.client.get(&url).await?;

        let connector: VultrDatabaseConnector = response.json().await?;
        Ok(connector)
    }

    #[instrument(skip(self, request))]
    pub async fn update_database_connector(&self, database_id: &str, connector_name: &str, request: UpdateDatabaseConnectorRequest) -> Result<VultrDatabaseConnector> {
        let url = format!("{}/databases/{}/connectors/{}", self.base_url, database_id, connector_name);
        let response = self.client.put(&url).json(&request).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to update database connector: {}", error_text));
        }

        let connector: VultrDatabaseConnector = response.json().await?;
        Ok(connector)
    }

    #[instrument(skip(self))]
    pub async fn delete_database_connector(&self, database_id: &str, connector_name: &str) -> Result<()> {
        let url = format!("{}/databases/{}/connectors/{}", self.base_url, database_id, connector_name);
        let response = self.client.delete(&url).await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to delete database connector: {}", error_text));
        }

        Ok(())
    }

    #[instrument(skip(self))]
    pub async fn get_database_connector_status(&self, database_id: &str, connector_name: &str) -> Result<VultrDatabaseConnectorStatus> {
        let url = format!("{}/databases/{}/connectors/{}/status", self.base_url, database_id, connector_name);
        let response = self.client.get(&url).await?;

        let status: VultrDatabaseConnectorStatus = response.json().await?;
        Ok(status)
    }

    #[instrument(skip(self))]
    pub async fn restart_database_connector(&self, database_id: &str, connector_name: &str) -> Result<()> {
        let url = format!("{}/databases/{}/connectors/{}/restart", self.base_url, database_id, connector_name);
        let response = self.client.post(&url).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to restart database connector: {}", error_text));
        }

        Ok(())
    }

    #[instrument(skip(self))]
    pub async fn pause_database_connector(&self, database_id: &str, connector_name: &str) -> Result<()> {
        let url = format!("{}/databases/{}/connectors/{}/pause", self.base_url, database_id, connector_name);
        let response = self.client.put(&url).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to pause database connector: {}", error_text));
        }

        Ok(())
    }

    #[instrument(skip(self))]
    pub async fn resume_database_connector(&self, database_id: &str, connector_name: &str) -> Result<()> {
        let url = format!("{}/databases/{}/connectors/{}/resume", self.base_url, database_id, connector_name);
        let response = self.client.put(&url).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to resume database connector: {}", error_text));
        }

        Ok(())
    }

    #[instrument(skip(self))]
    pub async fn restart_database_connector_task(&self, database_id: &str, connector_name: &str, task_id: u32) -> Result<()> {
        let url = format!("{}/databases/{}/connectors/{}/tasks/{}/restart", self.base_url, database_id, connector_name, task_id);
        let response = self.client.post(&url).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to restart database connector task: {}", error_text));
        }

        Ok(())
    }

    // Database Maintenance and Migration endpoints
    #[instrument(skip(self))]
    pub async fn list_database_maintenance_updates(&self, database_id: &str) -> Result<Vec<VultrDatabaseMaintenanceUpdate>> {
        let url = format!("{}/databases/{}/maintenance", self.base_url, database_id);
        let response = self.client.get(&url).await?;

        let updates_response: VultrDatabaseMaintenanceUpdatesResponse = response.json().await?;
        Ok(updates_response.available_updates)
    }

    #[instrument(skip(self))]
    pub async fn start_database_maintenance_updates(&self, database_id: &str) -> Result<()> {
        let url = format!("{}/databases/{}/maintenance", self.base_url, database_id);
        let response = self.client.post(&url).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to start database maintenance updates: {}", error_text));
        }

        Ok(())
    }

    #[instrument(skip(self))]
    pub async fn list_database_service_alerts(&self, database_id: &str) -> Result<Vec<VultrDatabaseServiceAlert>> {
        let url = format!("{}/databases/{}/alerts", self.base_url, database_id);
        let response = self.client.post(&url).send().await?;

        let alerts_response: VultrDatabaseServiceAlertsResponse = response.json().await?;
        Ok(alerts_response.alerts)
    }

    #[instrument(skip(self))]
    pub async fn view_database_migration_status(&self, database_id: &str) -> Result<VultrDatabaseMigrationStatus> {
        let url = format!("{}/databases/{}/migration", self.base_url, database_id);
        let response = self.client.get(&url).await?;

        let status: VultrDatabaseMigrationStatus = response.json().await?;
        Ok(status)
    }

    #[instrument(skip(self, request))]
    pub async fn start_database_migration(&self, database_id: &str, request: StartDatabaseMigrationRequest) -> Result<VultrDatabaseMigrationStatus> {
        let url = format!("{}/databases/{}/migration", self.base_url, database_id);
        let response = self.client.post(&url).json(&request).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to start database migration: {}", error_text));
        }

        let status: VultrDatabaseMigrationStatus = response.json().await?;
        Ok(status)
    }

    #[instrument(skip(self))]
    pub async fn detach_database_migration(&self, database_id: &str) -> Result<()> {
        let url = format!("{}/databases/{}/migration", self.base_url, database_id);
        let response = self.client.delete(&url).await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to detach database migration: {}", error_text));
        }

        Ok(())
    }

    #[instrument(skip(self, request))]
    pub async fn add_database_read_replica(&self, database_id: &str, request: AddDatabaseReadReplicaRequest) -> Result<VultrDatabaseReadReplica> {
        let url = format!("{}/databases/{}/read-replica", self.base_url, database_id);
        let response = self.client.post(&url).json(&request).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to add database read replica: {}", error_text));
        }

        let replica: VultrDatabaseReadReplica = response.json().await?;
        Ok(replica)
    }

    #[instrument(skip(self))]
    pub async fn promote_database_read_replica(&self, database_id: &str) -> Result<()> {
        let url = format!("{}/databases/{}/promote-read-replica", self.base_url, database_id);
        let response = self.client.post(&url).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to promote database read replica: {}", error_text));
        }

        Ok(())
    }

    #[instrument(skip(self))]
    pub async fn get_database_backup_information(&self, database_id: &str) -> Result<VultrDatabaseBackupInfo> {
        let url = format!("{}/databases/{}/backups", self.base_url, database_id);
        let response = self.client.get(&url).await?;

        let backup_info: VultrDatabaseBackupInfo = response.json().await?;
        Ok(backup_info)
    }

    #[instrument(skip(self, request))]
    pub async fn restore_database_from_backup(&self, database_id: &str, request: RestoreDatabaseFromBackupRequest) -> Result<VultrManagedDatabase> {
        let url = format!("{}/databases/{}/restore", self.base_url, database_id);
        let response = self.client.post(&url).json(&request).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to restore database from backup: {}", error_text));
        }

        let database: VultrManagedDatabase = response.json().await?;
        Ok(database)
    }

    #[instrument(skip(self, request))]
    pub async fn fork_database(&self, database_id: &str, request: ForkDatabaseRequest) -> Result<VultrManagedDatabase> {
        let url = format!("{}/databases/{}/fork", self.base_url, database_id);
        let response = self.client.post(&url).json(&request).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to fork database: {}", error_text));
        }

        let database: VultrManagedDatabase = response.json().await?;
        Ok(database)
    }

    // Database Connection Pool endpoints
    #[instrument(skip(self))]
    pub async fn list_database_connection_pools(&self, database_id: &str) -> Result<Vec<VultrDatabaseConnectionPool>> {
        let url = format!("{}/databases/{}/connection-pools", self.base_url, database_id);
        let response = self.client.get(&url).await?;

        let pools_response: VultrDatabaseConnectionPoolsResponse = response.json().await?;
        Ok(pools_response.connection_pools)
    }

    #[instrument(skip(self, request))]
    pub async fn create_database_connection_pool(&self, database_id: &str, request: CreateDatabaseConnectionPoolRequest) -> Result<VultrDatabaseConnectionPool> {
        let url = format!("{}/databases/{}/connection-pools", self.base_url, database_id);
        let response = self.client.post(&url).json(&request).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to create database connection pool: {}", error_text));
        }

        let pool: VultrDatabaseConnectionPool = response.json().await?;
        Ok(pool)
    }

    #[instrument(skip(self))]
    pub async fn get_database_connection_pool(&self, database_id: &str, pool_name: &str) -> Result<VultrDatabaseConnectionPool> {
        let url = format!("{}/databases/{}/connection-pools/{}", self.base_url, database_id, pool_name);
        let response = self.client.get(&url).await?;

        let pool: VultrDatabaseConnectionPool = response.json().await?;
        Ok(pool)
    }

    #[instrument(skip(self, request))]
    pub async fn update_database_connection_pool(&self, database_id: &str, pool_name: &str, request: UpdateDatabaseConnectionPoolRequest) -> Result<VultrDatabaseConnectionPool> {
        let url = format!("{}/databases/{}/connection-pools/{}", self.base_url, database_id, pool_name);
        let response = self.client.put(&url).json(&request).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to update database connection pool: {}", error_text));
        }

        let pool: VultrDatabaseConnectionPool = response.json().await?;
        Ok(pool)
    }

    #[instrument(skip(self))]
    pub async fn delete_database_connection_pool(&self, database_id: &str, pool_name: &str) -> Result<()> {
        let url = format!("{}/databases/{}/connection-pools/{}", self.base_url, database_id, pool_name);
        let response = self.client.delete(&url).await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to delete database connection pool: {}", error_text));
        }

        Ok(())
    }

    // Database Advanced Options endpoints
    #[instrument(skip(self))]
    pub async fn list_database_advanced_options(&self, database_id: &str) -> Result<VultrDatabaseAdvancedOptions> {
        let url = format!("{}/databases/{}/advanced-options", self.base_url, database_id);
        let response = self.client.get(&url).await?;

        let options: VultrDatabaseAdvancedOptions = response.json().await?;
        Ok(options)
    }

    #[instrument(skip(self, request))]
    pub async fn update_database_advanced_options(&self, database_id: &str, request: VultrDatabaseAdvancedOptions) -> Result<VultrDatabaseAdvancedOptions> {
        let url = format!("{}/databases/{}/advanced-options", self.base_url, database_id);
        let response = self.client.put(&url).json(&request).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to update database advanced options: {}", error_text));
        }

        let options: VultrDatabaseAdvancedOptions = response.json().await?;
        Ok(options)
    }

    // Database Version endpoints
    #[instrument(skip(self))]
    pub async fn list_database_available_versions(&self, database_id: &str) -> Result<VultrDatabaseVersions> {
        let url = format!("{}/databases/{}/version-upgrade", self.base_url, database_id);
        let response = self.client.get(&url).await?;

        let versions: VultrDatabaseVersions = response.json().await?;
        Ok(versions)
    }

    #[instrument(skip(self, request))]
    pub async fn start_database_version_upgrade(&self, database_id: &str, request: StartDatabaseVersionUpgradeRequest) -> Result<()> {
        let url = format!("{}/databases/{}/version-upgrade", self.base_url, database_id);
        let response = self.client.post(&url).json(&request).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to start database version upgrade: {}", error_text));
        }

        Ok(())
    }

    // Marketplace endpoints
    #[instrument(skip(self))]
    pub async fn list_marketplace_app_variables(&self, image_id: &str) -> Result<Vec<VultrMarketplaceAppVariable>> {
        let url = format!("{}/marketplace/apps/{}/variables", self.base_url, image_id);
        let response = self.client.get(&url).await?;

        let variables_response: VultrMarketplaceAppVariablesResponse = response.json().await?;
        Ok(variables_response.variables)
    }

    // Object Storage (S3) endpoints
    #[instrument(skip(self))]
    pub async fn list_object_storages(&self) -> Result<Vec<VultrObjectStorage>> {
        let url = format!("{}/object-storage", self.base_url);
        let response = self.client.get(&url).await?;

        let storages_response: VultrObjectStoragesResponse = response.json().await?;
        Ok(storages_response.object_storages)
    }

    #[instrument(skip(self, request))]
    pub async fn create_object_storage(&self, request: CreateObjectStorageRequest) -> Result<VultrObjectStorage> {
        let url = format!("{}/object-storage", self.base_url);
        let response = self.client.post(&url).json(&request).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to create object storage: {}", error_text));
        }

        let storage: VultrObjectStorage = response.json().await?;
        Ok(storage)
    }

    #[instrument(skip(self))]
    pub async fn get_object_storage(&self, object_storage_id: &str) -> Result<VultrObjectStorage> {
        let url = format!("{}/object-storage/{}", self.base_url, object_storage_id);
        let response = self.client.get(&url).await?;

        let storage: VultrObjectStorage = response.json().await?;
        Ok(storage)
    }

    #[instrument(skip(self))]
    pub async fn delete_object_storage(&self, object_storage_id: &str) -> Result<()> {
        let url = format!("{}/object-storage/{}", self.base_url, object_storage_id);
        let response = self.client.delete(&url).await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to delete object storage: {}", error_text));
        }

        Ok(())
    }

    #[instrument(skip(self, request))]
    pub async fn update_object_storage(&self, object_storage_id: &str, request: UpdateObjectStorageRequest) -> Result<VultrObjectStorage> {
        let url = format!("{}/object-storage/{}", self.base_url, object_storage_id);
        let response = self.client.put(&url).json(&request).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to update object storage: {}", error_text));
        }

        let storage: VultrObjectStorage = response.json().await?;
        Ok(storage)
    }

    #[instrument(skip(self))]
    pub async fn regenerate_object_storage_keys(&self, object_storage_id: &str) -> Result<VultrObjectStorage> {
        let url = format!("{}/object-storage/{}/regenerate-keys", self.base_url, object_storage_id);
        let response = self.client.post(&url).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to regenerate object storage keys: {}", error_text));
        }

        let storage: VultrObjectStorage = response.json().await?;
        Ok(storage)
    }

    #[instrument(skip(self))]
    pub async fn list_object_storage_clusters(&self) -> Result<Vec<VultrObjectStorageCluster>> {
        let url = format!("{}/object-storage/clusters", self.base_url);
        let response = self.client.get(&url).await?;

        let clusters_response: VultrObjectStorageClustersResponse = response.json().await?;
        Ok(clusters_response.clusters)
    }

    #[instrument(skip(self))]
    pub async fn list_object_storage_tiers(&self) -> Result<Vec<VultrObjectStorageTier>> {
        let url = format!("{}/object-storage/tiers", self.base_url);
        let response = self.client.get(&url).await?;

        let tiers_response: VultrObjectStorageTiersResponse = response.json().await?;
        Ok(tiers_response.tiers)
    }

    #[instrument(skip(self))]
    pub async fn list_object_storage_cluster_tiers(&self, cluster_id: u32) -> Result<Vec<VultrObjectStorageTier>> {
        let url = format!("{}/object-storage/clusters/{}/tiers", self.base_url, cluster_id);
        let response = self.client.get(&url).await?;

        let tiers_response: VultrObjectStorageTiersResponse = response.json().await?;
        Ok(tiers_response.tiers)
    }



    #[instrument(skip(self))]
    pub async fn list_metal_plans(&self) -> Result<Vec<VultrMetalPlan>> {
        let url = format!("{}/plans-metal", self.base_url);
        let response = self.client.get(&url).await?;

        let plans_response: VultrMetalPlansResponse = response.json().await?;
        Ok(plans_response.plans_metal)
    }

    // Serverless Inference endpoints
    #[instrument(skip(self))]
    pub async fn list_inference(&self) -> Result<Vec<VultrInference>> {
        let url = format!("{}/inference", self.base_url);
        let response = self.client.get(&url).await?;

        let inferences_response: VultrInferencesResponse = response.json().await?;
        Ok(inferences_response.inferences)
    }

    #[instrument(skip(self, request))]
    pub async fn create_inference(&self, request: CreateInferenceRequest) -> Result<VultrInference> {
        let url = format!("{}/inference", self.base_url);
        let response = self.client.post(&url).json(&request).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to create inference: {}", error_text));
        }

        let inference: VultrInference = response.json().await?;
        Ok(inference)
    }

    #[instrument(skip(self))]
    pub async fn get_inference(&self, inference_id: &str) -> Result<VultrInference> {
        let url = format!("{}/inference/{}", self.base_url, inference_id);
        let response = self.client.get(&url).await?;

        let inference: VultrInference = response.json().await?;
        Ok(inference)
    }

    #[instrument(skip(self, request))]
    pub async fn update_inference(&self, inference_id: &str, request: UpdateInferenceRequest) -> Result<VultrInference> {
        let url = format!("{}/inference/{}", self.base_url, inference_id);
        let response = self.client.patch(&url).json(&request).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to update inference: {}", error_text));
        }

        let inference: VultrInference = response.json().await?;
        Ok(inference)
    }

    #[instrument(skip(self))]
    pub async fn delete_inference(&self, inference_id: &str) -> Result<()> {
        let url = format!("{}/inference/{}", self.base_url, inference_id);
        let response = self.client.delete(&url).await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to delete inference: {}", error_text));
        }

        Ok(())
    }

    #[instrument(skip(self))]
    pub async fn get_inference_usage(&self, inference_id: &str) -> Result<VultrInferenceUsage> {
        let url = format!("{}/inference/{}/usage", self.base_url, inference_id);
        let response = self.client.get(&url).await?;

        let usage: VultrInferenceUsage = response.json().await?;
        Ok(usage)
    }



    #[instrument(skip(self, request))]
    pub async fn create_vpc(&self, request: CreateVPCRequest) -> Result<VultrVPCDetailed> {
        let url = format!("{}/vpcs", self.base_url);
        let response = self.client.post(&url).json(&request).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to create VPC: {}", error_text));
        }

        let vpc: VultrVPCDetailed = response.json().await?;
        Ok(vpc)
    }

    #[instrument(skip(self))]
    pub async fn get_vpc(&self, vpc_id: &str) -> Result<VultrVPCDetailed> {
        let url = format!("{}/vpcs/{}", self.base_url, vpc_id);
        let response = self.client.get(&url).await?;

        let vpc: VultrVPCDetailed = response.json().await?;
        Ok(vpc)
    }

    #[instrument(skip(self, request))]
    pub async fn update_vpc(&self, vpc_id: &str, request: UpdateVPCRequest) -> Result<()> {
        let url = format!("{}/vpcs/{}", self.base_url, vpc_id);
        let response = self.client.put(&url).json(&request).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to update VPC: {}", error_text));
        }

        Ok(())
    }

    #[instrument(skip(self))]
    pub async fn delete_vpc(&self, vpc_id: &str) -> Result<()> {
        let url = format!("{}/vpcs/{}", self.base_url, vpc_id);
        let response = self.client.delete(&url).await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to delete VPC: {}", error_text));
        }

        Ok(())
    }



    #[instrument(skip(self, request))]
    pub async fn create_reserved_ip(&self, request: CreateReservedIPRequest) -> Result<VultrReservedIPDetailed> {
        let url = format!("{}/reserved-ips", self.base_url);
        let response = self.client.post(&url).json(&request).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to create reserved IP: {}", error_text));
        }

        let reserved_ip: VultrReservedIPDetailed = response.json().await?;
        Ok(reserved_ip)
    }

    #[instrument(skip(self))]
    pub async fn get_reserved_ip(&self, reserved_ip: &str) -> Result<VultrReservedIPDetailed> {
        let url = format!("{}/reserved-ips/{}", self.base_url, reserved_ip);
        let response = self.client.get(&url).await?;

        let reserved_ip_info: VultrReservedIPDetailed = response.json().await?;
        Ok(reserved_ip_info)
    }

    #[instrument(skip(self, request))]
    pub async fn update_reserved_ip(&self, reserved_ip: &str, request: UpdateReservedIPRequest) -> Result<VultrReservedIPDetailed> {
        let url = format!("{}/reserved-ips/{}", self.base_url, reserved_ip);
        let response = self.client.patch(&url).json(&request).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to update reserved IP: {}", error_text));
        }

        let reserved_ip_info: VultrReservedIPDetailed = response.json().await?;
        Ok(reserved_ip_info)
    }

    #[instrument(skip(self))]
    pub async fn delete_reserved_ip(&self, reserved_ip: &str) -> Result<()> {
        let url = format!("{}/reserved-ips/{}", self.base_url, reserved_ip);
        let response = self.client.delete(&url).await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to delete reserved IP: {}", error_text));
        }

        Ok(())
    }

    #[instrument(skip(self, request))]
    pub async fn attach_reserved_ip(&self, reserved_ip: &str, request: AttachReservedIPRequest) -> Result<()> {
        let url = format!("{}/reserved-ips/{}/attach", self.base_url, reserved_ip);
        let response = self.client.post(&url).json(&request).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to attach reserved IP: {}", error_text));
        }

        Ok(())
    }

    #[instrument(skip(self))]
    pub async fn detach_reserved_ip(&self, reserved_ip: &str) -> Result<()> {
        let url = format!("{}/reserved-ips/{}/detach", self.base_url, reserved_ip);
        let response = self.client.post(&url).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to detach reserved IP: {}", error_text));
        }

        Ok(())
    }

    #[instrument(skip(self, request))]
    pub async fn convert_reserved_ip(&self, request: ConvertReservedIPRequest) -> Result<VultrReservedIPDetailed> {
        let url = format!("{}/reserved-ips/convert", self.base_url);
        let response = self.client.post(&url).json(&request).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to convert reserved IP: {}", error_text));
        }

        let reserved_ip: VultrReservedIPDetailed = response.json().await?;
        Ok(reserved_ip)
    }



    #[instrument(skip(self))]
    pub async fn list_available_plans_region(&self, region_id: &str) -> Result<VultrRegionAvailablePlans> {
        let url = format!("{}/regions/{}/availability", self.base_url, region_id);
        let response = self.client.get(&url).await?;

        let plans: VultrRegionAvailablePlans = response.json().await?;
        Ok(plans)
    }



    #[instrument(skip(self, request))]
    pub async fn create_snapshot(&self, request: CreateSnapshotRequest) -> Result<VultrSnapshot> {
        let url = format!("{}/snapshots", self.base_url);
        let response = self.client.post(&url).json(&request).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to create snapshot: {}", error_text));
        }

        let snapshot: VultrSnapshot = response.json().await?;
        Ok(snapshot)
    }

    #[instrument(skip(self, request))]
    pub async fn create_snapshot_from_url(&self, request: CreateSnapshotFromUrlRequest) -> Result<VultrSnapshot> {
        let url = format!("{}/snapshots/create-from-url", self.base_url);
        let response = self.client.post(&url).json(&request).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to create snapshot from URL: {}", error_text));
        }

        let snapshot: VultrSnapshot = response.json().await?;
        Ok(snapshot)
    }



    #[instrument(skip(self, request))]
    pub async fn update_snapshot(&self, snapshot_id: &str, request: UpdateSnapshotRequest) -> Result<VultrSnapshot> {
        let url = format!("{}/snapshots/{}", self.base_url, snapshot_id);
        let response = self.client.put(&url).json(&request).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to update snapshot: {}", error_text));
        }

        let snapshot: VultrSnapshot = response.json().await?;
        Ok(snapshot)
    }

    #[instrument(skip(self))]
    pub async fn delete_snapshot(&self, snapshot_id: &str) -> Result<()> {
        let url = format!("{}/snapshots/{}", self.base_url, snapshot_id);
        let response = self.client.delete(&url).await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to delete snapshot: {}", error_text));
        }

        Ok(())
    }

    // Subaccount endpoints
    #[instrument(skip(self))]
    pub async fn list_subaccounts(&self) -> Result<Vec<VultrSubaccount>> {
        let url = format!("{}/subaccounts", self.base_url);
        let response = self.client.get(&url).await?;

        let subaccounts_response: VultrSubaccountsResponse = response.json().await?;
        Ok(subaccounts_response.subaccounts)
    }

    #[instrument(skip(self, request))]
    pub async fn create_subaccount(&self, request: CreateSubaccountRequest) -> Result<VultrSubaccount> {
        let url = format!("{}/subaccounts", self.base_url);
        let response = self.client.post(&url).json(&request).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to create subaccount: {}", error_text));
        }

        let subaccount: VultrSubaccount = response.json().await?;
        Ok(subaccount)
    }



    #[instrument(skip(self, request))]
    pub async fn update_ssh_key(&self, ssh_key_id: &str, request: UpdateSSHKeyRequest) -> Result<VultrSSHKey> {
        let url = format!("{}/ssh-keys/{}", self.base_url, ssh_key_id);
        let response = self.client.patch(&url).json(&request).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to update SSH key: {}", error_text));
        }

        let ssh_key: VultrSSHKey = response.json().await?;
        Ok(ssh_key)
    }

    #[instrument(skip(self))]
    pub async fn delete_ssh_key(&self, ssh_key_id: &str) -> Result<()> {
        let url = format!("{}/ssh-keys/{}", self.base_url, ssh_key_id);
        let response = self.client.delete(&url).await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to delete SSH key: {}", error_text));
        }

        Ok(())
    }



    #[instrument(skip(self, request))]
    pub async fn update_startup_script(&self, script_id: &str, request: UpdateStartupScriptRequest) -> Result<VultrStartupScript> {
        let url = format!("{}/startup-scripts/{}", self.base_url, script_id);
        let response = self.client.patch(&url).json(&request).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to update startup script: {}", error_text));
        }

        let script: VultrStartupScript = response.json().await?;
        Ok(script)
    }

    #[instrument(skip(self))]
    pub async fn delete_startup_script(&self, script_id: &str) -> Result<()> {
        let url = format!("{}/startup-scripts/{}", self.base_url, script_id);
        let response = self.client.delete(&url).await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to delete startup script: {}", error_text));
        }

        Ok(())
    }



    #[instrument(skip(self, request))]
    pub async fn create_storage_gateway(&self, request: CreateStorageGatewayRequest) -> Result<VultrStorageGateway> {
        let url = format!("{}/storage-gateways", self.base_url);
        let response = self.client.post(&url).json(&request).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to create storage gateway: {}", error_text));
        }

        let gateway: VultrStorageGateway = response.json().await?;
        Ok(gateway)
    }

    #[instrument(skip(self))]
    pub async fn get_storage_gateway(&self, gateway_id: &str) -> Result<VultrStorageGateway> {
        let url = format!("{}/storage-gateways/{}", self.base_url, gateway_id);
        let response = self.client.get(&url).await?;

        let gateway: VultrStorageGateway = response.json().await?;
        Ok(gateway)
    }

    #[instrument(skip(self, request))]
    pub async fn update_storage_gateway(&self, gateway_id: &str, request: UpdateStorageGatewayRequest) -> Result<VultrStorageGateway> {
        let url = format!("{}/storage-gateways/{}", self.base_url, gateway_id);
        let response = self.client.put(&url).json(&request).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to update storage gateway: {}", error_text));
        }

        let gateway: VultrStorageGateway = response.json().await?;
        Ok(gateway)
    }

    #[instrument(skip(self))]
    pub async fn delete_storage_gateway(&self, gateway_id: &str) -> Result<()> {
        let url = format!("{}/storage-gateways/{}", self.base_url, gateway_id);
        let response = self.client.delete(&url).await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to delete storage gateway: {}", error_text));
        }

        Ok(())
    }

    #[instrument(skip(self, request))]
    pub async fn add_storage_gateway_export(&self, gateway_id: &str, request: AddStorageGatewayExportRequest) -> Result<VultrStorageGatewayExport> {
        let url = format!("{}/storage-gateways/{}/exports", self.base_url, gateway_id);
        let response = self.client.post(&url).json(&request).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to add storage gateway export: {}", error_text));
        }

        let export: VultrStorageGatewayExport = response.json().await?;
        Ok(export)
    }

    #[instrument(skip(self))]
    pub async fn delete_storage_gateway_export(&self, gateway_id: &str, export_id: &str) -> Result<()> {
        let url = format!("{}/storage-gateways/{}/exports/{}", self.base_url, gateway_id, export_id);
        let response = self.client.delete(&url).await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to delete storage gateway export: {}", error_text));
        }

        Ok(())
    }



    #[instrument(skip(self, request))]
    pub async fn create_user(&self, request: CreateUserRequest) -> Result<VultrUser> {
        let url = format!("{}/users", self.base_url);
        let response = self.client.post(&url).json(&request).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to create user: {}", error_text));
        }

        let user: VultrUser = response.json().await?;
        Ok(user)
    }

    #[instrument(skip(self))]
    pub async fn get_user(&self, user_id: &str) -> Result<VultrUser> {
        let url = format!("{}/users/{}", self.base_url, user_id);
        let response = self.client.get(&url).await?;

        let user: VultrUser = response.json().await?;
        Ok(user)
    }

    #[instrument(skip(self, request))]
    pub async fn update_user(&self, user_id: &str, request: UpdateUserRequest) -> Result<VultrUser> {
        let url = format!("{}/users/{}", self.base_url, user_id);
        let response = self.client.patch(&url).json(&request).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to update user: {}", error_text));
        }

        let user: VultrUser = response.json().await?;
        Ok(user)
    }

    #[instrument(skip(self))]
    pub async fn delete_user(&self, user_id: &str) -> Result<()> {
        let url = format!("{}/users/{}", self.base_url, user_id);
        let response = self.client.delete(&url).await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to delete user: {}", error_text));
        }

        Ok(())
    }

    // VFS (Vultr File System) endpoints
    #[instrument(skip(self))]
    pub async fn list_vfs_regions(&self) -> Result<Vec<VultrRegion>> {
        let url = format!("{}/vfs/regions", self.base_url);
        let response = self.client.get(&url).await?;

        let regions_response: VultrRegionsResponse = response.json().await?;
        Ok(regions_response.regions)
    }

    #[instrument(skip(self))]
    pub async fn list_vfs(&self) -> Result<Vec<VultrVFS>> {
        let url = format!("{}/vfs", self.base_url);
        let response = self.client.get(&url).await?;

        let vfs_response: VultrVFSResponse = response.json().await?;
        Ok(vfs_response.vfs)
    }

    #[instrument(skip(self, request))]
    pub async fn create_vfs(&self, request: CreateVFSRequest) -> Result<VultrVFS> {
        let url = format!("{}/vfs", self.base_url);
        let response = self.client.post(&url).json(&request).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to create VFS: {}", error_text));
        }

        let vfs: VultrVFS = response.json().await?;
        Ok(vfs)
    }

    #[instrument(skip(self))]
    pub async fn get_vfs(&self, vfs_id: &str) -> Result<VultrVFS> {
        let url = format!("{}/vfs/{}", self.base_url, vfs_id);
        let response = self.client.get(&url).await?;

        let vfs: VultrVFS = response.json().await?;
        Ok(vfs)
    }

    #[instrument(skip(self, request))]
    pub async fn update_vfs(&self, vfs_id: &str, request: UpdateVFSRequest) -> Result<VultrVFS> {
        let url = format!("{}/vfs/{}", self.base_url, vfs_id);
        let response = self.client.put(&url).json(&request).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to update VFS: {}", error_text));
        }

        let vfs: VultrVFS = response.json().await?;
        Ok(vfs)
    }

    #[instrument(skip(self))]
    pub async fn delete_vfs(&self, vfs_id: &str) -> Result<()> {
        let url = format!("{}/vfs/{}", self.base_url, vfs_id);
        let response = self.client.delete(&url).await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to delete VFS: {}", error_text));
        }

        Ok(())
    }

    #[instrument(skip(self))]
    pub async fn list_vfs_attachments(&self, vfs_id: &str) -> Result<Vec<VultrVFSAttachment>> {
        let url = format!("{}/vfs/{}/attachments", self.base_url, vfs_id);
        let response = self.client.get(&url).await?;

        let attachments_response: VultrVFSAttachmentsResponse = response.json().await?;
        Ok(attachments_response.attachments)
    }

    #[instrument(skip(self, request))]
    pub async fn create_vfs_attachment(&self, vfs_id: &str, request: CreateVFSAttachmentRequest) -> Result<VultrVFSAttachment> {
        let url = format!("{}/vfs/{}/attachments", self.base_url, vfs_id);
        let response = self.client.post(&url).json(&request).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to create VFS attachment: {}", error_text));
        }

        let attachment: VultrVFSAttachment = response.json().await?;
        Ok(attachment)
    }

    #[instrument(skip(self))]
    pub async fn get_vfs_attachment(&self, vfs_id: &str, attachment_id: &str) -> Result<VultrVFSAttachment> {
        let url = format!("{}/vfs/{}/attachments/{}", self.base_url, vfs_id, attachment_id);
        let response = self.client.get(&url).await?;

        let attachment: VultrVFSAttachment = response.json().await?;
        Ok(attachment)
    }

    #[instrument(skip(self))]
    pub async fn delete_vfs_attachment(&self, vfs_id: &str, attachment_id: &str) -> Result<()> {
        let url = format!("{}/vfs/{}/attachments/{}", self.base_url, vfs_id, attachment_id);
        let response = self.client.delete(&url).await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to delete VFS attachment: {}", error_text));
        }

        Ok(())
    }
}
