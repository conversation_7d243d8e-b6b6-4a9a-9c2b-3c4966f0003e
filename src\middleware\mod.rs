pub mod auth;
pub mod cors;
pub mod logging;
pub mod rate_limit;

use axum::{
    http::{Request, StatusCode},
    middleware::Next,
    response::Response,
    body::Body,
};
use std::time::Instant;
use tracing::{info, warn};

// Request ID middleware
pub async fn request_id_middleware(
    mut req: Request<Body>,
    next: Next,
) -> Response {
    let request_id = uuid::Uuid::new_v4().to_string();
    req.headers_mut().insert(
        "x-request-id",
        request_id.parse().unwrap(),
    );
    
    let response = next.run(req).await;
    response
}

// Request timing middleware
pub async fn timing_middleware(
    req: Request<Body>,
    next: Next,
) -> Response {
    let start = Instant::now();
    let method = req.method().clone();
    let uri = req.uri().clone();
    
    let response = next.run(req).await;
    
    let duration = start.elapsed();
    let status = response.status();
    
    if status.is_server_error() {
        warn!(
            method = %method,
            uri = %uri,
            status = %status,
            duration_ms = duration.as_millis(),
            "Request completed with server error"
        );
    } else {
        info!(
            method = %method,
            uri = %uri,
            status = %status,
            duration_ms = duration.as_millis(),
            "Request completed"
        );
    }
    
    response
}

// Error handling middleware
pub async fn error_handling_middleware(
    req: Request<Body>,
    next: Next,
) -> Response {
    let response = next.run(req).await;
    
    // Log errors and add correlation IDs
    if response.status().is_server_error() {
        // Add error tracking headers
        // This would integrate with error tracking services like Sentry
    }
    
    response
}
