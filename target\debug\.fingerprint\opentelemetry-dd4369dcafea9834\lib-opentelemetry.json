{"rustc": 2830703817519440116, "features": "[\"default\", \"metrics\", \"pin-project-lite\", \"trace\"]", "declared_features": "[\"default\", \"logs\", \"logs_level_enabled\", \"metrics\", \"pin-project-lite\", \"testing\", \"trace\"]", "target": 8496431757124413012, "profile": 2241668132362809309, "path": 18235190574995205246, "deps": [[1906322745568073236, "pin_project_lite", false, 13886474930693114932], [1996688857878793156, "<PERSON><PERSON><PERSON><PERSON>", false, 5970070653155887088], [3722963349756955755, "once_cell", false, 10983383885145764756], [7013762810557009322, "futures_sink", false, 5564455281536599080], [7620660491849607393, "futures_core", false, 9910657298933926885], [8008191657135824715, "thiserror", false, 8779817158858857571], [14483812548788871374, "indexmap", false, 14167501813314976866]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\opentelemetry-dd4369dcafea9834\\dep-lib-opentelemetry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}