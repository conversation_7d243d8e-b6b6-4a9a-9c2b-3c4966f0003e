{"rustc": 2830703817519440116, "features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"channel\", \"default\", \"futures-channel\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"io\", \"memchr\", \"sink\", \"slab\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"bilock\", \"cfg-target-has-atomic\", \"channel\", \"compat\", \"default\", \"futures-channel\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"futures_01\", \"io\", \"io-compat\", \"memchr\", \"portable-atomic\", \"sink\", \"slab\", \"std\", \"tokio-io\", \"unstable\", \"write-all-vectored\"]", "target": 1788798584831431502, "profile": 17467636112133979524, "path": 13671742598898157133, "deps": [[5103565458935487, "futures_io", false, 14764076604848365451], [1615478164327904835, "pin_utils", false, 5446903602768609306], [1811549171721445101, "futures_channel", false, 8930785311309996714], [1906322745568073236, "pin_project_lite", false, 13886474930693114932], [5451793922601807560, "slab", false, 4976906906525213565], [7013762810557009322, "futures_sink", false, 5564455281536599080], [7620660491849607393, "futures_core", false, 9910657298933926885], [10565019901765856648, "futures_macro", false, 18056632786705849544], [15932120279885307830, "memchr", false, 17104003403262588927], [16240732885093539806, "futures_task", false, 1190188428776250535]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\futures-util-a83705792396b546\\dep-lib-futures_util", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}