use super::{models::*, VultrApiClient};
use crate::config::Config;
use anyhow::Result;
use std::sync::Arc;
use tracing::instrument;

#[derive(Clone)]
pub struct VultrClient {
    api_client: Arc<VultrApiClient>,
}

impl VultrClient {
    pub fn new(api_key: &str, config: &Config) -> Result<Self> {
        let api_client = VultrApiClient::new(api_key.to_string(), config)?;
        Ok(Self {
            api_client: Arc::new(api_client),
        })
    }

    #[instrument(skip(self))]
    pub async fn get_account(&self) -> Result<VultrAccount> {
        self.api_client.get_account().await
    }

    #[instrument(skip(self))]
    pub async fn get_account_bgp(&self) -> Result<VultrAccountBGP> {
        self.api_client.get_account_bgp().await
    }

    #[instrument(skip(self))]
    pub async fn get_account_bandwidth(&self) -> Result<VultrAccountBandwidth> {
        self.api_client.get_account_bandwidth().await
    }

    #[instrument(skip(self))]
    pub async fn list_instances(&self) -> Result<Vec<VultrInstance>> {
        self.api_client.list_instances().await
    }

    #[instrument(skip(self))]
    pub async fn get_instance(&self, instance_id: &str) -> Result<VultrInstance> {
        self.api_client.get_instance(instance_id).await
    }

    #[instrument(skip(self, request))]
    pub async fn create_instance(&self, request: CreateVultrInstanceRequest) -> Result<VultrInstance> {
        self.api_client.create_instance(request).await
    }

    #[instrument(skip(self))]
    pub async fn delete_instance(&self, instance_id: &str) -> Result<()> {
        self.api_client.delete_instance(instance_id).await
    }

    #[instrument(skip(self))]
    pub async fn start_instance(&self, instance_id: &str) -> Result<()> {
        self.api_client.start_instance(instance_id).await
    }

    #[instrument(skip(self))]
    pub async fn stop_instance(&self, instance_id: &str) -> Result<()> {
        self.api_client.stop_instance(instance_id).await
    }

    #[instrument(skip(self))]
    pub async fn restart_instance(&self, instance_id: &str) -> Result<()> {
        self.api_client.restart_instance(instance_id).await
    }



    // Backup methods
    #[instrument(skip(self))]
    pub async fn list_backups(&self) -> Result<Vec<VultrBackup>> {
        self.api_client.list_backups().await
    }

    #[instrument(skip(self))]
    pub async fn get_backup(&self, backup_id: &str) -> Result<VultrBackup> {
        self.api_client.get_backup(backup_id).await
    }

    // Bare Metal methods
    #[instrument(skip(self))]
    pub async fn list_bare_metal(&self) -> Result<Vec<VultrBareMetal>> {
        self.api_client.list_bare_metal().await
    }

    #[instrument(skip(self, request))]
    pub async fn create_bare_metal(&self, request: CreateBareMetalRequest) -> Result<VultrBareMetal> {
        self.api_client.create_bare_metal(request).await
    }

    #[instrument(skip(self))]
    pub async fn get_bare_metal(&self, baremetal_id: &str) -> Result<VultrBareMetal> {
        self.api_client.get_bare_metal(baremetal_id).await
    }

    #[instrument(skip(self, request))]
    pub async fn update_bare_metal(&self, baremetal_id: &str, request: UpdateBareMetalRequest) -> Result<VultrBareMetal> {
        self.api_client.update_bare_metal(baremetal_id, request).await
    }

    #[instrument(skip(self))]
    pub async fn delete_bare_metal(&self, baremetal_id: &str) -> Result<()> {
        self.api_client.delete_bare_metal(baremetal_id).await
    }

    #[instrument(skip(self))]
    pub async fn get_bare_metal_ipv4(&self, baremetal_id: &str) -> Result<Vec<BareMetalIpv4Info>> {
        self.api_client.get_bare_metal_ipv4(baremetal_id).await
    }

    #[instrument(skip(self))]
    pub async fn get_bare_metal_ipv6(&self, baremetal_id: &str) -> Result<Vec<BareMetalIpv6Info>> {
        self.api_client.get_bare_metal_ipv6(baremetal_id).await
    }

    #[instrument(skip(self))]
    pub async fn create_bare_metal_ipv4_reverse(&self, baremetal_id: &str, ip: &str, reverse: &str) -> Result<()> {
        self.api_client.create_bare_metal_ipv4_reverse(baremetal_id, ip, reverse).await
    }

    #[instrument(skip(self))]
    pub async fn create_bare_metal_ipv6_reverse(&self, baremetal_id: &str, ip: &str, reverse: &str) -> Result<()> {
        self.api_client.create_bare_metal_ipv6_reverse(baremetal_id, ip, reverse).await
    }

    #[instrument(skip(self))]
    pub async fn reset_bare_metal_ipv4_reverse(&self, baremetal_id: &str, ip: &str) -> Result<()> {
        self.api_client.reset_bare_metal_ipv4_reverse(baremetal_id, ip).await
    }

    #[instrument(skip(self))]
    pub async fn delete_bare_metal_ipv6_reverse(&self, baremetal_id: &str, ip: &str) -> Result<()> {
        self.api_client.delete_bare_metal_ipv6_reverse(baremetal_id, ip).await
    }

    #[instrument(skip(self))]
    pub async fn start_bare_metal(&self, baremetal_id: &str) -> Result<()> {
        self.api_client.start_bare_metal(baremetal_id).await
    }

    #[instrument(skip(self))]
    pub async fn reboot_bare_metal(&self, baremetal_id: &str) -> Result<()> {
        self.api_client.reboot_bare_metal(baremetal_id).await
    }

    #[instrument(skip(self))]
    pub async fn reinstall_bare_metal(&self, baremetal_id: &str) -> Result<VultrBareMetal> {
        self.api_client.reinstall_bare_metal(baremetal_id).await
    }

    #[instrument(skip(self))]
    pub async fn halt_bare_metal(&self, baremetal_id: &str) -> Result<()> {
        self.api_client.halt_bare_metal(baremetal_id).await
    }

    #[instrument(skip(self))]
    pub async fn get_bare_metal_bandwidth(&self, baremetal_id: &str) -> Result<BareMetalBandwidth> {
        self.api_client.get_bare_metal_bandwidth(baremetal_id).await
    }

    #[instrument(skip(self, instance_ids))]
    pub async fn halt_bare_metals(&self, instance_ids: Vec<String>) -> Result<()> {
        self.api_client.halt_bare_metals(instance_ids).await
    }

    #[instrument(skip(self, instance_ids))]
    pub async fn reboot_bare_metals(&self, instance_ids: Vec<String>) -> Result<()> {
        self.api_client.reboot_bare_metals(instance_ids).await
    }

    #[instrument(skip(self, instance_ids))]
    pub async fn start_bare_metals(&self, instance_ids: Vec<String>) -> Result<()> {
        self.api_client.start_bare_metals(instance_ids).await
    }

    #[instrument(skip(self))]
    pub async fn get_bare_metal_user_data(&self, baremetal_id: &str) -> Result<BareMetalUserData> {
        self.api_client.get_bare_metal_user_data(baremetal_id).await
    }

    #[instrument(skip(self))]
    pub async fn get_bare_metal_upgrades(&self, baremetal_id: &str) -> Result<BareMetalUpgrades> {
        self.api_client.get_bare_metal_upgrades(baremetal_id).await
    }

    #[instrument(skip(self))]
    pub async fn get_bare_metal_vnc(&self, baremetal_id: &str) -> Result<BareMetalVncInfo> {
        self.api_client.get_bare_metal_vnc(baremetal_id).await
    }

    #[instrument(skip(self))]
    pub async fn attach_bare_metal_vpc(&self, baremetal_id: &str, vpc_id: &str) -> Result<()> {
        self.api_client.attach_bare_metal_vpc(baremetal_id, vpc_id).await
    }

    #[instrument(skip(self))]
    pub async fn detach_bare_metal_vpc(&self, baremetal_id: &str, vpc_id: &str) -> Result<()> {
        self.api_client.detach_bare_metal_vpc(baremetal_id, vpc_id).await
    }

    #[instrument(skip(self))]
    pub async fn list_bare_metal_vpcs(&self, baremetal_id: &str) -> Result<Vec<BareMetalVpcInfo>> {
        self.api_client.list_bare_metal_vpcs(baremetal_id).await
    }

    // Block Storage methods
    #[instrument(skip(self))]
    pub async fn list_block_storage(&self) -> Result<Vec<VultrBlockStorage>> {
        self.api_client.list_block_storage().await
    }

    #[instrument(skip(self))]
    pub async fn get_block_storage(&self, block_id: &str) -> Result<VultrBlockStorage> {
        self.api_client.get_block_storage(block_id).await
    }

    #[instrument(skip(self, request))]
    pub async fn create_block_storage(&self, request: CreateBlockStorageRequest) -> Result<VultrBlockStorage> {
        let json_request = serde_json::to_value(request).map_err(|e| anyhow::anyhow!("Serialization error: {}", e))?;
        self.api_client.create_block_storage(json_request).await
    }

    #[instrument(skip(self))]
    pub async fn delete_block_storage(&self, block_id: &str) -> Result<()> {
        self.api_client.delete_block_storage(block_id).await
    }

    // CDN methods
    #[instrument(skip(self))]
    pub async fn list_cdns(&self) -> Result<Vec<VultrCDN>> {
        self.api_client.list_cdns().await
    }









    // Managed Database methods
    #[instrument(skip(self))]
    pub async fn list_databases(&self) -> Result<Vec<VultrManagedDatabase>> {
        self.api_client.list_databases().await
    }

    #[instrument(skip(self))]
    pub async fn get_database(&self, database_id: &str) -> Result<VultrManagedDatabase> {
        self.api_client.get_database(database_id).await
    }

    // Object Storage methods
    #[instrument(skip(self))]
    pub async fn list_object_storage(&self) -> Result<Vec<VultrObjectStorage>> {
        self.api_client.list_object_storage().await
    }







    // Sub-Account methods
    #[instrument(skip(self))]
    pub async fn list_sub_accounts(&self) -> Result<Vec<VultrSubAccount>> {
        self.api_client.list_sub_accounts().await
    }







    // Application methods
    #[instrument(skip(self))]
    pub async fn list_applications(&self) -> Result<Vec<VultrApplication>> {
        self.api_client.list_applications().await
    }

    // Marketplace methods
    #[instrument(skip(self))]
    pub async fn list_marketplace_apps(&self) -> Result<Vec<VultrMarketplaceApp>> {
        self.api_client.list_marketplace_apps().await
    }

    // Billing methods
    #[instrument(skip(self))]
    pub async fn get_billing_history(&self) -> Result<Vec<VultrBillingHistory>> {
        self.api_client.get_billing_history().await
    }

    #[instrument(skip(self))]
    pub async fn list_invoices(&self) -> Result<Vec<VultrInvoice>> {
        self.api_client.list_invoices().await
    }





    // Container Registry methods
    #[instrument(skip(self))]
    pub async fn list_container_registries(&self) -> Result<Vec<VultrContainerRegistry>> {
        self.api_client.list_container_registries().await
    }

    #[instrument(skip(self))]
    pub async fn get_container_registry(&self, registry_id: &str) -> Result<VultrContainerRegistry> {
        self.api_client.get_container_registry(registry_id).await
    }



    #[instrument(skip(self, request))]
    pub async fn attach_block_storage(&self, block_id: &str, request: AttachBlockStorageRequest) -> Result<()> {
        self.api_client.attach_block_storage(block_id, request).await
    }

    #[instrument(skip(self, request))]
    pub async fn detach_block_storage(&self, block_id: &str, request: DetachBlockStorageRequest) -> Result<()> {
        self.api_client.detach_block_storage(block_id, request).await
    }

    // Additional Billing methods
    #[instrument(skip(self))]
    pub async fn get_invoice_items(&self, invoice_id: &str) -> Result<Vec<VultrInvoiceItem>> {
        self.api_client.get_invoice_items(invoice_id).await
    }

    #[instrument(skip(self))]
    pub async fn get_pending_charges(&self) -> Result<VultrPendingCharges> {
        self.api_client.get_pending_charges().await
    }

    // CDN Pull Zone methods
    #[instrument(skip(self))]
    pub async fn list_pull_zones(&self) -> Result<Vec<VultrPullZone>> {
        self.api_client.list_pull_zones().await
    }

    #[instrument(skip(self, request))]
    pub async fn create_pull_zone(&self, request: CreatePullZoneRequest) -> Result<VultrPullZone> {
        self.api_client.create_pull_zone(request).await
    }

    #[instrument(skip(self))]
    pub async fn get_pull_zone(&self, pullzone_id: &str) -> Result<VultrPullZone> {
        self.api_client.get_pull_zone(pullzone_id).await
    }

    #[instrument(skip(self, request))]
    pub async fn update_pull_zone(&self, pullzone_id: &str, request: UpdatePullZoneRequest) -> Result<VultrPullZone> {
        self.api_client.update_pull_zone(pullzone_id, request).await
    }

    #[instrument(skip(self))]
    pub async fn delete_pull_zone(&self, pullzone_id: &str) -> Result<()> {
        self.api_client.delete_pull_zone(pullzone_id).await
    }

    #[instrument(skip(self, request))]
    pub async fn purge_pull_zone(&self, pullzone_id: &str, request: PurgePullZoneRequest) -> Result<()> {
        self.api_client.purge_pull_zone(pullzone_id, request).await
    }

    // CDN Push Zone methods
    #[instrument(skip(self))]
    pub async fn list_push_zones(&self) -> Result<Vec<VultrPushZone>> {
        self.api_client.list_push_zones().await
    }

    #[instrument(skip(self, request))]
    pub async fn create_push_zone(&self, request: CreatePushZoneRequest) -> Result<VultrPushZone> {
        self.api_client.create_push_zone(request).await
    }

    #[instrument(skip(self))]
    pub async fn get_push_zone(&self, pushzone_id: &str) -> Result<VultrPushZone> {
        self.api_client.get_push_zone(pushzone_id).await
    }

    #[instrument(skip(self, request))]
    pub async fn update_push_zone(&self, pushzone_id: &str, request: UpdatePushZoneRequest) -> Result<VultrPushZone> {
        self.api_client.update_push_zone(pushzone_id, request).await
    }

    #[instrument(skip(self))]
    pub async fn delete_push_zone(&self, pushzone_id: &str) -> Result<()> {
        self.api_client.delete_push_zone(pushzone_id).await
    }

    #[instrument(skip(self))]
    pub async fn get_push_zone_files(&self, pushzone_id: &str) -> Result<Vec<VultrPushZoneFile>> {
        self.api_client.get_push_zone_files(pushzone_id).await
    }

    #[instrument(skip(self))]
    pub async fn delete_push_zone_file(&self, pushzone_id: &str, file_name: &str) -> Result<()> {
        self.api_client.delete_push_zone_file(pushzone_id, file_name).await
    }

    // Enhanced Container Registry methods
    #[instrument(skip(self, request))]
    pub async fn create_registry(&self, request: CreateRegistryRequest) -> Result<VultrContainerRegistry> {
        self.api_client.create_registry(request).await
    }

    #[instrument(skip(self, request))]
    pub async fn update_registry(&self, registry_id: &str, request: UpdateRegistryRequest) -> Result<VultrContainerRegistry> {
        self.api_client.update_registry(registry_id, request).await
    }

    #[instrument(skip(self))]
    pub async fn delete_registry(&self, registry_id: &str) -> Result<()> {
        self.api_client.delete_registry(registry_id).await
    }

    #[instrument(skip(self))]
    pub async fn list_registry_replications(&self, registry_id: &str) -> Result<Vec<VultrRegistryReplication>> {
        self.api_client.list_registry_replications(registry_id).await
    }

    #[instrument(skip(self, request))]
    pub async fn create_registry_replication(&self, registry_id: &str, request: CreateReplicationRequest) -> Result<VultrRegistryReplication> {
        self.api_client.create_registry_replication(registry_id, request).await
    }

    #[instrument(skip(self))]
    pub async fn get_registry_replication(&self, registry_id: &str, replication_id: &str) -> Result<VultrRegistryReplication> {
        self.api_client.get_registry_replication(registry_id, replication_id).await
    }

    #[instrument(skip(self))]
    pub async fn delete_registry_replication(&self, registry_id: &str, replication_id: &str) -> Result<()> {
        self.api_client.delete_registry_replication(registry_id, replication_id).await
    }

    #[instrument(skip(self))]
    pub async fn list_registry_repositories(&self, registry_id: &str) -> Result<Vec<VultrRegistryRepository>> {
        self.api_client.list_registry_repositories(registry_id).await
    }

    #[instrument(skip(self))]
    pub async fn get_registry_repository(&self, registry_id: &str, repository_image: &str) -> Result<VultrRegistryRepository> {
        self.api_client.get_registry_repository(registry_id, repository_image).await
    }

    #[instrument(skip(self, request))]
    pub async fn update_registry_repository(&self, registry_id: &str, repository_image: &str, request: UpdateRepositoryRequest) -> Result<VultrRegistryRepository> {
        self.api_client.update_registry_repository(registry_id, repository_image, request).await
    }

    #[instrument(skip(self))]
    pub async fn delete_registry_repository(&self, registry_id: &str, repository_image: &str) -> Result<()> {
        self.api_client.delete_registry_repository(registry_id, repository_image).await
    }

    #[instrument(skip(self))]
    pub async fn create_registry_docker_credentials(&self, registry_id: &str) -> Result<VultrDockerCredentials> {
        self.api_client.create_registry_docker_credentials(registry_id).await
    }

    #[instrument(skip(self))]
    pub async fn create_registry_kubernetes_docker_credentials(&self, registry_id: &str) -> Result<VultrKubernetesDockerCredentials> {
        self.api_client.create_registry_kubernetes_docker_credentials(registry_id).await
    }

    #[instrument(skip(self, request))]
    pub async fn update_registry_password(&self, registry_id: &str, request: UpdateRegistryPasswordRequest) -> Result<()> {
        self.api_client.update_registry_password(registry_id, request).await
    }

    #[instrument(skip(self))]
    pub async fn list_registry_robots(&self, registry_id: &str) -> Result<Vec<VultrRegistryRobot>> {
        self.api_client.list_registry_robots(registry_id).await
    }

    #[instrument(skip(self))]
    pub async fn get_registry_robot(&self, registry_id: &str, robot_name: &str) -> Result<VultrRegistryRobot> {
        self.api_client.get_registry_robot(registry_id, robot_name).await
    }

    #[instrument(skip(self, request))]
    pub async fn update_registry_robot(&self, registry_id: &str, robot_name: &str, request: UpdateRobotRequest) -> Result<VultrRegistryRobot> {
        self.api_client.update_registry_robot(registry_id, robot_name, request).await
    }

    #[instrument(skip(self))]
    pub async fn delete_registry_robot(&self, registry_id: &str, robot_name: &str) -> Result<()> {
        self.api_client.delete_registry_robot(registry_id, robot_name).await
    }

    #[instrument(skip(self))]
    pub async fn list_registry_repository_artifacts(&self, registry_id: &str, repository_image: &str) -> Result<Vec<VultrRegistryArtifact>> {
        self.api_client.list_registry_repository_artifacts(registry_id, repository_image).await
    }

    #[instrument(skip(self))]
    pub async fn get_registry_repository_artifact(&self, registry_id: &str, repository_image: &str, artifact_digest: &str) -> Result<VultrRegistryArtifact> {
        self.api_client.get_registry_repository_artifact(registry_id, repository_image, artifact_digest).await
    }

    #[instrument(skip(self))]
    pub async fn delete_registry_repository_artifact(&self, registry_id: &str, repository_image: &str, artifact_digest: &str) -> Result<()> {
        self.api_client.delete_registry_repository_artifact(registry_id, repository_image, artifact_digest).await
    }

    #[instrument(skip(self))]
    pub async fn list_registry_regions(&self) -> Result<Vec<VultrRegistryRegion>> {
        self.api_client.list_registry_regions().await
    }

    #[instrument(skip(self))]
    pub async fn list_registry_plans(&self) -> Result<Vec<VultrRegistryPlan>> {
        self.api_client.list_registry_plans().await
    }

    // Enhanced DNS methods
    #[instrument(skip(self))]
    pub async fn list_dns_domains(&self) -> Result<Vec<VultrDNSDomainDetailed>> {
        let domains = self.api_client.list_dns_domains().await?;
        // Convert VultrDNSDomain to VultrDNSDomainDetailed
        Ok(domains.into_iter().map(|d| VultrDNSDomainDetailed {
            domain: d.domain,
            date_created: d.date_created,
            dns_sec: "disabled".to_string(), // Default value since original doesn't have this field
        }).collect())
    }

    #[instrument(skip(self, request))]
    pub async fn create_dns_domain(&self, request: CreateDNSDomainRequest) -> Result<VultrDNSDomainDetailed> {
        self.api_client.create_dns_domain(request).await
    }

    #[instrument(skip(self))]
    pub async fn get_dns_domain(&self, domain: &str) -> Result<VultrDNSDomainDetailed> {
        self.api_client.get_dns_domain(domain).await
    }

    #[instrument(skip(self))]
    pub async fn delete_dns_domain(&self, domain: &str) -> Result<()> {
        self.api_client.delete_dns_domain(domain).await
    }

    #[instrument(skip(self, request))]
    pub async fn update_dns_domain(&self, domain: &str, request: UpdateDNSDomainRequest) -> Result<()> {
        self.api_client.update_dns_domain(domain, request).await
    }

    #[instrument(skip(self))]
    pub async fn get_dns_domain_soa(&self, domain: &str) -> Result<VultrDNSSOA> {
        self.api_client.get_dns_domain_soa(domain).await
    }

    #[instrument(skip(self, request))]
    pub async fn update_dns_domain_soa(&self, domain: &str, request: UpdateDNSSOARequest) -> Result<()> {
        self.api_client.update_dns_domain_soa(domain, request).await
    }

    #[instrument(skip(self))]
    pub async fn get_dns_domain_dnssec(&self, domain: &str) -> Result<Vec<String>> {
        self.api_client.get_dns_domain_dnssec(domain).await
    }

    #[instrument(skip(self))]
    pub async fn list_dns_domain_records(&self, domain: &str) -> Result<Vec<VultrDNSRecordDetailed>> {
        self.api_client.list_dns_domain_records(domain).await
    }

    #[instrument(skip(self, request))]
    pub async fn create_dns_domain_record(&self, domain: &str, request: CreateDNSRecordRequest) -> Result<VultrDNSRecordDetailed> {
        self.api_client.create_dns_domain_record(domain, request).await
    }

    #[instrument(skip(self))]
    pub async fn get_dns_domain_record(&self, domain: &str, record_id: &str) -> Result<VultrDNSRecordDetailed> {
        self.api_client.get_dns_domain_record(domain, record_id).await
    }

    #[instrument(skip(self, request))]
    pub async fn update_dns_domain_record(&self, domain: &str, record_id: &str, request: UpdateDNSRecordRequest) -> Result<()> {
        self.api_client.update_dns_domain_record(domain, record_id, request).await
    }

    #[instrument(skip(self))]
    pub async fn delete_dns_domain_record(&self, domain: &str, record_id: &str) -> Result<()> {
        self.api_client.delete_dns_domain_record(domain, record_id).await
    }

    // Enhanced Firewall methods
    #[instrument(skip(self))]
    pub async fn list_firewall_groups(&self) -> Result<Vec<VultrFirewallGroupDetailed>> {
        let groups = self.api_client.list_firewall_groups().await?;
        // Convert VultrFirewallGroup to VultrFirewallGroupDetailed
        Ok(groups.into_iter().map(|g| VultrFirewallGroupDetailed {
            id: g.id,
            description: g.description,
            date_created: g.date_created,
            date_modified: g.date_modified,
            instance_count: g.instance_count,
            rule_count: g.rule_count,
            max_rule_count: g.max_rule_count,
        }).collect())
    }

    #[instrument(skip(self, request))]
    pub async fn create_firewall_group(&self, request: CreateFirewallGroupRequest) -> Result<VultrFirewallGroupDetailed> {
        self.api_client.create_firewall_group(request).await
    }

    #[instrument(skip(self))]
    pub async fn get_firewall_group(&self, firewall_group_id: &str) -> Result<VultrFirewallGroupDetailed> {
        self.api_client.get_firewall_group(firewall_group_id).await
    }

    #[instrument(skip(self, request))]
    pub async fn update_firewall_group(&self, firewall_group_id: &str, request: UpdateFirewallGroupRequest) -> Result<()> {
        self.api_client.update_firewall_group(firewall_group_id, request).await
    }

    #[instrument(skip(self))]
    pub async fn delete_firewall_group(&self, firewall_group_id: &str) -> Result<()> {
        self.api_client.delete_firewall_group(firewall_group_id).await
    }

    #[instrument(skip(self))]
    pub async fn list_firewall_group_rules(&self, firewall_group_id: &str) -> Result<Vec<VultrFirewallRuleDetailed>> {
        self.api_client.list_firewall_group_rules(firewall_group_id).await
    }

    #[instrument(skip(self, request))]
    pub async fn create_firewall_group_rule(&self, firewall_group_id: &str, request: CreateFirewallRuleRequest) -> Result<VultrFirewallRuleDetailed> {
        self.api_client.create_firewall_group_rule(firewall_group_id, request).await
    }

    #[instrument(skip(self))]
    pub async fn get_firewall_group_rule(&self, firewall_group_id: &str, firewall_rule_id: &str) -> Result<VultrFirewallRuleDetailed> {
        self.api_client.get_firewall_group_rule(firewall_group_id, firewall_rule_id).await
    }

    #[instrument(skip(self))]
    pub async fn delete_firewall_group_rule(&self, firewall_group_id: &str, firewall_rule_id: &str) -> Result<()> {
        self.api_client.delete_firewall_group_rule(firewall_group_id, firewall_rule_id).await
    }

    // Enhanced Instance methods
    #[instrument(skip(self))]
    pub async fn list_instances_detailed(&self) -> Result<Vec<VultrInstanceDetailed>> {
        self.api_client.list_instances_detailed().await
    }

    #[instrument(skip(self, request))]
    pub async fn create_instance_detailed(&self, request: CreateInstanceRequest) -> Result<VultrInstanceDetailed> {
        self.api_client.create_instance_detailed(request).await
    }

    #[instrument(skip(self))]
    pub async fn get_instance_detailed(&self, instance_id: &str) -> Result<VultrInstanceDetailed> {
        self.api_client.get_instance_detailed(instance_id).await
    }

    #[instrument(skip(self, request))]
    pub async fn update_instance_detailed(&self, instance_id: &str, request: UpdateInstanceRequest) -> Result<VultrInstanceDetailed> {
        self.api_client.update_instance_detailed(instance_id, request).await
    }

    #[instrument(skip(self))]
    pub async fn delete_instance_detailed(&self, instance_id: &str) -> Result<()> {
        self.api_client.delete_instance_detailed(instance_id).await
    }

    #[instrument(skip(self))]
    pub async fn halt_instance(&self, instance_id: &str) -> Result<()> {
        self.api_client.halt_instance(instance_id).await
    }

    #[instrument(skip(self, request))]
    pub async fn halt_instances(&self, request: BulkInstanceActionRequest) -> Result<()> {
        self.api_client.halt_instances(request).await
    }

    #[instrument(skip(self))]
    pub async fn reboot_instance(&self, instance_id: &str) -> Result<()> {
        self.api_client.reboot_instance(instance_id).await
    }

    #[instrument(skip(self, request))]
    pub async fn reboot_instances(&self, request: BulkInstanceActionRequest) -> Result<()> {
        self.api_client.reboot_instances(request).await
    }

    #[instrument(skip(self))]


    #[instrument(skip(self, request))]
    pub async fn start_instances(&self, request: BulkInstanceActionRequest) -> Result<()> {
        self.api_client.start_instances(request).await
    }

    #[instrument(skip(self, request))]
    pub async fn reinstall_instance(&self, instance_id: &str, request: Option<serde_json::Value>) -> Result<VultrInstanceDetailed> {
        self.api_client.reinstall_instance(instance_id, request).await
    }

    #[instrument(skip(self))]
    pub async fn get_instance_bandwidth(&self, instance_id: &str) -> Result<HashMap<String, InstanceBandwidth>> {
        self.api_client.get_instance_bandwidth(instance_id).await
    }

    #[instrument(skip(self))]
    pub async fn get_instance_neighbors(&self, instance_id: &str) -> Result<Vec<InstanceNeighbor>> {
        self.api_client.get_instance_neighbors(instance_id).await
    }

    #[instrument(skip(self))]
    pub async fn list_instance_vpcs(&self, instance_id: &str) -> Result<Vec<InstanceVPC>> {
        self.api_client.list_instance_vpcs(instance_id).await
    }

    #[instrument(skip(self))]
    pub async fn get_instance_iso_status(&self, instance_id: &str) -> Result<InstanceISOStatus> {
        self.api_client.get_instance_iso_status(instance_id).await
    }

    #[instrument(skip(self, request))]
    pub async fn attach_instance_iso(&self, instance_id: &str, request: AttachISORequest) -> Result<()> {
        self.api_client.attach_instance_iso(instance_id, request).await
    }

    #[instrument(skip(self))]
    pub async fn detach_instance_iso(&self, instance_id: &str) -> Result<()> {
        self.api_client.detach_instance_iso(instance_id).await
    }

    #[instrument(skip(self, request))]
    pub async fn attach_instance_vpc(&self, instance_id: &str, request: AttachVPCRequest) -> Result<()> {
        self.api_client.attach_instance_vpc(instance_id, request).await
    }

    #[instrument(skip(self, request))]
    pub async fn detach_instance_vpc(&self, instance_id: &str, request: DetachVPCRequest) -> Result<()> {
        self.api_client.detach_instance_vpc(instance_id, request).await
    }

    #[instrument(skip(self, request))]
    pub async fn create_instance_backup_schedule(&self, instance_id: &str, request: CreateBackupScheduleRequest) -> Result<()> {
        self.api_client.create_instance_backup_schedule(instance_id, request).await
    }

    #[instrument(skip(self))]
    pub async fn get_instance_backup_schedule(&self, instance_id: &str) -> Result<InstanceBackupSchedule> {
        self.api_client.get_instance_backup_schedule(instance_id).await
    }

    #[instrument(skip(self, request))]
    pub async fn restore_instance(&self, instance_id: &str, request: RestoreInstanceRequest) -> Result<()> {
        self.api_client.restore_instance(instance_id, request).await
    }

    #[instrument(skip(self))]
    pub async fn get_instance_ipv4(&self, instance_id: &str) -> Result<Vec<InstanceIPv4>> {
        self.api_client.get_instance_ipv4(instance_id).await
    }

    #[instrument(skip(self, request))]
    pub async fn create_instance_ipv4(&self, instance_id: &str, request: CreateInstanceIPv4Request) -> Result<InstanceIPv4> {
        self.api_client.create_instance_ipv4(instance_id, request).await
    }

    #[instrument(skip(self))]
    pub async fn delete_instance_ipv4(&self, instance_id: &str, ipv4: &str) -> Result<()> {
        self.api_client.delete_instance_ipv4(instance_id, ipv4).await
    }

    #[instrument(skip(self))]
    pub async fn get_instance_ipv6(&self, instance_id: &str) -> Result<Vec<InstanceIPv6>> {
        self.api_client.get_instance_ipv6(instance_id).await
    }

    #[instrument(skip(self, request))]
    pub async fn create_instance_reverse_ipv6(&self, instance_id: &str, request: CreateReverseIPv6Request) -> Result<()> {
        self.api_client.create_instance_reverse_ipv6(instance_id, request).await
    }

    #[instrument(skip(self))]
    pub async fn list_instance_ipv6_reverse(&self, instance_id: &str) -> Result<Vec<InstanceIPv6Reverse>> {
        self.api_client.list_instance_ipv6_reverse(instance_id).await
    }

    #[instrument(skip(self))]
    pub async fn delete_instance_reverse_ipv6(&self, instance_id: &str, ipv6: &str) -> Result<()> {
        self.api_client.delete_instance_reverse_ipv6(instance_id, ipv6).await
    }

    #[instrument(skip(self, request))]
    pub async fn create_instance_reverse_ipv4(&self, instance_id: &str, request: CreateReverseIPv4Request) -> Result<()> {
        self.api_client.create_instance_reverse_ipv4(instance_id, request).await
    }

    #[instrument(skip(self))]
    pub async fn set_instance_default_reverse_ipv4(&self, instance_id: &str, ip: &str) -> Result<()> {
        self.api_client.set_instance_default_reverse_ipv4(instance_id, ip).await
    }

    #[instrument(skip(self))]
    pub async fn get_instance_userdata(&self, instance_id: &str) -> Result<InstanceUserData> {
        self.api_client.get_instance_userdata(instance_id).await
    }

    #[instrument(skip(self))]
    pub async fn get_instance_upgrades(&self, instance_id: &str) -> Result<InstanceUpgrades> {
        self.api_client.get_instance_upgrades(instance_id).await
    }

    #[instrument(skip(self))]
    pub async fn get_instance_job(&self, instance_id: &str, job_id: &str) -> Result<InstanceJob> {
        self.api_client.get_instance_job(instance_id, job_id).await
    }

    // ISO methods
    #[instrument(skip(self))]
    pub async fn list_isos(&self) -> Result<Vec<VultrISO>> {
        self.api_client.list_isos().await
    }

    #[instrument(skip(self, request))]
    pub async fn create_iso(&self, request: CreateISORequest) -> Result<VultrISO> {
        self.api_client.create_iso(request).await
    }

    #[instrument(skip(self))]
    pub async fn get_iso(&self, iso_id: &str) -> Result<VultrISO> {
        self.api_client.get_iso(iso_id).await
    }

    #[instrument(skip(self))]
    pub async fn delete_iso(&self, iso_id: &str) -> Result<()> {
        self.api_client.delete_iso(iso_id).await
    }

    #[instrument(skip(self))]
    pub async fn list_public_isos(&self) -> Result<Vec<VultrPublicISO>> {
        self.api_client.list_public_isos().await
    }

    // Enhanced Kubernetes methods
    #[instrument(skip(self, request))]
    pub async fn create_kubernetes_cluster(&self, request: CreateKubernetesClusterRequest) -> Result<VultrKubernetesClusterDetailed> {
        self.api_client.create_kubernetes_cluster(request).await
    }

    #[instrument(skip(self))]
    pub async fn list_kubernetes_clusters(&self) -> Result<Vec<VultrKubernetesClusterDetailed>> {
        let clusters = self.api_client.list_kubernetes_clusters().await?;
        // Convert VultrKubernetesCluster to VultrKubernetesClusterDetailed
        Ok(clusters.into_iter().map(|c| VultrKubernetesClusterDetailed {
            id: c.id,
            label: c.label,
            date_created: c.date_created,
            cluster_subnet: c.cluster_subnet,
            service_subnet: c.service_subnet,
            ip: c.ip,
            endpoint: c.endpoint,
            version: c.version,
            region: c.region,
            status: c.status,
            ha_controlplanes: false, // Default value since original doesn't have this field
            enable_firewall: false, // Default value since original doesn't have this field
            node_pools: c.node_pools.into_iter().map(|np| VultrNodePoolDetailed {
                id: np.id,
                date_created: np.date_created,
                date_updated: np.date_updated,
                label: np.label,
                tag: np.tag,
                plan: np.plan,
                status: np.status,
                node_quantity: np.node_quantity,
                min_nodes: np.min_nodes,
                max_nodes: np.max_nodes,
                auto_scaler: np.auto_scaler,
                nodes: np.nodes.into_iter().map(|n| VultrKubernetesNodeDetailed {
                    id: n.id,
                    date_created: n.date_created,
                    label: n.label,
                    status: n.status,
                }).collect(),
            }).collect(),
        }).collect())
    }

    #[instrument(skip(self))]
    pub async fn get_kubernetes_cluster(&self, vke_id: &str) -> Result<VultrKubernetesClusterDetailed> {
        let cluster = self.api_client.get_kubernetes_cluster(vke_id).await?;
        // Convert VultrKubernetesCluster to VultrKubernetesClusterDetailed
        Ok(VultrKubernetesClusterDetailed {
            id: cluster.id,
            label: cluster.label,
            date_created: cluster.date_created,
            cluster_subnet: cluster.cluster_subnet,
            service_subnet: cluster.service_subnet,
            ip: cluster.ip,
            endpoint: cluster.endpoint,
            version: cluster.version,
            region: cluster.region,
            status: cluster.status,
            ha_controlplanes: false, // Default value since original doesn't have this field
            enable_firewall: false, // Default value since original doesn't have this field
            node_pools: cluster.node_pools.into_iter().map(|np| VultrNodePoolDetailed {
                id: np.id,
                date_created: np.date_created,
                date_updated: np.date_updated,
                label: np.label,
                tag: np.tag,
                plan: np.plan,
                status: np.status,
                node_quantity: np.node_quantity,
                min_nodes: np.min_nodes,
                max_nodes: np.max_nodes,
                auto_scaler: np.auto_scaler,
                nodes: np.nodes.into_iter().map(|n| VultrKubernetesNodeDetailed {
                    id: n.id,
                    date_created: n.date_created,
                    label: n.label,
                    status: n.status,
                }).collect(),
            }).collect(),
        })
    }

    #[instrument(skip(self, request))]
    pub async fn update_kubernetes_cluster(&self, vke_id: &str, request: UpdateKubernetesClusterRequest) -> Result<VultrKubernetesClusterDetailed> {
        self.api_client.update_kubernetes_cluster(vke_id, request).await
    }

    #[instrument(skip(self))]
    pub async fn delete_kubernetes_cluster(&self, vke_id: &str) -> Result<()> {
        self.api_client.delete_kubernetes_cluster(vke_id).await
    }

    #[instrument(skip(self))]
    pub async fn delete_kubernetes_cluster_with_resources(&self, vke_id: &str) -> Result<()> {
        self.api_client.delete_kubernetes_cluster_with_resources(vke_id).await
    }

    #[instrument(skip(self))]
    pub async fn get_kubernetes_resources(&self, vke_id: &str) -> Result<VultrKubernetesResources> {
        self.api_client.get_kubernetes_resources(vke_id).await
    }

    #[instrument(skip(self))]
    pub async fn get_kubernetes_available_upgrades(&self, vke_id: &str) -> Result<VultrKubernetesUpgrades> {
        self.api_client.get_kubernetes_available_upgrades(vke_id).await
    }

    #[instrument(skip(self, request))]
    pub async fn start_kubernetes_cluster_upgrade(&self, vke_id: &str, request: StartKubernetesUpgradeRequest) -> Result<()> {
        self.api_client.start_kubernetes_cluster_upgrade(vke_id, request).await
    }

    #[instrument(skip(self, request))]
    pub async fn create_nodepool(&self, vke_id: &str, request: CreateNodePoolRequest) -> Result<VultrNodePoolDetailed> {
        self.api_client.create_nodepool(vke_id, request).await
    }

    #[instrument(skip(self))]
    pub async fn get_nodepools(&self, vke_id: &str) -> Result<Vec<VultrNodePoolDetailed>> {
        self.api_client.get_nodepools(vke_id).await
    }

    #[instrument(skip(self))]
    pub async fn get_nodepool(&self, vke_id: &str, nodepool_id: &str) -> Result<VultrNodePoolDetailed> {
        self.api_client.get_nodepool(vke_id, nodepool_id).await
    }

    #[instrument(skip(self, request))]
    pub async fn update_nodepool(&self, vke_id: &str, nodepool_id: &str, request: UpdateNodePoolRequest) -> Result<VultrNodePoolDetailed> {
        self.api_client.update_nodepool(vke_id, nodepool_id, request).await
    }

    #[instrument(skip(self))]
    pub async fn delete_nodepool(&self, vke_id: &str, nodepool_id: &str) -> Result<()> {
        self.api_client.delete_nodepool(vke_id, nodepool_id).await
    }

    #[instrument(skip(self))]
    pub async fn delete_nodepool_instance(&self, vke_id: &str, nodepool_id: &str, node_id: &str) -> Result<()> {
        self.api_client.delete_nodepool_instance(vke_id, nodepool_id, node_id).await
    }

    #[instrument(skip(self))]
    pub async fn recycle_nodepool_instance(&self, vke_id: &str, nodepool_id: &str, node_id: &str) -> Result<()> {
        self.api_client.recycle_nodepool_instance(vke_id, nodepool_id, node_id).await
    }

    #[instrument(skip(self))]
    pub async fn get_kubernetes_cluster_config(&self, vke_id: &str) -> Result<VultrKubernetesConfig> {
        self.api_client.get_kubernetes_cluster_config(vke_id).await
    }

    #[instrument(skip(self))]
    pub async fn get_kubernetes_versions(&self) -> Result<VultrKubernetesVersions> {
        self.api_client.get_kubernetes_versions().await
    }

    // Enhanced Load Balancer methods
    #[instrument(skip(self))]
    pub async fn list_load_balancers(&self) -> Result<Vec<VultrLoadBalancerDetailed>> {
        let lbs = self.api_client.list_load_balancers().await?;
        // Convert VultrLoadBalancer to VultrLoadBalancerDetailed
        Ok(lbs.into_iter().map(|lb| VultrLoadBalancerDetailed {
            id: lb.id,
            date_created: lb.date_created,
            region: lb.region,
            label: lb.label,
            status: lb.status,
            ipv4: lb.ipv4,
            ipv6: lb.ipv6,
            generic_info: VultrLoadBalancerGenericInfo {
                balancing_algorithm: "roundrobin".to_string(),
                ssl_redirect: false,
                sticky_sessions: VultrStickySessions { cookie_name: None },
                proxy_protocol: false,
                private_network: None,
                vpc: None,
            },
            health_check: VultrHealthCheck {
                protocol: "http".to_string(),
                port: 80,
                path: "/".to_string(),
                check_interval: 15,
                response_timeout: 5,
                unhealthy_threshold: 5,
                healthy_threshold: 3,
            },
            has_ssl: false,
            forwarding_rules: vec![],
            firewall_rules: vec![],
            http2: false,
            nodes: vec![],
        }).collect())
    }

    #[instrument(skip(self, request))]
    pub async fn create_load_balancer(&self, request: CreateLoadBalancerRequest) -> Result<VultrLoadBalancerDetailed> {
        let lb = self.api_client.create_load_balancer(request).await?;
        // Convert VultrLoadBalancer to VultrLoadBalancerDetailed
        Ok(VultrLoadBalancerDetailed {
            id: lb.id,
            date_created: lb.date_created,
            region: lb.region,
            label: lb.label,
            status: lb.status,
            ipv4: lb.ipv4,
            ipv6: lb.ipv6,
            generic_info: VultrLoadBalancerGenericInfo {
                balancing_algorithm: "roundrobin".to_string(),
                ssl_redirect: false,
                sticky_sessions: VultrStickySessions { cookie_name: None },
                proxy_protocol: false,
                private_network: None,
                vpc: None,
            },
            health_check: VultrHealthCheck {
                protocol: "http".to_string(),
                port: 80,
                path: "/".to_string(),
                check_interval: 15,
                response_timeout: 5,
                unhealthy_threshold: 5,
                healthy_threshold: 3,
            },
            has_ssl: false,
            forwarding_rules: vec![],
            firewall_rules: vec![],
            http2: false,
            nodes: vec![],
        })
    }

    #[instrument(skip(self))]
    pub async fn get_load_balancer(&self, load_balancer_id: &str) -> Result<VultrLoadBalancerDetailed> {
        let lb = self.api_client.get_load_balancer(load_balancer_id).await?;
        // Convert VultrLoadBalancer to VultrLoadBalancerDetailed
        Ok(VultrLoadBalancerDetailed {
            id: lb.id,
            date_created: lb.date_created,
            region: lb.region,
            label: lb.label,
            status: lb.status,
            ipv4: lb.ipv4,
            ipv6: lb.ipv6,
            generic_info: VultrLoadBalancerGenericInfo {
                balancing_algorithm: "roundrobin".to_string(),
                ssl_redirect: false,
                sticky_sessions: VultrStickySessions { cookie_name: None },
                proxy_protocol: false,
                private_network: None,
                vpc: None,
            },
            health_check: VultrHealthCheck {
                protocol: "http".to_string(),
                port: 80,
                path: "/".to_string(),
                check_interval: 15,
                response_timeout: 5,
                unhealthy_threshold: 5,
                healthy_threshold: 3,
            },
            has_ssl: false,
            forwarding_rules: vec![],
            firewall_rules: vec![],
            http2: false,
            nodes: vec![],
        })
    }

    #[instrument(skip(self, request))]
    pub async fn update_load_balancer(&self, load_balancer_id: &str, request: UpdateLoadBalancerRequest) -> Result<()> {
        self.api_client.update_load_balancer(load_balancer_id, request).await
    }

    #[instrument(skip(self))]
    pub async fn delete_load_balancer(&self, load_balancer_id: &str) -> Result<()> {
        self.api_client.delete_load_balancer(load_balancer_id).await
    }

    #[instrument(skip(self))]
    pub async fn delete_load_balancer_ssl(&self, load_balancer_id: &str) -> Result<()> {
        self.api_client.delete_load_balancer_ssl(load_balancer_id).await
    }

    #[instrument(skip(self))]
    pub async fn delete_load_balancer_auto_ssl(&self, load_balancer_id: &str) -> Result<()> {
        self.api_client.delete_load_balancer_auto_ssl(load_balancer_id).await
    }

    #[instrument(skip(self, request))]
    pub async fn create_load_balancer_forwarding_rule(&self, load_balancer_id: &str, request: CreateForwardingRuleRequest) -> Result<VultrForwardingRuleDetailed> {
        self.api_client.create_load_balancer_forwarding_rule(load_balancer_id, request).await
    }

    #[instrument(skip(self))]
    pub async fn get_load_balancer_forwarding_rule(&self, load_balancer_id: &str, forwarding_rule_id: &str) -> Result<VultrForwardingRuleDetailed> {
        self.api_client.get_load_balancer_forwarding_rule(load_balancer_id, forwarding_rule_id).await
    }

    #[instrument(skip(self))]
    pub async fn delete_load_balancer_forwarding_rule(&self, load_balancer_id: &str, forwarding_rule_id: &str) -> Result<()> {
        self.api_client.delete_load_balancer_forwarding_rule(load_balancer_id, forwarding_rule_id).await
    }

    #[instrument(skip(self))]
    pub async fn list_load_balancer_firewall_rules(&self, load_balancer_id: &str) -> Result<Vec<VultrLoadBalancerFirewallRule>> {
        self.api_client.list_load_balancer_firewall_rules(load_balancer_id).await
    }

    #[instrument(skip(self))]
    pub async fn get_load_balancer_firewall_rule(&self, load_balancer_id: &str, firewall_rule_id: &str) -> Result<VultrLoadBalancerFirewallRule> {
        self.api_client.get_load_balancer_firewall_rule(load_balancer_id, firewall_rule_id).await
    }

    // Managed Database methods
    #[instrument(skip(self))]
    pub async fn list_database_plans(&self) -> Result<Vec<VultrDatabasePlan>> {
        self.api_client.list_database_plans().await
    }

    #[instrument(skip(self))]
    pub async fn list_managed_databases(&self) -> Result<Vec<VultrManagedDatabase>> {
        self.api_client.list_managed_databases().await
    }

    #[instrument(skip(self, request))]
    pub async fn create_managed_database(&self, request: CreateManagedDatabaseRequest) -> Result<VultrManagedDatabase> {
        self.api_client.create_managed_database(request).await
    }

    #[instrument(skip(self))]
    pub async fn get_managed_database(&self, database_id: &str) -> Result<VultrManagedDatabase> {
        self.api_client.get_managed_database(database_id).await
    }

    #[instrument(skip(self, request))]
    pub async fn update_managed_database(&self, database_id: &str, request: UpdateManagedDatabaseRequest) -> Result<VultrManagedDatabase> {
        self.api_client.update_managed_database(database_id, request).await
    }

    #[instrument(skip(self))]
    pub async fn delete_managed_database(&self, database_id: &str) -> Result<()> {
        self.api_client.delete_managed_database(database_id).await
    }

    #[instrument(skip(self))]
    pub async fn get_database_usage(&self, database_id: &str) -> Result<VultrDatabaseUsage> {
        self.api_client.get_database_usage(database_id).await
    }

    // Database User methods
    #[instrument(skip(self))]
    pub async fn list_database_users(&self, database_id: &str) -> Result<Vec<VultrDatabaseUser>> {
        self.api_client.list_database_users(database_id).await
    }

    #[instrument(skip(self, request))]
    pub async fn create_database_user(&self, database_id: &str, request: CreateDatabaseUserRequest) -> Result<VultrDatabaseUser> {
        self.api_client.create_database_user(database_id, request).await
    }

    #[instrument(skip(self))]
    pub async fn get_database_user(&self, database_id: &str, username: &str) -> Result<VultrDatabaseUser> {
        self.api_client.get_database_user(database_id, username).await
    }

    #[instrument(skip(self, request))]
    pub async fn update_database_user(&self, database_id: &str, username: &str, request: UpdateDatabaseUserRequest) -> Result<VultrDatabaseUser> {
        self.api_client.update_database_user(database_id, username, request).await
    }

    #[instrument(skip(self))]
    pub async fn delete_database_user(&self, database_id: &str, username: &str) -> Result<()> {
        self.api_client.delete_database_user(database_id, username).await
    }

    #[instrument(skip(self, request))]
    pub async fn set_database_user_acl(&self, database_id: &str, username: &str, request: SetDatabaseUserACLRequest) -> Result<()> {
        self.api_client.set_database_user_acl(database_id, username, request).await
    }

    // Database DB methods
    #[instrument(skip(self))]
    pub async fn list_database_dbs(&self, database_id: &str) -> Result<Vec<VultrDatabaseDB>> {
        self.api_client.list_database_dbs(database_id).await
    }

    #[instrument(skip(self, request))]
    pub async fn create_database_db(&self, database_id: &str, request: CreateDatabaseDBRequest) -> Result<VultrDatabaseDB> {
        self.api_client.create_database_db(database_id, request).await
    }

    #[instrument(skip(self))]
    pub async fn get_database_db(&self, database_id: &str, db_name: &str) -> Result<VultrDatabaseDB> {
        self.api_client.get_database_db(database_id, db_name).await
    }

    #[instrument(skip(self))]
    pub async fn delete_database_db(&self, database_id: &str, db_name: &str) -> Result<()> {
        self.api_client.delete_database_db(database_id, db_name).await
    }

    // Database Topic methods (Kafka)
    #[instrument(skip(self))]
    pub async fn list_database_topics(&self, database_id: &str) -> Result<Vec<VultrDatabaseTopic>> {
        self.api_client.list_database_topics(database_id).await
    }

    #[instrument(skip(self, request))]
    pub async fn create_database_topic(&self, database_id: &str, request: CreateDatabaseTopicRequest) -> Result<VultrDatabaseTopic> {
        self.api_client.create_database_topic(database_id, request).await
    }

    #[instrument(skip(self))]
    pub async fn get_database_topic(&self, database_id: &str, topic_name: &str) -> Result<VultrDatabaseTopic> {
        self.api_client.get_database_topic(database_id, topic_name).await
    }

    #[instrument(skip(self, request))]
    pub async fn update_database_topic(&self, database_id: &str, topic_name: &str, request: UpdateDatabaseTopicRequest) -> Result<VultrDatabaseTopic> {
        self.api_client.update_database_topic(database_id, topic_name, request).await
    }

    #[instrument(skip(self))]
    pub async fn delete_database_topic(&self, database_id: &str, topic_name: &str) -> Result<()> {
        self.api_client.delete_database_topic(database_id, topic_name).await
    }

    // Database Quota methods (Kafka)
    #[instrument(skip(self))]
    pub async fn list_database_quotas(&self, database_id: &str) -> Result<Vec<VultrDatabaseQuota>> {
        self.api_client.list_database_quotas(database_id).await
    }

    #[instrument(skip(self, request))]
    pub async fn create_database_quota(&self, database_id: &str, request: CreateDatabaseQuotaRequest) -> Result<VultrDatabaseQuota> {
        self.api_client.create_database_quota(database_id, request).await
    }

    #[instrument(skip(self))]
    pub async fn get_database_quota(&self, database_id: &str, client_id: &str, user: &str) -> Result<VultrDatabaseQuota> {
        self.api_client.get_database_quota(database_id, client_id, user).await
    }

    #[instrument(skip(self))]
    pub async fn delete_database_quota(&self, database_id: &str, client_id: &str, user: &str) -> Result<()> {
        self.api_client.delete_database_quota(database_id, client_id, user).await
    }

    // Database Connector methods (Kafka)
    #[instrument(skip(self))]
    pub async fn list_database_available_connectors(&self, database_id: &str) -> Result<Vec<String>> {
        self.api_client.list_database_available_connectors(database_id).await
    }

    #[instrument(skip(self))]
    pub async fn get_database_connector_configuration_schema(&self, database_id: &str, connector_name: &str) -> Result<serde_json::Value> {
        self.api_client.get_database_connector_configuration_schema(database_id, connector_name).await
    }

    #[instrument(skip(self))]
    pub async fn list_database_connectors(&self, database_id: &str) -> Result<Vec<VultrDatabaseConnector>> {
        self.api_client.list_database_connectors(database_id).await
    }

    #[instrument(skip(self, request))]
    pub async fn create_database_connector(&self, database_id: &str, request: CreateDatabaseConnectorRequest) -> Result<VultrDatabaseConnector> {
        self.api_client.create_database_connector(database_id, request).await
    }

    #[instrument(skip(self))]
    pub async fn get_database_connector(&self, database_id: &str, connector_name: &str) -> Result<VultrDatabaseConnector> {
        self.api_client.get_database_connector(database_id, connector_name).await
    }

    #[instrument(skip(self, request))]
    pub async fn update_database_connector(&self, database_id: &str, connector_name: &str, request: UpdateDatabaseConnectorRequest) -> Result<VultrDatabaseConnector> {
        self.api_client.update_database_connector(database_id, connector_name, request).await
    }

    #[instrument(skip(self))]
    pub async fn delete_database_connector(&self, database_id: &str, connector_name: &str) -> Result<()> {
        self.api_client.delete_database_connector(database_id, connector_name).await
    }

    #[instrument(skip(self))]
    pub async fn get_database_connector_status(&self, database_id: &str, connector_name: &str) -> Result<VultrDatabaseConnectorStatus> {
        self.api_client.get_database_connector_status(database_id, connector_name).await
    }

    #[instrument(skip(self))]
    pub async fn restart_database_connector(&self, database_id: &str, connector_name: &str) -> Result<()> {
        self.api_client.restart_database_connector(database_id, connector_name).await
    }

    #[instrument(skip(self))]
    pub async fn pause_database_connector(&self, database_id: &str, connector_name: &str) -> Result<()> {
        self.api_client.pause_database_connector(database_id, connector_name).await
    }

    #[instrument(skip(self))]
    pub async fn resume_database_connector(&self, database_id: &str, connector_name: &str) -> Result<()> {
        self.api_client.resume_database_connector(database_id, connector_name).await
    }

    #[instrument(skip(self))]
    pub async fn restart_database_connector_task(&self, database_id: &str, connector_name: &str, task_id: u32) -> Result<()> {
        self.api_client.restart_database_connector_task(database_id, connector_name, task_id).await
    }

    // Database Maintenance and Migration methods
    #[instrument(skip(self))]
    pub async fn list_database_maintenance_updates(&self, database_id: &str) -> Result<Vec<VultrDatabaseMaintenanceUpdate>> {
        self.api_client.list_database_maintenance_updates(database_id).await
    }

    #[instrument(skip(self))]
    pub async fn start_database_maintenance_updates(&self, database_id: &str) -> Result<()> {
        self.api_client.start_database_maintenance_updates(database_id).await
    }

    #[instrument(skip(self))]
    pub async fn list_database_service_alerts(&self, database_id: &str) -> Result<Vec<VultrDatabaseServiceAlert>> {
        self.api_client.list_database_service_alerts(database_id).await
    }

    #[instrument(skip(self))]
    pub async fn view_database_migration_status(&self, database_id: &str) -> Result<VultrDatabaseMigrationStatus> {
        self.api_client.view_database_migration_status(database_id).await
    }

    #[instrument(skip(self, request))]
    pub async fn start_database_migration(&self, database_id: &str, request: StartDatabaseMigrationRequest) -> Result<VultrDatabaseMigrationStatus> {
        self.api_client.start_database_migration(database_id, request).await
    }

    #[instrument(skip(self))]
    pub async fn detach_database_migration(&self, database_id: &str) -> Result<()> {
        self.api_client.detach_database_migration(database_id).await
    }

    #[instrument(skip(self, request))]
    pub async fn add_database_read_replica(&self, database_id: &str, request: AddDatabaseReadReplicaRequest) -> Result<VultrDatabaseReadReplica> {
        self.api_client.add_database_read_replica(database_id, request).await
    }

    #[instrument(skip(self))]
    pub async fn promote_database_read_replica(&self, database_id: &str) -> Result<()> {
        self.api_client.promote_database_read_replica(database_id).await
    }

    #[instrument(skip(self))]
    pub async fn get_database_backup_information(&self, database_id: &str) -> Result<VultrDatabaseBackupInfo> {
        self.api_client.get_database_backup_information(database_id).await
    }

    #[instrument(skip(self, request))]
    pub async fn restore_database_from_backup(&self, database_id: &str, request: RestoreDatabaseFromBackupRequest) -> Result<VultrManagedDatabase> {
        self.api_client.restore_database_from_backup(database_id, request).await
    }

    #[instrument(skip(self, request))]
    pub async fn fork_database(&self, database_id: &str, request: ForkDatabaseRequest) -> Result<VultrManagedDatabase> {
        self.api_client.fork_database(database_id, request).await
    }

    // Database Connection Pool methods
    #[instrument(skip(self))]
    pub async fn list_database_connection_pools(&self, database_id: &str) -> Result<Vec<VultrDatabaseConnectionPool>> {
        self.api_client.list_database_connection_pools(database_id).await
    }

    #[instrument(skip(self, request))]
    pub async fn create_database_connection_pool(&self, database_id: &str, request: CreateDatabaseConnectionPoolRequest) -> Result<VultrDatabaseConnectionPool> {
        self.api_client.create_database_connection_pool(database_id, request).await
    }

    #[instrument(skip(self))]
    pub async fn get_database_connection_pool(&self, database_id: &str, pool_name: &str) -> Result<VultrDatabaseConnectionPool> {
        self.api_client.get_database_connection_pool(database_id, pool_name).await
    }

    #[instrument(skip(self, request))]
    pub async fn update_database_connection_pool(&self, database_id: &str, pool_name: &str, request: UpdateDatabaseConnectionPoolRequest) -> Result<VultrDatabaseConnectionPool> {
        self.api_client.update_database_connection_pool(database_id, pool_name, request).await
    }

    #[instrument(skip(self))]
    pub async fn delete_database_connection_pool(&self, database_id: &str, pool_name: &str) -> Result<()> {
        self.api_client.delete_database_connection_pool(database_id, pool_name).await
    }

    // Database Advanced Options methods
    #[instrument(skip(self))]
    pub async fn list_database_advanced_options(&self, database_id: &str) -> Result<VultrDatabaseAdvancedOptions> {
        self.api_client.list_database_advanced_options(database_id).await
    }

    #[instrument(skip(self, request))]
    pub async fn update_database_advanced_options(&self, database_id: &str, request: VultrDatabaseAdvancedOptions) -> Result<VultrDatabaseAdvancedOptions> {
        self.api_client.update_database_advanced_options(database_id, request).await
    }

    // Database Version methods
    #[instrument(skip(self))]
    pub async fn list_database_available_versions(&self, database_id: &str) -> Result<VultrDatabaseVersions> {
        self.api_client.list_database_available_versions(database_id).await
    }

    #[instrument(skip(self, request))]
    pub async fn start_database_version_upgrade(&self, database_id: &str, request: StartDatabaseVersionUpgradeRequest) -> Result<()> {
        self.api_client.start_database_version_upgrade(database_id, request).await
    }

    // Marketplace methods
    #[instrument(skip(self))]
    pub async fn list_marketplace_app_variables(&self, image_id: &str) -> Result<Vec<VultrMarketplaceAppVariable>> {
        self.api_client.list_marketplace_app_variables(image_id).await
    }

    // Object Storage (S3) methods
    #[instrument(skip(self))]
    pub async fn list_object_storages(&self) -> Result<Vec<VultrObjectStorage>> {
        self.api_client.list_object_storages().await
    }

    #[instrument(skip(self, request))]
    pub async fn create_object_storage(&self, request: CreateObjectStorageRequest) -> Result<VultrObjectStorage> {
        self.api_client.create_object_storage(request).await
    }

    #[instrument(skip(self))]
    pub async fn get_object_storage(&self, object_storage_id: &str) -> Result<VultrObjectStorage> {
        self.api_client.get_object_storage(object_storage_id).await
    }

    #[instrument(skip(self))]
    pub async fn delete_object_storage(&self, object_storage_id: &str) -> Result<()> {
        self.api_client.delete_object_storage(object_storage_id).await
    }

    #[instrument(skip(self, request))]
    pub async fn update_object_storage(&self, object_storage_id: &str, request: UpdateObjectStorageRequest) -> Result<VultrObjectStorage> {
        self.api_client.update_object_storage(object_storage_id, request).await
    }

    #[instrument(skip(self))]
    pub async fn regenerate_object_storage_keys(&self, object_storage_id: &str) -> Result<VultrObjectStorage> {
        self.api_client.regenerate_object_storage_keys(object_storage_id).await
    }

    #[instrument(skip(self))]
    pub async fn list_object_storage_clusters(&self) -> Result<Vec<VultrObjectStorageCluster>> {
        self.api_client.list_object_storage_clusters().await
    }

    #[instrument(skip(self))]
    pub async fn list_object_storage_tiers(&self) -> Result<Vec<VultrObjectStorageTier>> {
        self.api_client.list_object_storage_tiers().await
    }

    #[instrument(skip(self))]
    pub async fn list_object_storage_cluster_tiers(&self, cluster_id: u32) -> Result<Vec<VultrObjectStorageTier>> {
        self.api_client.list_object_storage_cluster_tiers(cluster_id).await
    }

    // Operating System methods
    #[instrument(skip(self))]
    pub async fn list_os(&self) -> Result<Vec<VultrOS>> {
        self.api_client.list_os().await
    }

    // Plans methods
    #[instrument(skip(self))]
    pub async fn list_plans(&self) -> Result<Vec<VultrPlan>> {
        self.api_client.list_plans().await
    }

    #[instrument(skip(self))]
    pub async fn list_metal_plans(&self) -> Result<Vec<VultrMetalPlan>> {
        self.api_client.list_metal_plans().await
    }

    // Serverless Inference methods
    #[instrument(skip(self))]
    pub async fn list_inference(&self) -> Result<Vec<VultrInference>> {
        self.api_client.list_inference().await
    }

    #[instrument(skip(self, request))]
    pub async fn create_inference(&self, request: CreateInferenceRequest) -> Result<VultrInference> {
        self.api_client.create_inference(request).await
    }

    #[instrument(skip(self))]
    pub async fn get_inference(&self, inference_id: &str) -> Result<VultrInference> {
        self.api_client.get_inference(inference_id).await
    }

    #[instrument(skip(self, request))]
    pub async fn update_inference(&self, inference_id: &str, request: UpdateInferenceRequest) -> Result<VultrInference> {
        self.api_client.update_inference(inference_id, request).await
    }

    #[instrument(skip(self))]
    pub async fn delete_inference(&self, inference_id: &str) -> Result<()> {
        self.api_client.delete_inference(inference_id).await
    }

    #[instrument(skip(self))]
    pub async fn get_inference_usage(&self, inference_id: &str) -> Result<VultrInferenceUsage> {
        self.api_client.get_inference_usage(inference_id).await
    }

    // VPC methods
    #[instrument(skip(self))]
    pub async fn list_vpcs(&self) -> Result<Vec<VultrVPCDetailed>> {
        let vpcs = self.api_client.list_vpcs().await?;
        // Convert VultrVPC to VultrVPCDetailed
        Ok(vpcs.into_iter().map(|vpc| VultrVPCDetailed {
            id: vpc.id,
            date_created: vpc.date_created,
            region: vpc.region,
            description: vpc.description,
            v4_subnet: vpc.v4_subnet,
            v4_subnet_mask: vpc.v4_subnet_mask,
        }).collect())
    }

    #[instrument(skip(self, request))]
    pub async fn create_vpc(&self, request: CreateVPCRequest) -> Result<VultrVPCDetailed> {
        self.api_client.create_vpc(request).await
    }

    #[instrument(skip(self))]
    pub async fn get_vpc(&self, vpc_id: &str) -> Result<VultrVPCDetailed> {
        self.api_client.get_vpc(vpc_id).await
    }

    #[instrument(skip(self, request))]
    pub async fn update_vpc(&self, vpc_id: &str, request: UpdateVPCRequest) -> Result<()> {
        self.api_client.update_vpc(vpc_id, request).await
    }

    #[instrument(skip(self))]
    pub async fn delete_vpc(&self, vpc_id: &str) -> Result<()> {
        self.api_client.delete_vpc(vpc_id).await
    }

    // Reserved IP methods
    #[instrument(skip(self))]
    pub async fn list_reserved_ips(&self) -> Result<Vec<VultrReservedIPDetailed>> {
        let ips = self.api_client.list_reserved_ips().await?;
        // Convert VultrReservedIP to VultrReservedIPDetailed
        Ok(ips.into_iter().map(|ip| VultrReservedIPDetailed {
            id: ip.id,
            region: ip.region,
            ip_type: ip.ip_type,
            subnet: ip.subnet,
            subnet_size: ip.subnet_size,
            label: ip.label,
            instance_id: ip.instance_id,
        }).collect())
    }

    #[instrument(skip(self, request))]
    pub async fn create_reserved_ip(&self, request: CreateReservedIPRequest) -> Result<VultrReservedIPDetailed> {
        self.api_client.create_reserved_ip(request).await
    }

    #[instrument(skip(self))]
    pub async fn get_reserved_ip(&self, reserved_ip: &str) -> Result<VultrReservedIPDetailed> {
        self.api_client.get_reserved_ip(reserved_ip).await
    }

    #[instrument(skip(self, request))]
    pub async fn update_reserved_ip(&self, reserved_ip: &str, request: UpdateReservedIPRequest) -> Result<VultrReservedIPDetailed> {
        self.api_client.update_reserved_ip(reserved_ip, request).await
    }

    #[instrument(skip(self))]
    pub async fn delete_reserved_ip(&self, reserved_ip: &str) -> Result<()> {
        self.api_client.delete_reserved_ip(reserved_ip).await
    }

    #[instrument(skip(self, request))]
    pub async fn attach_reserved_ip(&self, reserved_ip: &str, request: AttachReservedIPRequest) -> Result<()> {
        self.api_client.attach_reserved_ip(reserved_ip, request).await
    }

    #[instrument(skip(self))]
    pub async fn detach_reserved_ip(&self, reserved_ip: &str) -> Result<()> {
        self.api_client.detach_reserved_ip(reserved_ip).await
    }

    #[instrument(skip(self, request))]
    pub async fn convert_reserved_ip(&self, request: ConvertReservedIPRequest) -> Result<VultrReservedIPDetailed> {
        self.api_client.convert_reserved_ip(request).await
    }

    // Region methods
    #[instrument(skip(self))]
    pub async fn list_regions(&self) -> Result<Vec<VultrRegion>> {
        self.api_client.list_regions().await
    }

    #[instrument(skip(self))]
    pub async fn list_available_plans_region(&self, region_id: &str) -> Result<VultrRegionAvailablePlans> {
        self.api_client.list_available_plans_region(region_id).await
    }

    // Snapshot methods
    #[instrument(skip(self))]
    pub async fn list_snapshots(&self) -> Result<Vec<VultrSnapshot>> {
        self.api_client.list_snapshots().await
    }

    #[instrument(skip(self, request))]
    pub async fn create_snapshot(&self, request: CreateSnapshotRequest) -> Result<VultrSnapshot> {
        self.api_client.create_snapshot(request).await
    }

    #[instrument(skip(self, request))]
    pub async fn create_snapshot_from_url(&self, request: CreateSnapshotFromUrlRequest) -> Result<VultrSnapshot> {
        self.api_client.create_snapshot_from_url(request).await
    }

    #[instrument(skip(self))]
    pub async fn get_snapshot(&self, snapshot_id: &str) -> Result<VultrSnapshot> {
        self.api_client.get_snapshot(snapshot_id).await
    }

    #[instrument(skip(self, request))]
    pub async fn update_snapshot(&self, snapshot_id: &str, request: UpdateSnapshotRequest) -> Result<VultrSnapshot> {
        self.api_client.update_snapshot(snapshot_id, request).await
    }

    #[instrument(skip(self))]
    pub async fn delete_snapshot(&self, snapshot_id: &str) -> Result<()> {
        self.api_client.delete_snapshot(snapshot_id).await
    }

    // Subaccount methods
    #[instrument(skip(self))]
    pub async fn list_subaccounts(&self) -> Result<Vec<VultrSubaccount>> {
        self.api_client.list_subaccounts().await
    }

    #[instrument(skip(self, request))]
    pub async fn create_subaccount(&self, request: CreateSubaccountRequest) -> Result<VultrSubaccount> {
        self.api_client.create_subaccount(request).await
    }

    // SSH Key methods
    #[instrument(skip(self))]
    pub async fn list_ssh_keys(&self) -> Result<Vec<VultrSSHKey>> {
        self.api_client.list_ssh_keys().await
    }

    #[instrument(skip(self, request))]
    pub async fn create_ssh_key(&self, request: CreateSSHKeyRequest) -> Result<VultrSSHKey> {
        self.api_client.create_ssh_key(request).await
    }

    #[instrument(skip(self))]
    pub async fn get_ssh_key(&self, ssh_key_id: &str) -> Result<VultrSSHKey> {
        self.api_client.get_ssh_key(ssh_key_id).await
    }

    #[instrument(skip(self, request))]
    pub async fn update_ssh_key(&self, ssh_key_id: &str, request: UpdateSSHKeyRequest) -> Result<VultrSSHKey> {
        self.api_client.update_ssh_key(ssh_key_id, request).await
    }

    #[instrument(skip(self))]
    pub async fn delete_ssh_key(&self, ssh_key_id: &str) -> Result<()> {
        self.api_client.delete_ssh_key(ssh_key_id).await
    }

    // Startup Script methods
    #[instrument(skip(self))]
    pub async fn list_startup_scripts(&self) -> Result<Vec<VultrStartupScript>> {
        self.api_client.list_startup_scripts().await
    }

    #[instrument(skip(self, request))]
    pub async fn create_startup_script(&self, request: CreateStartupScriptRequest) -> Result<VultrStartupScript> {
        self.api_client.create_startup_script(request).await
    }

    #[instrument(skip(self))]
    pub async fn get_startup_script(&self, script_id: &str) -> Result<VultrStartupScript> {
        self.api_client.get_startup_script(script_id).await
    }

    #[instrument(skip(self, request))]
    pub async fn update_startup_script(&self, script_id: &str, request: UpdateStartupScriptRequest) -> Result<VultrStartupScript> {
        self.api_client.update_startup_script(script_id, request).await
    }

    #[instrument(skip(self))]
    pub async fn delete_startup_script(&self, script_id: &str) -> Result<()> {
        self.api_client.delete_startup_script(script_id).await
    }

    // Storage Gateway methods
    #[instrument(skip(self))]
    pub async fn list_storage_gateways(&self) -> Result<Vec<VultrStorageGateway>> {
        self.api_client.list_storage_gateways().await
    }

    #[instrument(skip(self, request))]
    pub async fn create_storage_gateway(&self, request: CreateStorageGatewayRequest) -> Result<VultrStorageGateway> {
        self.api_client.create_storage_gateway(request).await
    }

    #[instrument(skip(self))]
    pub async fn get_storage_gateway(&self, gateway_id: &str) -> Result<VultrStorageGateway> {
        self.api_client.get_storage_gateway(gateway_id).await
    }

    #[instrument(skip(self, request))]
    pub async fn update_storage_gateway(&self, gateway_id: &str, request: UpdateStorageGatewayRequest) -> Result<VultrStorageGateway> {
        self.api_client.update_storage_gateway(gateway_id, request).await
    }

    #[instrument(skip(self))]
    pub async fn delete_storage_gateway(&self, gateway_id: &str) -> Result<()> {
        self.api_client.delete_storage_gateway(gateway_id).await
    }

    #[instrument(skip(self, request))]
    pub async fn add_storage_gateway_export(&self, gateway_id: &str, request: AddStorageGatewayExportRequest) -> Result<VultrStorageGatewayExport> {
        self.api_client.add_storage_gateway_export(gateway_id, request).await
    }

    #[instrument(skip(self))]
    pub async fn delete_storage_gateway_export(&self, gateway_id: &str, export_id: &str) -> Result<()> {
        self.api_client.delete_storage_gateway_export(gateway_id, export_id).await
    }

    // User methods
    #[instrument(skip(self))]
    pub async fn list_users(&self) -> Result<Vec<VultrUser>> {
        self.api_client.list_users().await
    }

    #[instrument(skip(self, request))]
    pub async fn create_user(&self, request: CreateUserRequest) -> Result<VultrUser> {
        self.api_client.create_user(request).await
    }

    #[instrument(skip(self))]
    pub async fn get_user(&self, user_id: &str) -> Result<VultrUser> {
        self.api_client.get_user(user_id).await
    }

    #[instrument(skip(self, request))]
    pub async fn update_user(&self, user_id: &str, request: UpdateUserRequest) -> Result<VultrUser> {
        self.api_client.update_user(user_id, request).await
    }

    #[instrument(skip(self))]
    pub async fn delete_user(&self, user_id: &str) -> Result<()> {
        self.api_client.delete_user(user_id).await
    }

    // VFS (Vultr File System) methods
    #[instrument(skip(self))]
    pub async fn list_vfs_regions(&self) -> Result<Vec<VultrRegion>> {
        self.api_client.list_vfs_regions().await
    }

    #[instrument(skip(self))]
    pub async fn list_vfs(&self) -> Result<Vec<VultrVFS>> {
        self.api_client.list_vfs().await
    }

    #[instrument(skip(self, request))]
    pub async fn create_vfs(&self, request: CreateVFSRequest) -> Result<VultrVFS> {
        self.api_client.create_vfs(request).await
    }

    #[instrument(skip(self))]
    pub async fn get_vfs(&self, vfs_id: &str) -> Result<VultrVFS> {
        self.api_client.get_vfs(vfs_id).await
    }

    #[instrument(skip(self, request))]
    pub async fn update_vfs(&self, vfs_id: &str, request: UpdateVFSRequest) -> Result<VultrVFS> {
        self.api_client.update_vfs(vfs_id, request).await
    }

    #[instrument(skip(self))]
    pub async fn delete_vfs(&self, vfs_id: &str) -> Result<()> {
        self.api_client.delete_vfs(vfs_id).await
    }

    #[instrument(skip(self))]
    pub async fn list_vfs_attachments(&self, vfs_id: &str) -> Result<Vec<VultrVFSAttachment>> {
        self.api_client.list_vfs_attachments(vfs_id).await
    }

    #[instrument(skip(self, request))]
    pub async fn create_vfs_attachment(&self, vfs_id: &str, request: CreateVFSAttachmentRequest) -> Result<VultrVFSAttachment> {
        self.api_client.create_vfs_attachment(vfs_id, request).await
    }

    #[instrument(skip(self))]
    pub async fn get_vfs_attachment(&self, vfs_id: &str, attachment_id: &str) -> Result<VultrVFSAttachment> {
        self.api_client.get_vfs_attachment(vfs_id, attachment_id).await
    }

    #[instrument(skip(self))]
    pub async fn delete_vfs_attachment(&self, vfs_id: &str, attachment_id: &str) -> Result<()> {
        self.api_client.delete_vfs_attachment(vfs_id, attachment_id).await
    }
}
