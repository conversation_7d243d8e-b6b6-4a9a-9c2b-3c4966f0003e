{"rustc": 2830703817519440116, "features": "[\"client\", \"h2\", \"http1\", \"http2\", \"runtime\", \"server\", \"socket2\", \"tcp\"]", "declared_features": "[\"__internal_happy_eyeballs_tests\", \"backports\", \"client\", \"default\", \"deprecated\", \"ffi\", \"full\", \"h2\", \"http1\", \"http2\", \"libc\", \"nightly\", \"runtime\", \"server\", \"socket2\", \"stream\", \"tcp\"]", "target": 5299595107718448861, "profile": 2241668132362809309, "path": 10938701176039163404, "deps": [[784494742817713399, "tower_service", false, 13980336227272009489], [1569313478171189446, "want", false, 9550972500464892507], [1811549171721445101, "futures_channel", false, 8930785311309996714], [1906322745568073236, "pin_project_lite", false, 13886474930693114932], [4405182208873388884, "http", false, 17638096306642354227], [6163892036024256188, "httparse", false, 18431962046823446980], [6304235478050270880, "httpdate", false, 9722987246176728873], [7620660491849607393, "futures_core", false, 9910657298933926885], [7695812897323945497, "itoa", false, 4893506542226318205], [8606274917505247608, "tracing", false, 13822962143594336692], [8915503303801890683, "http_body", false, 7113186864810676009], [9538054652646069845, "tokio", false, 4325870631136706026], [10629569228670356391, "futures_util", false, 10549570323539430975], [12614995553916589825, "socket2", false, 5295776572399356321], [13809605890706463735, "h2", false, 1947520839050327309], [16066129441945555748, "bytes", false, 2617022058346697638]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\hyper-3f307c2034710544\\dep-lib-hyper", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}