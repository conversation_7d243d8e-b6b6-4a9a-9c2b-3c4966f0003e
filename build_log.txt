cargo :    Compiling achidas v0.1.0 (D:\workspace\.rust\achidas)
At line:1 char:1
+ cargo build 2>&1 | tee build_log.txt
+ ~~~~~~~~~~~~~~~~
    + CategoryInfo          : NotSpecified: (   Compiling ac...\.rust\achidas):String) [], RemoteException
    + FullyQualifiedErrorId : NativeCommandError
 
error: no rules expected `,`
   --> src\observability\mod.rs:114:5
    |
114 | /     metrics::histogram!("http_request_duration_seconds", duration.as_secs_f64(),
115 | |         "method" => method.to_string(),
116 | |         "path" => path,
117 | |         "status" => status.to_string()
118 | |     );
    | |_____^ no rules expected this token in macro call
    |
note: while trying to match `=>`
   --> C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\metrics-0.22.4\src\macros.rs:48:36
    |
48  |     ($name:expr, $($label_key:expr => $label_value:expr),*) => {{
    |                                    ^^
    = note: this error originates in the macro `$crate::histogram` which comes from the expansion of the macro 
`metrics::histogram` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0428]: the name `VultrObjectStorage` is defined multiple times
    --> src\vultr\models.rs:2100:1
     |
572  | pub struct VultrObjectStorage {
     | ----------------------------- previous definition of the type `VultrObjectStorage` here
...
2100 | pub struct VultrObjectStorage {
     | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ `VultrObjectStorage` redefined here
     |
     = note: `VultrObjectStorage` must be defined only once in the type namespace of this module

error[E0428]: the name `VultrOS` is defined multiple times
    --> src\vultr\models.rs:2160:1
     |
171  | pub struct VultrOS {
     | ------------------ previous definition of the type `VultrOS` here
...
2160 | pub struct VultrOS {
     | ^^^^^^^^^^^^^^^^^^ `VultrOS` redefined here
     |
     = note: `VultrOS` must be defined only once in the type namespace of this module

error[E0428]: the name `VultrOSResponse` is defined multiple times
    --> src\vultr\models.rs:2168:1
     |
179  | pub struct VultrOSResponse {
     | -------------------------- previous definition of the type `VultrOSResponse` here
...
2168 | pub struct VultrOSResponse {
     | ^^^^^^^^^^^^^^^^^^^^^^^^^^ `VultrOSResponse` redefined here
     |
     = note: `VultrOSResponse` must be defined only once in the type namespace of this module

error[E0428]: the name `VultrPlan` is defined multiple times
    --> src\vultr\models.rs:2175:1
     |
134  | pub struct VultrPlan {
     | -------------------- previous definition of the type `VultrPlan` here
...
2175 | pub struct VultrPlan {
     | ^^^^^^^^^^^^^^^^^^^^ `VultrPlan` redefined here
     |
     = note: `VultrPlan` must be defined only once in the type namespace of this module

error[E0428]: the name `VultrPlansResponse` is defined multiple times
    --> src\vultr\models.rs:2189:1
     |
148  | pub struct VultrPlansResponse {
     | ----------------------------- previous definition of the type `VultrPlansResponse` here
...
2189 | pub struct VultrPlansResponse {
     | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ `VultrPlansResponse` redefined here
     |
     = note: `VultrPlansResponse` must be defined only once in the type namespace of this module

error[E0428]: the name `VultrRegion` is defined multiple times
    --> src\vultr\models.rs:2321:1
     |
155  | pub struct VultrRegion {
     | ---------------------- previous definition of the type `VultrRegion` here
...
2321 | pub struct VultrRegion {
     | ^^^^^^^^^^^^^^^^^^^^^^ `VultrRegion` redefined here
     |
     = note: `VultrRegion` must be defined only once in the type namespace of this module

error[E0428]: the name `VultrRegionsResponse` is defined multiple times
    --> src\vultr\models.rs:2330:1
     |
164  | pub struct VultrRegionsResponse {
     | ------------------------------- previous definition of the type `VultrRegionsResponse` here
...
2330 | pub struct VultrRegionsResponse {
     | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ `VultrRegionsResponse` redefined here
     |
     = note: `VultrRegionsResponse` must be defined only once in the type namespace of this module

error[E0428]: the name `VultrSSHKey` is defined multiple times
    --> src\vultr\models.rs:2401:1
     |
186  | pub struct VultrSSHKey {
     | ---------------------- previous definition of the type `VultrSSHKey` here
...
2401 | pub struct VultrSSHKey {
     | ^^^^^^^^^^^^^^^^^^^^^^ `VultrSSHKey` redefined here
     |
     = note: `VultrSSHKey` must be defined only once in the type namespace of this module

error[E0428]: the name `VultrSSHKeysResponse` is defined multiple times
    --> src\vultr\models.rs:2409:1
     |
194  | pub struct VultrSSHKeysResponse {
     | ------------------------------- previous definition of the type `VultrSSHKeysResponse` here
...
2409 | pub struct VultrSSHKeysResponse {
     | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ `VultrSSHKeysResponse` redefined here
     |
     = note: `VultrSSHKeysResponse` must be defined only once in the type namespace of this module

error[E0428]: the name `VultrSnapshot` is defined multiple times
    --> src\vultr\models.rs:2612:1
     |
2342 | pub struct VultrSnapshot {
     | ------------------------ previous definition of the type `VultrSnapshot` here
...
2612 | pub struct VultrSnapshot {
     | ^^^^^^^^^^^^^^^^^^^^^^^^ `VultrSnapshot` redefined here
     |
     = note: `VultrSnapshot` must be defined only once in the type namespace of this module

error[E0428]: the name `VultrStartupScript` is defined multiple times
    --> src\vultr\models.rs:2636:1
     |
2428 | pub struct VultrStartupScript {
     | ----------------------------- previous definition of the type `VultrStartupScript` here
...
2636 | pub struct VultrStartupScript {
     | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ `VultrStartupScript` redefined here
     |
     = note: `VultrStartupScript` must be defined only once in the type namespace of this module

error[E0428]: the name `VultrISO` is defined multiple times
    --> src\vultr\models.rs:2648:1
     |
1332 | pub struct VultrISO {
     | ------------------- previous definition of the type `VultrISO` here
...
2648 | pub struct VultrISO {
     | ^^^^^^^^^^^^^^^^^^^ `VultrISO` redefined here
     |
     = note: `VultrISO` must be defined only once in the type namespace of this module

error[E0428]: the name `VultrStorageGateway` is defined multiple times
    --> src\vultr\models.rs:2705:1
     |
2460 | pub struct VultrStorageGateway {
     | ------------------------------ previous definition of the type `VultrStorageGateway` here
...
2705 | pub struct VultrStorageGateway {
     | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ `VultrStorageGateway` redefined here
     |
     = note: `VultrStorageGateway` must be defined only once in the type namespace of this module

error[E0428]: the name `VultrUser` is defined multiple times
    --> src\vultr\models.rs:2719:1
     |
2504 | pub struct VultrUser {
     | -------------------- previous definition of the type `VultrUser` here
...
2719 | pub struct VultrUser {
     | ^^^^^^^^^^^^^^^^^^^^ `VultrUser` redefined here
     |
     = note: `VultrUser` must be defined only once in the type namespace of this module

error[E0252]: the name `VultrOS` is defined multiple times
  --> src\controllers\vultr.rs:49:9
   |
6  |         UpdateBareMetalRequest, VultrOS, VultrPlan, VultrRegion, VultrBareMetal,
   |                                 ------- previous import of the type `VultrOS` here
...
49 |         VultrOS, VultrPlan, VultrMetalPlan, VultrInference, CreateInferenceRequest,
   |         ^^^^^^^ `VultrOS` reimported here
   |
   = note: `VultrOS` must be defined only once in the type namespace of this module
help: you can use `as` to change the binding name of the import
   |
49 |         VultrOS as OtherVultrOS, VultrPlan, VultrMetalPlan, VultrInference, CreateInferenceRequest,
   |                 +++++++++++++++

error[E0252]: the name `VultrPlan` is defined multiple times
  --> src\controllers\vultr.rs:49:18
   |
6  |         UpdateBareMetalRequest, VultrOS, VultrPlan, VultrRegion, VultrBareMetal,
   |                                          --------- previous import of the type `VultrPlan` here
...
49 |         VultrOS, VultrPlan, VultrMetalPlan, VultrInference, CreateInferenceRequest,
   |                  ^^^^^^^^^ `VultrPlan` reimported here
   |
   = note: `VultrPlan` must be defined only once in the type namespace of this module
help: you can use `as` to change the binding name of the import
   |
49 |         VultrOS, VultrPlan as OtherVultrPlan, VultrMetalPlan, VultrInference, CreateInferenceRequest,
   |                            +++++++++++++++++

error[E0252]: the name `VultrRegion` is defined multiple times
  --> src\controllers\vultr.rs:53:9
   |
6  |         UpdateBareMetalRequest, VultrOS, VultrPlan, VultrRegion, VultrBareMetal,
   |                                                     ----------- previous import of the type `VultrRegion` here
...
53 |         VultrRegion, VultrRegionAvailablePlans, VultrSnapshot, CreateSnapshotRequest,
   |         ^^^^^^^^^^^ `VultrRegion` reimported here
   |
   = note: `VultrRegion` must be defined only once in the type namespace of this module
help: you can use `as` to change the binding name of the import
   |
53 |         VultrRegion as OtherVultrRegion, VultrRegionAvailablePlans, VultrSnapshot, CreateSnapshotRequest,
   |                     +++++++++++++++++++

error[E0252]: the name `VultrSSHKey` is defined multiple times
  --> src\controllers\vultr.rs:55:34
   |
9  |         VultrSSHKey, VultrAccount, VultrAccountBGP, VultrAccountBandwidth,
   |         ----------- previous import of the type `VultrSSHKey` here
...
55 |         CreateSubaccountRequest, VultrSSHKey, CreateSSHKeyRequest, UpdateSSHKeyRequest,
   |                                  ^^^^^^^^^^^--
   |                                  |
   |                                  `VultrSSHKey` reimported here
   |                                  help: remove unnecessary import
   |
   = note: `VultrSSHKey` must be defined only once in the type namespace of this module

error[E0433]: failed to resolve: could not find `sdk` in `opentelemetry`
 --> src\observability\mod.rs:5:5
  |
5 |     sdk::{
  |     ^^^ could not find `sdk` in `opentelemetry`

error[E0432]: unresolved import `opentelemetry::sdk`
 --> src\observability\mod.rs:5:5
  |
5 |     sdk::{
  |     ^^^ could not find `sdk` in `opentelemetry`

error[E0432]: unresolved import `crate::services::admin`
 --> src\controllers\admin.rs:4:16
  |
4 |     services::{admin::AdminService, billing::BillingService},
  |                ^^^^^ could not find `admin` in `services`

error[E0433]: failed to resolve: could not find `register_counter` in `metrics`
  --> src\observability\mod.rs:81:14
   |
81 |     metrics::register_counter!("http_requests_total", "Total number of HTTP requests");
   |              ^^^^^^^^^^^^^^^^ could not find `register_counter` in `metrics`

error[E0433]: failed to resolve: could not find `register_histogram` in `metrics`
  --> src\observability\mod.rs:82:14
   |
82 |     metrics::register_histogram!("http_request_duration_seconds", "HTTP request duration in seconds");
   |              ^^^^^^^^^^^^^^^^^^ could not find `register_histogram` in `metrics`

error[E0433]: failed to resolve: could not find `register_gauge` in `metrics`
  --> src\observability\mod.rs:83:14
   |
83 |     metrics::register_gauge!("active_connections", "Number of active connections");
   |              ^^^^^^^^^^^^^^ could not find `register_gauge` in `metrics`

error[E0433]: failed to resolve: could not find `register_counter` in `metrics`
  --> src\observability\mod.rs:84:14
   |
84 |     metrics::register_counter!("vultr_api_calls_total", "Total number of Vultr API calls");
   |              ^^^^^^^^^^^^^^^^ could not find `register_counter` in `metrics`

error[E0433]: failed to resolve: could not find `register_counter` in `metrics`
  --> src\observability\mod.rs:85:14
   |
85 |     metrics::register_counter!("vultr_api_errors_total", "Total number of Vultr API errors");
   |              ^^^^^^^^^^^^^^^^ could not find `register_counter` in `metrics`

error[E0433]: failed to resolve: could not find `register_gauge` in `metrics`
  --> src\observability\mod.rs:86:14
   |
86 |     metrics::register_gauge!("active_instances", "Number of active instances");
   |              ^^^^^^^^^^^^^^ could not find `register_gauge` in `metrics`

error[E0433]: failed to resolve: could not find `register_counter` in `metrics`
  --> src\observability\mod.rs:87:14
   |
87 |     metrics::register_counter!("user_registrations_total", "Total number of user registrations");
   |              ^^^^^^^^^^^^^^^^ could not find `register_counter` in `metrics`

error[E0433]: failed to resolve: could not find `register_counter` in `metrics`
  --> src\observability\mod.rs:88:14
   |
88 |     metrics::register_counter!("user_logins_total", "Total number of user logins");
   |              ^^^^^^^^^^^^^^^^ could not find `register_counter` in `metrics`

error[E0433]: failed to resolve: could not find `increment_counter` in `metrics`
   --> src\observability\mod.rs:108:14
    |
108 |     metrics::increment_counter!("http_requests_total", 
    |              ^^^^^^^^^^^^^^^^^ could not find `increment_counter` in `metrics`

error[E0412]: cannot find type `HashMap` in this scope
    --> src\controllers\vultr.rs:1636:55
     |
1636 | ) -> ControllerResult<Json<crate::models::ApiResponse<HashMap<String, InstanceBandwidth>>>> {
     |                                                       ^^^^^^^ not found in this scope
     |
help: consider importing this struct
     |
1    + use std::collections::HashMap;
     |

error[E0433]: failed to resolve: use of unresolved module or unlinked crate `services`
  --> src\controllers\mod.rs:85:11
   |
85 | impl From<services::ServiceError> for ControllerError {
   |           ^^^^^^^^ use of unresolved module or unlinked crate `services`
   |
   = help: if you wanted to use a crate named `services`, use `cargo add services` to add it to your `Cargo.toml`
help: consider importing this module
   |
9  + use crate::services;
   |

error[E0433]: failed to resolve: use of unresolved module or unlinked crate `services`
  --> src\controllers\mod.rs:86:18
   |
86 |     fn from(err: services::ServiceError) -> Self {
   |                  ^^^^^^^^ use of unresolved module or unlinked crate `services`
   |
   = help: if you wanted to use a crate named `services`, use `cargo add services` to add it to your `Cargo.toml`
help: consider importing this module
   |
9  + use crate::services;
   |

error[E0425]: cannot find function `patch` in this scope
   --> src\routes\mod.rs:142:50
    |
142 |         .route("/vultr/dns/domains/:domain/soa", patch(controllers::vultr::update_dns_domain_soa))
    |                                                  ^^^^^ not found in this scope
    |
help: consider importing this function
    |
1   + use axum::routing::patch;
    |

error[E0425]: cannot find function `patch` in this scope
   --> src\routes\mod.rs:147:65
    |
147 |         .route("/vultr/dns/domains/:domain/records/:record_id", patch(controllers::vultr::update_dns_domain_record))
    |                                                                 ^^^^^ not found in this scope
    |
help: consider importing this function
    |
1   + use axum::routing::patch;
    |

error[E0425]: cannot find function `patch` in this scope
   --> src\routes\mod.rs:165:49
    |
165 |         .route("/vultr/instances/:instance_id", patch(controllers::vultr::update_instance_detailed))
    |                                                 ^^^^^ not found in this scope
    |
help: consider importing this function
    |
1   + use axum::routing::patch;
    |

error[E0425]: cannot find function `patch` in this scope
   --> src\routes\mod.rs:218:78
    |
218 |         .route("/vultr/kubernetes/clusters/:vke_id/node-pools/:nodepool_id", patch(controllers::vultr::update_nodepool))
    |                                                                              ^^^^^ not found in this scope
    |
help: consider importing this function
    |
1   + use axum::routing::patch;
    |

error[E0425]: cannot find function `patch` in this scope
   --> src\routes\mod.rs:229:59
    |
229 |         .route("/vultr/load-balancers/:load_balancer_id", patch(controllers::vultr::update_load_balancer))
    |                                                           ^^^^^ not found in this scope
    |
help: consider importing this function
    |
1   + use axum::routing::patch;
    |

error[E0425]: cannot find function `patch` in this scope
   --> src\routes\mod.rs:279:50
    |
279 |         .route("/vultr/inference/:inference_id", patch(controllers::vultr::update_inference))
    |                                                  ^^^^^ not found in this scope
    |
help: consider importing this function
    |
1   + use axum::routing::patch;
    |

error[E0425]: cannot find function `patch` in this scope
   --> src\routes\mod.rs:294:52
    |
294 |         .route("/vultr/reserved-ips/:reserved_ip", patch(controllers::vultr::update_reserved_ip))
    |                                                    ^^^^^ not found in this scope
    |
help: consider importing this function
    |
1   + use axum::routing::patch;
    |

error[E0425]: cannot find value `create_ssh_key` in module `controllers::vultr`
   --> src\routes\mod.rs:318:60
    |
318 |         .route("/vultr/ssh-keys", post(controllers::vultr::create_ssh_key))
    |                                                            ^^^^^^^^^^^^^^ not found in `controllers::vultr`

error[E0425]: cannot find value `get_ssh_key` in module `controllers::vultr`
   --> src\routes\mod.rs:319:71
    |
319 |         .route("/vultr/ssh-keys/:ssh_key_id", get(controllers::vultr::get_ssh_key))
    |                                                                       ^^^^^^^^^^^ not found in `controllers::vultr`

error[E0425]: cannot find function `patch` in this scope
   --> src\routes\mod.rs:320:47
    |
320 |         .route("/vultr/ssh-keys/:ssh_key_id", patch(controllers::vultr::update_ssh_key))
    |                                               ^^^^^ not found in this scope
    |
help: consider importing this function
    |
1   + use axum::routing::patch;
    |

error[E0425]: cannot find value `update_ssh_key` in module `controllers::vultr`
   --> src\routes\mod.rs:320:73
    |
320 |         .route("/vultr/ssh-keys/:ssh_key_id", patch(controllers::vultr::update_ssh_key))
    |                                                                         ^^^^^^^^^^^^^^ not found in `controllers::vultr`

error[E0425]: cannot find value `delete_ssh_key` in module `controllers::vultr`
   --> src\routes\mod.rs:321:74
    |
321 |         .route("/vultr/ssh-keys/:ssh_key_id", delete(controllers::vultr::delete_ssh_key))
    |                                                                          ^^^^^^^^^^^^^^ not found in `controllers::vultr`

error[E0425]: cannot find value `list_startup_scripts` in module `controllers::vultr`
   --> src\routes\mod.rs:324:66
    |
324 |         .route("/vultr/startup-scripts", get(controllers::vultr::list_startup_scripts))
    |                                                                  ^^^^^^^^^^^^^^^^^^^^ not found in `controllers::vultr`

error[E0425]: cannot find value `create_startup_script` in module `controllers::vultr`
   --> src\routes\mod.rs:325:67
    |
325 |         .route("/vultr/startup-scripts", post(controllers::vultr::create_startup_script))
    |                                                                   ^^^^^^^^^^^^^^^^^^^^^ not found in `controllers::vultr`

error[E0425]: cannot find value `get_startup_script` in module `controllers::vultr`
   --> src\routes\mod.rs:326:77
    |
326 |         .route("/vultr/startup-scripts/:script_id", get(controllers::vultr::get_startup_script))
    |                                                                             ^^^^^^^^^^^^^^^^^^ not found in 
`controllers::vultr`

error[E0425]: cannot find function `patch` in this scope
   --> src\routes\mod.rs:327:53
    |
327 |         .route("/vultr/startup-scripts/:script_id", patch(controllers::vultr::update_startup_script))
    |                                                     ^^^^^ not found in this scope
    |
help: consider importing this function
    |
1   + use axum::routing::patch;
    |

error[E0425]: cannot find value `update_startup_script` in module `controllers::vultr`
   --> src\routes\mod.rs:327:79
    |
327 |         .route("/vultr/startup-scripts/:script_id", patch(controllers::vultr::update_startup_script))
    |                                                                               ^^^^^^^^^^^^^^^^^^^^^ not found in 
`controllers::vultr`

error[E0425]: cannot find value `delete_startup_script` in module `controllers::vultr`
   --> src\routes\mod.rs:328:80
    |
328 |         .route("/vultr/startup-scripts/:script_id", delete(controllers::vultr::delete_startup_script))
    |                                                                                ^^^^^^^^^^^^^^^^^^^^^ not found in 
`controllers::vultr`

error[E0425]: cannot find value `list_storage_gateways` in module `controllers::vultr`
   --> src\routes\mod.rs:331:67
    |
331 |         .route("/vultr/storage-gateways", get(controllers::vultr::list_storage_gateways))
    |                                                                   ^^^^^^^^^^^^^^^^^^^^^ not found in `controllers::vultr`

error[E0425]: cannot find value `create_storage_gateway` in module `controllers::vultr`
   --> src\routes\mod.rs:332:68
    |
332 |         .route("/vultr/storage-gateways", post(controllers::vultr::create_storage_gateway))
    |                                                                    ^^^^^^^^^^^^^^^^^^^^^^ not found in 
`controllers::vultr`

error[E0425]: cannot find value `get_storage_gateway` in module `controllers::vultr`
   --> src\routes\mod.rs:333:79
    |
333 |         .route("/vultr/storage-gateways/:gateway_id", get(controllers::vultr::get_storage_gateway))
    |                                                                               ^^^^^^^^^^^^^^^^^^^ not found in 
`controllers::vultr`

error[E0425]: cannot find value `update_storage_gateway` in module `controllers::vultr`
   --> src\routes\mod.rs:334:79
    |
334 |         .route("/vultr/storage-gateways/:gateway_id", put(controllers::vultr::update_storage_gateway))
    |                                                                               ^^^^^^^^^^^^^^^^^^^^^^ not found in 
`controllers::vultr`

error[E0425]: cannot find value `delete_storage_gateway` in module `controllers::vultr`
   --> src\routes\mod.rs:335:82
    |
335 |         .route("/vultr/storage-gateways/:gateway_id", delete(controllers::vultr::delete_storage_gateway))
    |                                                                                  ^^^^^^^^^^^^^^^^^^^^^^ not found in 
`controllers::vultr`

error[E0425]: cannot find value `add_storage_gateway_export` in module `controllers::vultr`
   --> src\routes\mod.rs:336:88
    |
336 |         .route("/vultr/storage-gateways/:gateway_id/exports", post(controllers::vultr::add_storage_gateway_export))
    |                                                                                        ^^^^^^^^^^^^^^^^^^^^^^^^^^ not 
found in `controllers::vultr`

error[E0425]: cannot find value `delete_storage_gateway_export` in module `controllers::vultr`
   --> src\routes\mod.rs:337:101
    |
337 |         .route("/vultr/storage-gateways/:gateway_id/exports/:export_id", 
delete(controllers::vultr::delete_storage_gateway_export))
    |                                                                                                     
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ not found in `controllers::vultr`

error[E0425]: cannot find value `list_users` in module `controllers::vultr`
    --> src\routes\mod.rs:340:56
     |
340  |           .route("/vultr/users", get(controllers::vultr::list_users))
     |                                                          ^^^^^^^^^^
     |
    ::: src\controllers\vultr.rs:1964:1
     |
1964 | / pub async fn list_isos(
1965 | |     State(state): State<Arc<AppState>>,
1966 | | ) -> ControllerResult<Json<crate::models::ApiResponse<Vec<VultrISO>>>> {
1967 | |     let isos = state
...    |
1973 | |     Ok(success_response(isos))
1974 | | }
     | |_- similarly named function `list_isos` defined here
     |
help: a function with a similar name exists
     |
340  -         .route("/vultr/users", get(controllers::vultr::list_users))
340  +         .route("/vultr/users", get(controllers::vultr::list_isos))
     |
help: consider importing this function
     |
1    + use crate::controllers::admin::list_users;
     |
help: if you import `list_users`, refer to it directly
     |
340  -         .route("/vultr/users", get(controllers::vultr::list_users))
340  +         .route("/vultr/users", get(list_users))
     |

error[E0425]: cannot find value `create_user` in module `controllers::vultr`
    --> src\routes\mod.rs:341:57
     |
341  |           .route("/vultr/users", post(controllers::vultr::create_user))
     |                                                           ^^^^^^^^^^^ help: a function with a similar name exists: 
`create_iso`
     |
    ::: src\controllers\vultr.rs:1977:1
     |
1977 | / pub async fn create_iso(
1978 | |     State(state): State<Arc<AppState>>,
1979 | |     Json(request): Json<CreateISORequest>,
1980 | | ) -> ControllerResult<Json<crate::models::ApiResponse<VultrISO>>> {
...    |
1987 | |     Ok(success_response(iso))
1988 | | }
     | |_- similarly named function `create_iso` defined here

error[E0425]: cannot find value `get_user` in module `controllers::vultr`
   --> src\routes\mod.rs:342:65
    |
342 |         .route("/vultr/users/:user_id", get(controllers::vultr::get_user))
    |                                                                 ^^^^^^^^ not found in `controllers::vultr`
    |
help: consider importing this function
    |
1   + use crate::controllers::admin::get_user;
    |
help: if you import `get_user`, refer to it directly
    |
342 -         .route("/vultr/users/:user_id", get(controllers::vultr::get_user))
342 +         .route("/vultr/users/:user_id", get(get_user))
    |

error[E0425]: cannot find function `patch` in this scope
   --> src\routes\mod.rs:343:41
    |
343 |         .route("/vultr/users/:user_id", patch(controllers::vultr::update_user))
    |                                         ^^^^^ not found in this scope
    |
help: consider importing this function
    |
1   + use axum::routing::patch;
    |

error[E0425]: cannot find value `update_user` in module `controllers::vultr`
   --> src\routes\mod.rs:343:67
    |
343 |         .route("/vultr/users/:user_id", patch(controllers::vultr::update_user))
    |                                                                   ^^^^^^^^^^^ not found in `controllers::vultr`

error[E0425]: cannot find value `delete_user` in module `controllers::vultr`
    --> src\routes\mod.rs:344:68
     |
344  |           .route("/vultr/users/:user_id", delete(controllers::vultr::delete_user))
     |                                                                      ^^^^^^^^^^^ help: a function with a similar name 
exists: `delete_iso`
     |
    ::: src\controllers\vultr.rs:2005:1
     |
2005 | / pub async fn delete_iso(
2006 | |     State(state): State<Arc<AppState>>,
2007 | |     Path(iso_id): Path<String>,
2008 | | ) -> ControllerResult<Json<crate::models::ApiResponse<()>>> {
...    |
2015 | |     Ok(success_response(()))
2016 | | }
     | |_- similarly named function `delete_iso` defined here

error[E0425]: cannot find value `list_vfs_regions` in module `controllers::vultr`
   --> src\routes\mod.rs:347:62
    |
347 |           .route("/vultr/vfs/regions", get(controllers::vultr::list_vfs_regions))
    |                                                                ^^^^^^^^^^^^^^^^ help: a function with a similar name 
exists: `list_regions`
    |
   ::: src\controllers\vultr.rs:140:1
    |
140 | / pub async fn list_regions(
141 | |     State(state): State<Arc<AppState>>,
142 | | ) -> ControllerResult<Json<crate::models::ApiResponse<Vec<crate::vultr::models::VultrRegion>>>> {
143 | |     let regions = state
...   |
161 | |     Ok(success_response(converted_regions))
162 | | }
    | |_- similarly named function `list_regions` defined here

error[E0425]: cannot find value `list_vfs` in module `controllers::vultr`
   --> src\routes\mod.rs:348:54
    |
348 |           .route("/vultr/vfs", get(controllers::vultr::list_vfs))
    |                                                        ^^^^^^^^ help: a function with a similar name exists: `list_os`
    |
   ::: src\controllers\vultr.rs:165:1
    |
165 | / pub async fn list_os(
166 | |     State(state): State<Arc<AppState>>,
167 | | ) -> ControllerResult<Json<crate::models::ApiResponse<Vec<crate::vultr::models::VultrOS>>>> {
168 | |     let os_list = state
...   |
185 | |     Ok(success_response(converted_os))
186 | | }
    | |_- similarly named function `list_os` defined here

error[E0425]: cannot find value `create_vfs` in module `controllers::vultr`
    --> src\routes\mod.rs:349:55
     |
349  |           .route("/vultr/vfs", post(controllers::vultr::create_vfs))
     |                                                         ^^^^^^^^^^ help: a function with a similar name exists: 
`create_vpc`
     |
    ::: src\controllers\vultr.rs:2899:1
     |
2899 | / pub async fn create_vpc(
2900 | |     State(state): State<Arc<AppState>>,
2901 | |     Json(request): Json<CreateVPCRequest>,
2902 | | ) -> ControllerResult<Json<crate::models::ApiResponse<VultrVPCDetailed>>> {
...    |
2909 | |     Ok(success_response(vpc))
2910 | | }
     | |_- similarly named function `create_vpc` defined here

error[E0425]: cannot find value `get_vfs` in module `controllers::vultr`
    --> src\routes\mod.rs:350:62
     |
350  |           .route("/vultr/vfs/:vfs_id", get(controllers::vultr::get_vfs))
     |                                                                ^^^^^^^ help: a function with a similar name exists: 
`get_vpc`
     |
    ::: src\controllers\vultr.rs:2913:1
     |
2913 | / pub async fn get_vpc(
2914 | |     State(state): State<Arc<AppState>>,
2915 | |     Path(vpc_id): Path<String>,
2916 | | ) -> ControllerResult<Json<crate::models::ApiResponse<VultrVPCDetailed>>> {
...    |
2923 | |     Ok(success_response(vpc))
2924 | | }
     | |_- similarly named function `get_vpc` defined here

error[E0425]: cannot find value `update_vfs` in module `controllers::vultr`
    --> src\routes\mod.rs:351:62
     |
351  |           .route("/vultr/vfs/:vfs_id", put(controllers::vultr::update_vfs))
     |                                                                ^^^^^^^^^^ help: a function with a similar name exists: 
`update_vpc`
     |
    ::: src\controllers\vultr.rs:2927:1
     |
2927 | / pub async fn update_vpc(
2928 | |     State(state): State<Arc<AppState>>,
2929 | |     Path(vpc_id): Path<String>,
2930 | |     Json(request): Json<UpdateVPCRequest>,
...    |
2938 | |     Ok(success_response(()))
2939 | | }
     | |_- similarly named function `update_vpc` defined here

error[E0425]: cannot find value `delete_vfs` in module `controllers::vultr`
    --> src\routes\mod.rs:352:65
     |
352  |           .route("/vultr/vfs/:vfs_id", delete(controllers::vultr::delete_vfs))
     |                                                                   ^^^^^^^^^^ help: a function with a similar name 
exists: `delete_vpc`
     |
    ::: src\controllers\vultr.rs:2942:1
     |
2942 | / pub async fn delete_vpc(
2943 | |     State(state): State<Arc<AppState>>,
2944 | |     Path(vpc_id): Path<String>,
2945 | | ) -> ControllerResult<Json<crate::models::ApiResponse<()>>> {
...    |
2952 | |     Ok(success_response(()))
2953 | | }
     | |_- similarly named function `delete_vpc` defined here

error[E0425]: cannot find value `list_vfs_attachments` in module `controllers::vultr`
   --> src\routes\mod.rs:353:74
    |
353 |         .route("/vultr/vfs/:vfs_id/attachments", get(controllers::vultr::list_vfs_attachments))
    |                                                                          ^^^^^^^^^^^^^^^^^^^^ not found in 
`controllers::vultr`

error[E0425]: cannot find value `create_vfs_attachment` in module `controllers::vultr`
   --> src\routes\mod.rs:354:75
    |
354 |         .route("/vultr/vfs/:vfs_id/attachments", post(controllers::vultr::create_vfs_attachment))
    |                                                                           ^^^^^^^^^^^^^^^^^^^^^ not found in 
`controllers::vultr`

error[E0425]: cannot find value `get_vfs_attachment` in module `controllers::vultr`
   --> src\routes\mod.rs:355:89
    |
355 |         .route("/vultr/vfs/:vfs_id/attachments/:attachment_id", get(controllers::vultr::get_vfs_attachment))
    |                                                                                         ^^^^^^^^^^^^^^^^^^ not found in 
`controllers::vultr`

error[E0425]: cannot find value `delete_vfs_attachment` in module `controllers::vultr`
   --> src\routes\mod.rs:356:92
    |
356 |         .route("/vultr/vfs/:vfs_id/attachments/:attachment_id", delete(controllers::vultr::delete_vfs_attachment))
    |                                                                                            ^^^^^^^^^^^^^^^^^^^^^ not 
found in `controllers::vultr`

error[E0433]: failed to resolve: could not find `ErrorKind` in `reqwest`
  --> src\utils\retry.rs:80:51
   |
80 |                     reqwest::Error::from(reqwest::ErrorKind::Request)
   |                                                   ^^^^^^^^^ could not find `ErrorKind` in `reqwest`
   |
help: consider importing one of these enums
   |
1  + use std::io::ErrorKind;
   |
1  + use axum::extract::path::ErrorKind;
   |
1  + use bson::raw::ErrorKind;
   |
1  + use futures::io::ErrorKind;
   |
     and 3 other candidates
help: if you import `ErrorKind`, refer to it directly
   |
80 -                     reqwest::Error::from(reqwest::ErrorKind::Request)
80 +                     reqwest::Error::from(ErrorKind::Request)
   |

error[E0433]: failed to resolve: could not find `ErrorKind` in `reqwest`
  --> src\utils\retry.rs:88:59
   |
88 | ...                   reqwest::Error::from(reqwest::ErrorKind::Request)
   |                                                     ^^^^^^^^^ could not find `ErrorKind` in `reqwest`
   |
help: consider importing one of these enums
   |
1  + use std::io::ErrorKind;
   |
1  + use axum::extract::path::ErrorKind;
   |
1  + use bson::raw::ErrorKind;
   |
1  + use futures::io::ErrorKind;
   |
     and 3 other candidates
help: if you import `ErrorKind`, refer to it directly
   |
88 -                             reqwest::Error::from(reqwest::ErrorKind::Request)
88 +                             reqwest::Error::from(ErrorKind::Request)
   |

error[E0412]: cannot find type `HashMap` in this scope
   --> src\vultr\client.rs:717:77
    |
717 |     pub async fn get_instance_bandwidth(&self, instance_id: &str) -> Result<HashMap<String, InstanceBandwidth>> {
    |                                                                             ^^^^^^^ not found in this scope
    |
help: consider importing one of these structs
    |
1   + use crate::vultr::HashMap;
    |
1   + use std::collections::HashMap;
    |

warning: unused import: `billing::BillingService`
 --> src\controllers\admin.rs:4:37
  |
4 |     services::{admin::AdminService, billing::BillingService},
  |                                     ^^^^^^^^^^^^^^^^^^^^^^^
  |
  = note: `#[warn(unused_imports)]` on by default

warning: unused imports: `AddDatabaseReadReplicaRequest`, `AddStorageGatewayExportRequest`, `BareMetalBandwidth`, 
`BareMetalIpv4Info`, `BareMetalIpv6Info`, `BareMetalUpgrades`, `BareMetalUserData`, `BareMetalVncInfo`, `BareMetalVpcInfo`, 
`CreateBareMetalRequest`, `CreateDatabaseConnectionPoolRequest`, `CreateDatabaseConnectorRequest`, `CreateDatabaseDBRequest`, 
`CreateDatabaseQuotaRequest`, `CreateDatabaseTopicRequest`, `CreateSSHKeyRequest`, `CreateStartupScriptRequest`, 
`CreateStorageGatewayRequest`, `CreateUserRequest`, `CreateVFSAttachmentRequest`, `CreateVFSRequest`, `ForkDatabaseRequest`, 
`RestoreDatabaseFromBackupRequest`, `StartDatabaseMigrationRequest`, `StartDatabaseVersionUpgradeRequest`, 
`UpdateBareMetalRequest`, `UpdateDatabaseConnectionPoolRequest`, `UpdateDatabaseConnectorRequest`, 
`UpdateDatabaseTopicRequest`, `UpdateSSHKeyRequest`, `UpdateStartupScriptRequest`, `UpdateStorageGatewayRequest`, 
`UpdateUserRequest`, `UpdateVFSRequest`, `VultrDatabaseAdvancedOptions`, `VultrDatabaseBackupInfo`, 
`VultrDatabaseConnectionPool`, `VultrDatabaseConnectorStatus`, `VultrDatabaseConnector`, `VultrDatabaseDB`, 
`VultrDatabaseMaintenanceUpdate`, `VultrDatabaseMigrationStatus`, `VultrDatabaseQuota`, `VultrDatabaseReadReplica`, 
`VultrDatabaseServiceAlert`, `VultrDatabaseTopic`, `VultrDatabaseVersions`, `VultrOS`, `VultrPlan`, `VultrRegion`, 
`VultrSSHKey`, `VultrStartupScript`, `VultrStorageGatewayExport`, `VultrStorageGateway`, `VultrUser`, `VultrVFSAttachment`, and 
`VultrVFS`
  --> src\controllers\vultr.rs:4:9
   |
4  |         BareMetalBandwidth, BareMetalIpv4Info, BareMetalIpv6Info, BareMetalUpgrades,
   |         ^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^
5  |         BareMetalUserData, BareMetalVncInfo, BareMetalVpcInfo, CreateBareMetalRequest,
   |         ^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^
6  |         UpdateBareMetalRequest, VultrOS, VultrPlan, VultrRegion, VultrBareMetal,
   |         ^^^^^^^^^^^^^^^^^^^^^^
...
37 |         SetDatabaseUserACLRequest, VultrDatabaseDB, CreateDatabaseDBRequest,
   |                                    ^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^^
38 |         VultrDatabaseTopic, CreateDatabaseTopicRequest, UpdateDatabaseTopicRequest,
   |         ^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^^^^^
39 |         VultrDatabaseQuota, CreateDatabaseQuotaRequest, VultrDatabaseConnector,
   |         ^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^
40 |         CreateDatabaseConnectorRequest, UpdateDatabaseConnectorRequest,
   |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
41 |         VultrDatabaseConnectorStatus, VultrDatabaseMaintenanceUpdate,
   |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
42 |         VultrDatabaseServiceAlert, VultrDatabaseMigrationStatus, StartDatabaseMigrationRequest,
   |         ^^^^^^^^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
43 |         AddDatabaseReadReplicaRequest, VultrDatabaseReadReplica, VultrDatabaseBackupInfo,
   |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^^
44 |         RestoreDatabaseFromBackupRequest, ForkDatabaseRequest, VultrDatabaseConnectionPool,
   |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^^^^^^
45 |         CreateDatabaseConnectionPoolRequest, UpdateDatabaseConnectionPoolRequest,
   |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
46 |         VultrDatabaseAdvancedOptions, VultrDatabaseVersions, StartDatabaseVersionUpgradeRequest,
   |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
...
49 |         VultrOS, VultrPlan, VultrMetalPlan, VultrInference, CreateInferenceRequest,
   |         ^^^^^^^  ^^^^^^^^^
...
53 |         VultrRegion, VultrRegionAvailablePlans, VultrSnapshot, CreateSnapshotRequest,
   |         ^^^^^^^^^^^
54 |         CreateSnapshotFromUrlRequest, UpdateSnapshotRequest, VultrSubaccount,
55 |         CreateSubaccountRequest, VultrSSHKey, CreateSSHKeyRequest, UpdateSSHKeyRequest,
   |                                  ^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^
56 |         VultrStartupScript, CreateStartupScriptRequest, UpdateStartupScriptRequest,
   |         ^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^^^^^
57 |         VultrStorageGateway, CreateStorageGatewayRequest, UpdateStorageGatewayRequest,
   |         ^^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^^^^^^
58 |         VultrStorageGatewayExport, AddStorageGatewayExportRequest, VultrUser,
   |         ^^^^^^^^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^  ^^^^^^^^^
59 |         CreateUserRequest, UpdateUserRequest, VultrVFS, CreateVFSRequest,
   |         ^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^  ^^^^^^^^  ^^^^^^^^^^^^^^^^
60 |         UpdateVFSRequest, VultrVFSAttachment, CreateVFSAttachmentRequest,
   |         ^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `serde_json::json`
  --> src\controllers\mod.rs:15:5
   |
15 | use serde_json::json;
   |     ^^^^^^^^^^^^^^^^

warning: unused import: `error`
  --> src\middleware\auth.rs:13:15
   |
13 | use tracing::{error, warn};
   |               ^^^^^

warning: unused imports: `HeaderValue` and `response::Response`
 --> src\middleware\cors.rs:2:20
  |
2 |     http::{header, HeaderValue, Method},
  |                    ^^^^^^^^^^^
3 |     response::Response,
  |     ^^^^^^^^^^^^^^^^^^

warning: unused import: `StatusCode`
 --> src\middleware\mod.rs:7:21
  |
7 |     http::{Request, StatusCode},
  |                     ^^^^^^^^^^

warning: unused import: `bson::oid::ObjectId`
 --> src\models\mod.rs:1:5
  |
1 | use bson::oid::ObjectId;
  |     ^^^^^^^^^^^^^^^^^^^

warning: unused imports: `DateTime` and `Utc`
 --> src\models\mod.rs:2:14
  |
2 | use chrono::{DateTime, Utc};
  |              ^^^^^^^^  ^^^

warning: unused import: `uuid::Uuid`
 --> src\models\mod.rs:4:5
  |
4 | use uuid::Uuid;
  |     ^^^^^^^^^^

warning: unused import: `global`
 --> src\observability\mod.rs:4:5
  |
4 |     global,
  |     ^^^^^^

warning: unused import: `Level`
  --> src\observability\mod.rs:12:21
   |
12 | use tracing::{info, Level};
   |                     ^^^^^

warning: unused import: `error`
  --> src\services\auth.rs:15:15
   |
15 | use tracing::{error, info, instrument};
   |               ^^^^^

warning: unused import: `database::Database`
 --> src\services\billing.rs:2:5
  |
2 |     database::Database,
  |     ^^^^^^^^^^^^^^^^^^

warning: unused import: `error`
  --> src\services\instance.rs:14:15
   |
14 | use tracing::{error, info, instrument};
   |               ^^^^^

warning: unused import: `DateTime`
 --> src\utils\mod.rs:3:14
  |
3 | use chrono::{DateTime, Utc};
  |              ^^^^^^^^

warning: unused import: `Response`
 --> src\vultr\mod.rs:3:42
  |
3 | use reqwest::{header::HeaderMap, Client, Response};
  |                                          ^^^^^^^^

warning: unused imports: `Deserialize` and `Serialize`
 --> src\vultr\mod.rs:4:13
  |
4 | use serde::{Deserialize, Serialize};
  |             ^^^^^^^^^^^  ^^^^^^^^^

warning: unused import: `futures::TryStreamExt`
 --> src\vultr\mod.rs:7:5
  |
7 | use futures::TryStreamExt;
  |     ^^^^^^^^^^^^^^^^^^^^^

warning: unused imports: `DateTime` and `Utc`
 --> src\vultr\models.rs:1:14
  |
1 | use chrono::{DateTime, Utc};
  |              ^^^^^^^^  ^^^

error[E0119]: conflicting implementations of trait `Clone` for type `vultr::models::VultrObjectStorage`
    --> src\vultr\models.rs:2099:17
     |
571  | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                 ----- first implementation here
...
2099 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                 ^^^^^ conflicting implementation for `vultr::models::VultrObjectStorage`
     |
     = note: this error originates in the derive macro `Clone` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0119]: conflicting implementations of trait `Clone` for type `vultr::models::VultrOS`
    --> src\vultr\models.rs:2159:17
     |
170  | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                 ----- first implementation here
...
2159 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                 ^^^^^ conflicting implementation for `vultr::models::VultrOS`
     |
     = note: this error originates in the derive macro `Clone` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0119]: conflicting implementations of trait `Clone` for type `vultr::models::VultrOSResponse`
    --> src\vultr\models.rs:2167:17
     |
178  | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                 ----- first implementation here
...
2167 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                 ^^^^^ conflicting implementation for `vultr::models::VultrOSResponse`
     |
     = note: this error originates in the derive macro `Clone` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0119]: conflicting implementations of trait `Clone` for type `vultr::models::VultrPlan`
    --> src\vultr\models.rs:2174:17
     |
133  | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                 ----- first implementation here
...
2174 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                 ^^^^^ conflicting implementation for `vultr::models::VultrPlan`
     |
     = note: this error originates in the derive macro `Clone` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0119]: conflicting implementations of trait `Clone` for type `vultr::models::VultrPlansResponse`
    --> src\vultr\models.rs:2188:17
     |
147  | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                 ----- first implementation here
...
2188 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                 ^^^^^ conflicting implementation for `vultr::models::VultrPlansResponse`
     |
     = note: this error originates in the derive macro `Clone` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0119]: conflicting implementations of trait `Clone` for type `vultr::models::VultrRegion`
    --> src\vultr\models.rs:2320:17
     |
154  | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                 ----- first implementation here
...
2320 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                 ^^^^^ conflicting implementation for `vultr::models::VultrRegion`
     |
     = note: this error originates in the derive macro `Clone` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0119]: conflicting implementations of trait `Clone` for type `vultr::models::VultrRegionsResponse`
    --> src\vultr\models.rs:2329:17
     |
163  | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                 ----- first implementation here
...
2329 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                 ^^^^^ conflicting implementation for `vultr::models::VultrRegionsResponse`
     |
     = note: this error originates in the derive macro `Clone` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0119]: conflicting implementations of trait `Clone` for type `vultr::models::VultrSSHKey`
    --> src\vultr\models.rs:2400:17
     |
185  | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                 ----- first implementation here
...
2400 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                 ^^^^^ conflicting implementation for `vultr::models::VultrSSHKey`
     |
     = note: this error originates in the derive macro `Clone` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0119]: conflicting implementations of trait `Clone` for type `vultr::models::VultrSSHKeysResponse`
    --> src\vultr\models.rs:2408:17
     |
193  | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                 ----- first implementation here
...
2408 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                 ^^^^^ conflicting implementation for `vultr::models::VultrSSHKeysResponse`
     |
     = note: this error originates in the derive macro `Clone` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0119]: conflicting implementations of trait `Clone` for type `vultr::models::VultrSnapshot`
    --> src\vultr\models.rs:2611:17
     |
2341 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                 ----- first implementation here
...
2611 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                 ^^^^^ conflicting implementation for `vultr::models::VultrSnapshot`
     |
     = note: this error originates in the derive macro `Clone` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0119]: conflicting implementations of trait `Clone` for type `vultr::models::VultrStartupScript`
    --> src\vultr\models.rs:2635:17
     |
2427 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                 ----- first implementation here
...
2635 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                 ^^^^^ conflicting implementation for `vultr::models::VultrStartupScript`
     |
     = note: this error originates in the derive macro `Clone` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0119]: conflicting implementations of trait `Clone` for type `vultr::models::VultrISO`
    --> src\vultr\models.rs:2647:17
     |
1331 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                 ----- first implementation here
...
2647 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                 ^^^^^ conflicting implementation for `vultr::models::VultrISO`
     |
     = note: this error originates in the derive macro `Clone` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0119]: conflicting implementations of trait `Clone` for type `vultr::models::VultrStorageGateway`
    --> src\vultr\models.rs:2704:17
     |
2459 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                 ----- first implementation here
...
2704 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                 ^^^^^ conflicting implementation for `vultr::models::VultrStorageGateway`
     |
     = note: this error originates in the derive macro `Clone` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0119]: conflicting implementations of trait `Clone` for type `vultr::models::VultrUser`
    --> src\vultr\models.rs:2718:17
     |
2503 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                 ----- first implementation here
...
2718 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                 ^^^^^ conflicting implementation for `vultr::models::VultrUser`
     |
     = note: this error originates in the derive macro `Clone` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0119]: conflicting implementations of trait `std::fmt::Debug` for type `vultr::models::VultrObjectStorage`
    --> src\vultr\models.rs:2099:10
     |
571  | #[derive(Debug, Clone, Serialize, Deserialize)]
     |          ----- first implementation here
...
2099 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |          ^^^^^ conflicting implementation for `vultr::models::VultrObjectStorage`
     |
     = note: this error originates in the derive macro `Debug` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0119]: conflicting implementations of trait `std::fmt::Debug` for type `vultr::models::VultrOS`
    --> src\vultr\models.rs:2159:10
     |
170  | #[derive(Debug, Clone, Serialize, Deserialize)]
     |          ----- first implementation here
...
2159 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |          ^^^^^ conflicting implementation for `vultr::models::VultrOS`
     |
     = note: this error originates in the derive macro `Debug` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0119]: conflicting implementations of trait `std::fmt::Debug` for type `vultr::models::VultrOSResponse`
    --> src\vultr\models.rs:2167:10
     |
178  | #[derive(Debug, Clone, Serialize, Deserialize)]
     |          ----- first implementation here
...
2167 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |          ^^^^^ conflicting implementation for `vultr::models::VultrOSResponse`
     |
     = note: this error originates in the derive macro `Debug` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0119]: conflicting implementations of trait `std::fmt::Debug` for type `vultr::models::VultrPlan`
    --> src\vultr\models.rs:2174:10
     |
133  | #[derive(Debug, Clone, Serialize, Deserialize)]
     |          ----- first implementation here
...
2174 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |          ^^^^^ conflicting implementation for `vultr::models::VultrPlan`
     |
     = note: this error originates in the derive macro `Debug` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0119]: conflicting implementations of trait `std::fmt::Debug` for type `vultr::models::VultrPlansResponse`
    --> src\vultr\models.rs:2188:10
     |
147  | #[derive(Debug, Clone, Serialize, Deserialize)]
     |          ----- first implementation here
...
2188 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |          ^^^^^ conflicting implementation for `vultr::models::VultrPlansResponse`
     |
     = note: this error originates in the derive macro `Debug` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0119]: conflicting implementations of trait `std::fmt::Debug` for type `vultr::models::VultrRegion`
    --> src\vultr\models.rs:2320:10
     |
154  | #[derive(Debug, Clone, Serialize, Deserialize)]
     |          ----- first implementation here
...
2320 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |          ^^^^^ conflicting implementation for `vultr::models::VultrRegion`
     |
     = note: this error originates in the derive macro `Debug` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0119]: conflicting implementations of trait `std::fmt::Debug` for type `vultr::models::VultrRegionsResponse`
    --> src\vultr\models.rs:2329:10
     |
163  | #[derive(Debug, Clone, Serialize, Deserialize)]
     |          ----- first implementation here
...
2329 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |          ^^^^^ conflicting implementation for `vultr::models::VultrRegionsResponse`
     |
     = note: this error originates in the derive macro `Debug` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0119]: conflicting implementations of trait `std::fmt::Debug` for type `vultr::models::VultrSSHKey`
    --> src\vultr\models.rs:2400:10
     |
185  | #[derive(Debug, Clone, Serialize, Deserialize)]
     |          ----- first implementation here
...
2400 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |          ^^^^^ conflicting implementation for `vultr::models::VultrSSHKey`
     |
     = note: this error originates in the derive macro `Debug` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0119]: conflicting implementations of trait `std::fmt::Debug` for type `vultr::models::VultrSSHKeysResponse`
    --> src\vultr\models.rs:2408:10
     |
193  | #[derive(Debug, Clone, Serialize, Deserialize)]
     |          ----- first implementation here
...
2408 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |          ^^^^^ conflicting implementation for `vultr::models::VultrSSHKeysResponse`
     |
     = note: this error originates in the derive macro `Debug` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0119]: conflicting implementations of trait `std::fmt::Debug` for type `vultr::models::VultrSnapshot`
    --> src\vultr\models.rs:2611:10
     |
2341 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |          ----- first implementation here
...
2611 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |          ^^^^^ conflicting implementation for `vultr::models::VultrSnapshot`
     |
     = note: this error originates in the derive macro `Debug` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0119]: conflicting implementations of trait `std::fmt::Debug` for type `vultr::models::VultrStartupScript`
    --> src\vultr\models.rs:2635:10
     |
2427 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |          ----- first implementation here
...
2635 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |          ^^^^^ conflicting implementation for `vultr::models::VultrStartupScript`
     |
     = note: this error originates in the derive macro `Debug` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0119]: conflicting implementations of trait `std::fmt::Debug` for type `vultr::models::VultrISO`
    --> src\vultr\models.rs:2647:10
     |
1331 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |          ----- first implementation here
...
2647 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |          ^^^^^ conflicting implementation for `vultr::models::VultrISO`
     |
     = note: this error originates in the derive macro `Debug` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0119]: conflicting implementations of trait `std::fmt::Debug` for type `vultr::models::VultrStorageGateway`
    --> src\vultr\models.rs:2704:10
     |
2459 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |          ----- first implementation here
...
2704 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |          ^^^^^ conflicting implementation for `vultr::models::VultrStorageGateway`
     |
     = note: this error originates in the derive macro `Debug` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0119]: conflicting implementations of trait `std::fmt::Debug` for type `vultr::models::VultrUser`
    --> src\vultr\models.rs:2718:10
     |
2503 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |          ----- first implementation here
...
2718 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |          ^^^^^ conflicting implementation for `vultr::models::VultrUser`
     |
     = note: this error originates in the derive macro `Debug` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0119]: conflicting implementations of trait `Serialize` for type `vultr::models::VultrObjectStorage`
    --> src\vultr\models.rs:2099:24
     |
571  | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                        --------- first implementation here
...
2099 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                        ^^^^^^^^^ conflicting implementation for `vultr::models::VultrObjectStorage`
     |
     = note: this error originates in the derive macro `Serialize` (in Nightly builds, run with -Z macro-backtrace for more 
info)

error[E0119]: conflicting implementations of trait `Serialize` for type `vultr::models::VultrOS`
    --> src\vultr\models.rs:2159:24
     |
170  | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                        --------- first implementation here
...
2159 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                        ^^^^^^^^^ conflicting implementation for `vultr::models::VultrOS`
     |
     = note: this error originates in the derive macro `Serialize` (in Nightly builds, run with -Z macro-backtrace for more 
info)

error[E0119]: conflicting implementations of trait `Serialize` for type `vultr::models::VultrOSResponse`
    --> src\vultr\models.rs:2167:24
     |
178  | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                        --------- first implementation here
...
2167 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                        ^^^^^^^^^ conflicting implementation for `vultr::models::VultrOSResponse`
     |
     = note: this error originates in the derive macro `Serialize` (in Nightly builds, run with -Z macro-backtrace for more 
info)

error[E0119]: conflicting implementations of trait `Serialize` for type `vultr::models::VultrPlan`
    --> src\vultr\models.rs:2174:24
     |
133  | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                        --------- first implementation here
...
2174 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                        ^^^^^^^^^ conflicting implementation for `vultr::models::VultrPlan`
     |
     = note: this error originates in the derive macro `Serialize` (in Nightly builds, run with -Z macro-backtrace for more 
info)

error[E0119]: conflicting implementations of trait `Serialize` for type `vultr::models::VultrPlansResponse`
    --> src\vultr\models.rs:2188:24
     |
147  | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                        --------- first implementation here
...
2188 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                        ^^^^^^^^^ conflicting implementation for `vultr::models::VultrPlansResponse`
     |
     = note: this error originates in the derive macro `Serialize` (in Nightly builds, run with -Z macro-backtrace for more 
info)

error[E0119]: conflicting implementations of trait `Serialize` for type `vultr::models::VultrRegion`
    --> src\vultr\models.rs:2320:24
     |
154  | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                        --------- first implementation here
...
2320 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                        ^^^^^^^^^ conflicting implementation for `vultr::models::VultrRegion`
     |
     = note: this error originates in the derive macro `Serialize` (in Nightly builds, run with -Z macro-backtrace for more 
info)

error[E0119]: conflicting implementations of trait `Serialize` for type `vultr::models::VultrRegionsResponse`
    --> src\vultr\models.rs:2329:24
     |
163  | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                        --------- first implementation here
...
2329 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                        ^^^^^^^^^ conflicting implementation for `vultr::models::VultrRegionsResponse`
     |
     = note: this error originates in the derive macro `Serialize` (in Nightly builds, run with -Z macro-backtrace for more 
info)

error[E0119]: conflicting implementations of trait `Serialize` for type `vultr::models::VultrSSHKey`
    --> src\vultr\models.rs:2400:24
     |
185  | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                        --------- first implementation here
...
2400 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                        ^^^^^^^^^ conflicting implementation for `vultr::models::VultrSSHKey`
     |
     = note: this error originates in the derive macro `Serialize` (in Nightly builds, run with -Z macro-backtrace for more 
info)

error[E0119]: conflicting implementations of trait `Serialize` for type `vultr::models::VultrSSHKeysResponse`
    --> src\vultr\models.rs:2408:24
     |
193  | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                        --------- first implementation here
...
2408 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                        ^^^^^^^^^ conflicting implementation for `vultr::models::VultrSSHKeysResponse`
     |
     = note: this error originates in the derive macro `Serialize` (in Nightly builds, run with -Z macro-backtrace for more 
info)

error[E0119]: conflicting implementations of trait `Serialize` for type `vultr::models::VultrSnapshot`
    --> src\vultr\models.rs:2611:24
     |
2341 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                        --------- first implementation here
...
2611 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                        ^^^^^^^^^ conflicting implementation for `vultr::models::VultrSnapshot`
     |
     = note: this error originates in the derive macro `Serialize` (in Nightly builds, run with -Z macro-backtrace for more 
info)

error[E0119]: conflicting implementations of trait `Serialize` for type `vultr::models::VultrStartupScript`
    --> src\vultr\models.rs:2635:24
     |
2427 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                        --------- first implementation here
...
2635 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                        ^^^^^^^^^ conflicting implementation for `vultr::models::VultrStartupScript`
     |
     = note: this error originates in the derive macro `Serialize` (in Nightly builds, run with -Z macro-backtrace for more 
info)

error[E0119]: conflicting implementations of trait `Serialize` for type `vultr::models::VultrISO`
    --> src\vultr\models.rs:2647:24
     |
1331 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                        --------- first implementation here
...
2647 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                        ^^^^^^^^^ conflicting implementation for `vultr::models::VultrISO`
     |
     = note: this error originates in the derive macro `Serialize` (in Nightly builds, run with -Z macro-backtrace for more 
info)

error[E0119]: conflicting implementations of trait `Serialize` for type `vultr::models::VultrStorageGateway`
    --> src\vultr\models.rs:2704:24
     |
2459 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                        --------- first implementation here
...
2704 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                        ^^^^^^^^^ conflicting implementation for `vultr::models::VultrStorageGateway`
     |
     = note: this error originates in the derive macro `Serialize` (in Nightly builds, run with -Z macro-backtrace for more 
info)

error[E0119]: conflicting implementations of trait `Serialize` for type `vultr::models::VultrUser`
    --> src\vultr\models.rs:2718:24
     |
2503 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                        --------- first implementation here
...
2718 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                        ^^^^^^^^^ conflicting implementation for `vultr::models::VultrUser`
     |
     = note: this error originates in the derive macro `Serialize` (in Nightly builds, run with -Z macro-backtrace for more 
info)

error[E0119]: conflicting implementations of trait `Deserialize<'_>` for type `vultr::models::VultrObjectStorage`
    --> src\vultr\models.rs:2099:35
     |
571  | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                                   ----------- first implementation here
...
2099 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                                   ^^^^^^^^^^^ conflicting implementation for `vultr::models::VultrObjectStorage`
     |
     = note: this error originates in the derive macro `Deserialize` (in Nightly builds, run with -Z macro-backtrace for more 
info)

error[E0119]: conflicting implementations of trait `Deserialize<'_>` for type `vultr::models::VultrOS`
    --> src\vultr\models.rs:2159:35
     |
170  | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                                   ----------- first implementation here
...
2159 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                                   ^^^^^^^^^^^ conflicting implementation for `vultr::models::VultrOS`
     |
     = note: this error originates in the derive macro `Deserialize` (in Nightly builds, run with -Z macro-backtrace for more 
info)

error[E0119]: conflicting implementations of trait `Deserialize<'_>` for type `vultr::models::VultrOSResponse`
    --> src\vultr\models.rs:2167:35
     |
178  | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                                   ----------- first implementation here
...
2167 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                                   ^^^^^^^^^^^ conflicting implementation for `vultr::models::VultrOSResponse`
     |
     = note: this error originates in the derive macro `Deserialize` (in Nightly builds, run with -Z macro-backtrace for more 
info)

error[E0119]: conflicting implementations of trait `Deserialize<'_>` for type `vultr::models::VultrPlan`
    --> src\vultr\models.rs:2174:35
     |
133  | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                                   ----------- first implementation here
...
2174 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                                   ^^^^^^^^^^^ conflicting implementation for `vultr::models::VultrPlan`
     |
     = note: this error originates in the derive macro `Deserialize` (in Nightly builds, run with -Z macro-backtrace for more 
info)

error[E0119]: conflicting implementations of trait `Deserialize<'_>` for type `vultr::models::VultrPlansResponse`
    --> src\vultr\models.rs:2188:35
     |
147  | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                                   ----------- first implementation here
...
2188 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                                   ^^^^^^^^^^^ conflicting implementation for `vultr::models::VultrPlansResponse`
     |
     = note: this error originates in the derive macro `Deserialize` (in Nightly builds, run with -Z macro-backtrace for more 
info)

error[E0119]: conflicting implementations of trait `Deserialize<'_>` for type `vultr::models::VultrRegion`
    --> src\vultr\models.rs:2320:35
     |
154  | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                                   ----------- first implementation here
...
2320 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                                   ^^^^^^^^^^^ conflicting implementation for `vultr::models::VultrRegion`
     |
     = note: this error originates in the derive macro `Deserialize` (in Nightly builds, run with -Z macro-backtrace for more 
info)

error[E0119]: conflicting implementations of trait `Deserialize<'_>` for type `vultr::models::VultrRegionsResponse`
    --> src\vultr\models.rs:2329:35
     |
163  | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                                   ----------- first implementation here
...
2329 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                                   ^^^^^^^^^^^ conflicting implementation for `vultr::models::VultrRegionsResponse`
     |
     = note: this error originates in the derive macro `Deserialize` (in Nightly builds, run with -Z macro-backtrace for more 
info)

error[E0119]: conflicting implementations of trait `Deserialize<'_>` for type `vultr::models::VultrSSHKey`
    --> src\vultr\models.rs:2400:35
     |
185  | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                                   ----------- first implementation here
...
2400 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                                   ^^^^^^^^^^^ conflicting implementation for `vultr::models::VultrSSHKey`
     |
     = note: this error originates in the derive macro `Deserialize` (in Nightly builds, run with -Z macro-backtrace for more 
info)

error[E0119]: conflicting implementations of trait `Deserialize<'_>` for type `vultr::models::VultrSSHKeysResponse`
    --> src\vultr\models.rs:2408:35
     |
193  | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                                   ----------- first implementation here
...
2408 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                                   ^^^^^^^^^^^ conflicting implementation for `vultr::models::VultrSSHKeysResponse`
     |
     = note: this error originates in the derive macro `Deserialize` (in Nightly builds, run with -Z macro-backtrace for more 
info)

error[E0119]: conflicting implementations of trait `Deserialize<'_>` for type `vultr::models::VultrSnapshot`
    --> src\vultr\models.rs:2611:35
     |
2341 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                                   ----------- first implementation here
...
2611 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                                   ^^^^^^^^^^^ conflicting implementation for `vultr::models::VultrSnapshot`
     |
     = note: this error originates in the derive macro `Deserialize` (in Nightly builds, run with -Z macro-backtrace for more 
info)

error[E0119]: conflicting implementations of trait `Deserialize<'_>` for type `vultr::models::VultrStartupScript`
    --> src\vultr\models.rs:2635:35
     |
2427 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                                   ----------- first implementation here
...
2635 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                                   ^^^^^^^^^^^ conflicting implementation for `vultr::models::VultrStartupScript`
     |
     = note: this error originates in the derive macro `Deserialize` (in Nightly builds, run with -Z macro-backtrace for more 
info)

error[E0119]: conflicting implementations of trait `Deserialize<'_>` for type `vultr::models::VultrISO`
    --> src\vultr\models.rs:2647:35
     |
1331 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                                   ----------- first implementation here
...
2647 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                                   ^^^^^^^^^^^ conflicting implementation for `vultr::models::VultrISO`
     |
     = note: this error originates in the derive macro `Deserialize` (in Nightly builds, run with -Z macro-backtrace for more 
info)

error[E0119]: conflicting implementations of trait `Deserialize<'_>` for type `vultr::models::VultrStorageGateway`
    --> src\vultr\models.rs:2704:35
     |
2459 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                                   ----------- first implementation here
...
2704 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                                   ^^^^^^^^^^^ conflicting implementation for `vultr::models::VultrStorageGateway`
     |
     = note: this error originates in the derive macro `Deserialize` (in Nightly builds, run with -Z macro-backtrace for more 
info)

error[E0119]: conflicting implementations of trait `Deserialize<'_>` for type `vultr::models::VultrUser`
    --> src\vultr\models.rs:2718:35
     |
2503 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                                   ----------- first implementation here
...
2718 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                                   ^^^^^^^^^^^ conflicting implementation for `vultr::models::VultrUser`
     |
     = note: this error originates in the derive macro `Deserialize` (in Nightly builds, run with -Z macro-backtrace for more 
info)

error[E0107]: struct takes 0 generic arguments but 1 generic argument was supplied
   --> src\middleware\mod.rs:17:11
    |
17  |     next: Next<B>,
    |           ^^^^--- help: remove the unnecessary generics
    |           |
    |           expected 0 generic arguments
    |
note: struct defined here, with 0 generic parameters
   --> C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\axum-0.7.9\src\middleware\from_fn.rs:335:12
    |
335 | pub struct Next {
    |            ^^^^

error[E0107]: struct takes 0 generic arguments but 1 generic argument was supplied
   --> src\middleware\mod.rs:32:11
    |
32  |     next: Next<B>,
    |           ^^^^--- help: remove the unnecessary generics
    |           |
    |           expected 0 generic arguments
    |
note: struct defined here, with 0 generic parameters
   --> C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\axum-0.7.9\src\middleware\from_fn.rs:335:12
    |
335 | pub struct Next {
    |            ^^^^

error[E0107]: struct takes 0 generic arguments but 1 generic argument was supplied
   --> src\middleware\mod.rs:67:11
    |
67  |     next: Next<B>,
    |           ^^^^--- help: remove the unnecessary generics
    |           |
    |           expected 0 generic arguments
    |
note: struct defined here, with 0 generic parameters
   --> C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\axum-0.7.9\src\middleware\from_fn.rs:335:12
    |
335 | pub struct Next {
    |            ^^^^

error[E0107]: struct takes 0 generic arguments but 1 generic argument was supplied
   --> src\observability\mod.rs:96:29
    |
96  |     next: axum::middleware::Next<B>,
    |                             ^^^^--- help: remove the unnecessary generics
    |                             |
    |                             expected 0 generic arguments
    |
note: struct defined here, with 0 generic parameters
   --> C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\axum-0.7.9\src\middleware\from_fn.rs:335:12
    |
335 | pub struct Next {
    |            ^^^^

error[E0107]: struct takes 0 lifetime arguments but 1 lifetime argument was supplied
  --> src\services\billing.rs:21:10
   |
21 | impl<'a> BillingService<'a> {
   |          ^^^^^^^^^^^^^^---- help: remove the unnecessary generics
   |          |
   |          expected 0 lifetime arguments
   |
note: struct defined here, with 0 lifetime parameters
  --> src\services\billing.rs:15:12
   |
15 | pub struct BillingService {
   |            ^^^^^^^^^^^^^^

error[E0119]: conflicting implementations of trait `From<mongodb::error::Error>` for type `ControllerError`
  --> src\controllers\mod.rs:19:10
   |
19 | #[derive(Error, Debug)]
   |          ^^^^^ conflicting implementation for `ControllerError`
...
97 | impl From<mongodb::error::Error> for ControllerError {
   | ---------------------------------------------------- first implementation here
   |
   = note: this error originates in the derive macro `Error` (in Nightly builds, run with -Z macro-backtrace for more info)

warning: unused variable: `state`
  --> src\controllers\auth.rs:46:11
   |
46 |     State(state): State<Arc<AppState>>,
   |           ^^^^^ help: if this is intentional, prefix it with an underscore: `_state`
   |
   = note: `#[warn(unused_variables)]` on by default

error[E0599]: no function or associated item named `new` found for struct `BillingService` in the current scope
  --> src\controllers\billing.rs:23:43
   |
23 |     let billing_service = BillingService::new(&state.database);
   |                                           ^^^ function or associated item not found in `BillingService`
   |
  ::: src\services\billing.rs:15:1
   |
15 | pub struct BillingService {
   | ------------------------- function or associated item `new` not found for this struct
   |
   = help: items from traits can only be used if the trait is implemented and in scope
   = note: the following traits define an item `new`, perhaps you need to implement one of them:
           candidate #1: `ahash::HashMapExt`
           candidate #2: `ahash::HashSetExt`
           candidate #3: `bitvec::store::BitStore`
           candidate #4: `crypto_common::KeyInit`
           candidate #5: `crypto_common::KeyIvInit`
           candidate #6: `digest::VariableOutput`
           candidate #7: `digest::core_api::VariableOutputCore`
           candidate #8: `digest::digest::Digest`
           candidate #9: `digest::mac::Mac`
           candidate #10: `parking_lot_core::thread_parker::ThreadParkerT`
           candidate #11: `radium::Radium`
           candidate #12: `rand::distr::uniform::UniformSampler`
           candidate #13: `rand::distributions::uniform::UniformSampler`
           candidate #14: `ring::aead::BoundKey`
           candidate #15: `serde_with::duplicate_key_impls::error_on_duplicate::PreventDuplicateInsertsMap`
           candidate #16: `serde_with::duplicate_key_impls::error_on_duplicate::PreventDuplicateInsertsSet`
           candidate #17: `serde_with::duplicate_key_impls::first_value_wins::DuplicateInsertsFirstWinsMap`
           candidate #18: `serde_with::duplicate_key_impls::first_value_wins::DuplicateInsertsFirstWinsSet`
           candidate #19: `serde_with::duplicate_key_impls::last_value_wins::DuplicateInsertsLastWinsSet`
           candidate #20: `trust_dns_proto::Executor`
           candidate #21: `typenum::marker_traits::Bit`

error[E0599]: no function or associated item named `new` found for struct `BillingService` in the current scope
  --> src\controllers\billing.rs:41:43
   |
41 |     let billing_service = BillingService::new(&state.database);
   |                                           ^^^ function or associated item not found in `BillingService`
   |
  ::: src\services\billing.rs:15:1
   |
15 | pub struct BillingService {
   | ------------------------- function or associated item `new` not found for this struct
   |
   = help: items from traits can only be used if the trait is implemented and in scope
   = note: the following traits define an item `new`, perhaps you need to implement one of them:
           candidate #1: `ahash::HashMapExt`
           candidate #2: `ahash::HashSetExt`
           candidate #3: `bitvec::store::BitStore`
           candidate #4: `crypto_common::KeyInit`
           candidate #5: `crypto_common::KeyIvInit`
           candidate #6: `digest::VariableOutput`
           candidate #7: `digest::core_api::VariableOutputCore`
           candidate #8: `digest::digest::Digest`
           candidate #9: `digest::mac::Mac`
           candidate #10: `parking_lot_core::thread_parker::ThreadParkerT`
           candidate #11: `radium::Radium`
           candidate #12: `rand::distr::uniform::UniformSampler`
           candidate #13: `rand::distributions::uniform::UniformSampler`
           candidate #14: `ring::aead::BoundKey`
           candidate #15: `serde_with::duplicate_key_impls::error_on_duplicate::PreventDuplicateInsertsMap`
           candidate #16: `serde_with::duplicate_key_impls::error_on_duplicate::PreventDuplicateInsertsSet`
           candidate #17: `serde_with::duplicate_key_impls::first_value_wins::DuplicateInsertsFirstWinsMap`
           candidate #18: `serde_with::duplicate_key_impls::first_value_wins::DuplicateInsertsFirstWinsSet`
           candidate #19: `serde_with::duplicate_key_impls::last_value_wins::DuplicateInsertsLastWinsSet`
           candidate #20: `trust_dns_proto::Executor`
           candidate #21: `typenum::marker_traits::Bit`

error[E0599]: no function or associated item named `new` found for struct `BillingService` in the current scope
  --> src\controllers\billing.rs:57:43
   |
57 |     let billing_service = BillingService::new(&state.database);
   |                                           ^^^ function or associated item not found in `BillingService`
   |
  ::: src\services\billing.rs:15:1
   |
15 | pub struct BillingService {
   | ------------------------- function or associated item `new` not found for this struct
   |
   = help: items from traits can only be used if the trait is implemented and in scope
   = note: the following traits define an item `new`, perhaps you need to implement one of them:
           candidate #1: `ahash::HashMapExt`
           candidate #2: `ahash::HashSetExt`
           candidate #3: `bitvec::store::BitStore`
           candidate #4: `crypto_common::KeyInit`
           candidate #5: `crypto_common::KeyIvInit`
           candidate #6: `digest::VariableOutput`
           candidate #7: `digest::core_api::VariableOutputCore`
           candidate #8: `digest::digest::Digest`
           candidate #9: `digest::mac::Mac`
           candidate #10: `parking_lot_core::thread_parker::ThreadParkerT`
           candidate #11: `radium::Radium`
           candidate #12: `rand::distr::uniform::UniformSampler`
           candidate #13: `rand::distributions::uniform::UniformSampler`
           candidate #14: `ring::aead::BoundKey`
           candidate #15: `serde_with::duplicate_key_impls::error_on_duplicate::PreventDuplicateInsertsMap`
           candidate #16: `serde_with::duplicate_key_impls::error_on_duplicate::PreventDuplicateInsertsSet`
           candidate #17: `serde_with::duplicate_key_impls::first_value_wins::DuplicateInsertsFirstWinsMap`
           candidate #18: `serde_with::duplicate_key_impls::first_value_wins::DuplicateInsertsFirstWinsSet`
           candidate #19: `serde_with::duplicate_key_impls::last_value_wins::DuplicateInsertsLastWinsSet`
           candidate #20: `trust_dns_proto::Executor`
           candidate #21: `typenum::marker_traits::Bit`

error[E0599]: no function or associated item named `new` found for struct `BillingService` in the current scope
  --> src\controllers\billing.rs:73:43
   |
73 |     let billing_service = BillingService::new(&state.database);
   |                                           ^^^ function or associated item not found in `BillingService`
   |
  ::: src\services\billing.rs:15:1
   |
15 | pub struct BillingService {
   | ------------------------- function or associated item `new` not found for this struct
   |
   = help: items from traits can only be used if the trait is implemented and in scope
   = note: the following traits define an item `new`, perhaps you need to implement one of them:
           candidate #1: `ahash::HashMapExt`
           candidate #2: `ahash::HashSetExt`
           candidate #3: `bitvec::store::BitStore`
           candidate #4: `crypto_common::KeyInit`
           candidate #5: `crypto_common::KeyIvInit`
           candidate #6: `digest::VariableOutput`
           candidate #7: `digest::core_api::VariableOutputCore`
           candidate #8: `digest::digest::Digest`
           candidate #9: `digest::mac::Mac`
           candidate #10: `parking_lot_core::thread_parker::ThreadParkerT`
           candidate #11: `radium::Radium`
           candidate #12: `rand::distr::uniform::UniformSampler`
           candidate #13: `rand::distributions::uniform::UniformSampler`
           candidate #14: `ring::aead::BoundKey`
           candidate #15: `serde_with::duplicate_key_impls::error_on_duplicate::PreventDuplicateInsertsMap`
           candidate #16: `serde_with::duplicate_key_impls::error_on_duplicate::PreventDuplicateInsertsSet`
           candidate #17: `serde_with::duplicate_key_impls::first_value_wins::DuplicateInsertsFirstWinsMap`
           candidate #18: `serde_with::duplicate_key_impls::first_value_wins::DuplicateInsertsFirstWinsSet`
           candidate #19: `serde_with::duplicate_key_impls::last_value_wins::DuplicateInsertsLastWinsSet`
           candidate #20: `trust_dns_proto::Executor`
           candidate #21: `typenum::marker_traits::Bit`

error[E0560]: struct `vultr::models::VultrPlan` has no field named `type_`
   --> src\controllers\vultr.rs:131:13
    |
131 |             type_: plan.plan_type,
    |             ^^^^^ `vultr::models::VultrPlan` does not have this field
    |
    = note: available fields are: `plan_type`, `disk_count`

error[E0277]: a value of type `Vec<models::instance::VultrPlan>` cannot be built from an iterator over elements of type 
`vultr::models::VultrPlan`
    --> src\controllers\vultr.rs:134:10
     |
134  |         .collect();
     |          ^^^^^^^ value of type `Vec<models::instance::VultrPlan>` cannot be built from 
`std::iter::Iterator<Item=vultr::models::VultrPlan>`
     |
     = help: the trait `FromIterator<vultr::models::VultrPlan>` is not implemented for `Vec<models::instance::VultrPlan>`
             but trait `FromIterator<models::instance::VultrPlan>` is implemented for it
     = help: for that trait implementation, expected `models::instance::VultrPlan`, found `vultr::models::VultrPlan`
note: the method call chain might not have had the expected associated types
    --> src\controllers\vultr.rs:123:10
     |
115  |       let plans = state
     |  _________________-
116  | |         .vultr_client
117  | |         .list_plans()
118  | |         .await
119  | |         .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("Vultr API error: {}", e)))?;
     | |__________________________________________________________________________________________________________- this 
expression has type `Vec<VultrPlan>`
...
123  |           .into_iter()
     |            ^^^^^^^^^^^ `Iterator::Item` is `VultrPlan` here
124  |           .map(|plan| crate::vultr::models::VultrPlan {
     |  __________-
125  | |             id: plan.id,
126  | |             vcpu_count: plan.vcpu_count,
127  | |             ram: plan.ram,
...    |
132  | |             locations: plan.locations,
133  | |         })
     | |__________- `Iterator::Item` remains `VultrPlan` here
note: required by a bound in `std::iter::Iterator::collect`
    --> C:\Users\<USER>\.rustup\toolchains\stable-x86_64-pc-windows-gnu\lib/rustlib/src/rust\library\core\src\iter\traits\iterator
.rs:1967:19
     |
1967 |     fn collect<B: FromIterator<Self::Item>>(self) -> B
     |                   ^^^^^^^^^^^^^^^^^^^^^^^^ required by this bound in `Iterator::collect`

error[E0308]: mismatched types
   --> src\controllers\vultr.rs:136:25
    |
136 |     Ok(success_response(converted_plans))
    |        ---------------- ^^^^^^^^^^^^^^^ expected `vultr::models::VultrPlan`, found `models::instance::VultrPlan`
    |        |
    |        arguments to this function are incorrect
    |
    = note: `models::instance::VultrPlan` and `vultr::models::VultrPlan` have similar names, but are actually distinct types
note: `models::instance::VultrPlan` is defined in module `crate::models::instance` of the current crate
   --> src\models\instance.rs:101:1
    |
101 | pub struct VultrPlan {
    | ^^^^^^^^^^^^^^^^^^^^
note: `vultr::models::VultrPlan` is defined in module `crate::vultr::models` of the current crate
   --> src\vultr\models.rs:134:1
    |
134 | pub struct VultrPlan {
    | ^^^^^^^^^^^^^^^^^^^^
help: the return type of this call is `Vec<models::instance::VultrPlan>` due to the type of the argument passed
   --> src\controllers\vultr.rs:136:8
    |
136 |     Ok(success_response(converted_plans))
    |        ^^^^^^^^^^^^^^^^^---------------^
    |                         |
    |                         this argument influences the return type of `success_response`
note: function defined here
   --> src\controllers\mod.rs:76:8
    |
76  | pub fn success_response<T>(data: T) -> Json<ApiResponse<T>> {
    |        ^^^^^^^^^^^^^^^^    -------

error[E0277]: a value of type `Vec<models::instance::VultrRegion>` cannot be built from an iterator over elements of type 
`vultr::models::VultrRegion`
    --> src\controllers\vultr.rs:159:10
     |
159  |         .collect();
     |          ^^^^^^^ value of type `Vec<models::instance::VultrRegion>` cannot be built from 
`std::iter::Iterator<Item=vultr::models::VultrRegion>`
     |
     = help: the trait `FromIterator<vultr::models::VultrRegion>` is not implemented for `Vec<models::instance::VultrRegion>`
             but trait `FromIterator<models::instance::VultrRegion>` is implemented for it
     = help: for that trait implementation, expected `models::instance::VultrRegion`, found `vultr::models::VultrRegion`
note: the method call chain might not have had the expected associated types
    --> src\controllers\vultr.rs:151:10
     |
143  |       let regions = state
     |  ___________________-
144  | |         .vultr_client
145  | |         .list_regions()
146  | |         .await
147  | |         .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("Vultr API error: {}", e)))?;
     | |__________________________________________________________________________________________________________- this 
expression has type `Vec<VultrRegion>`
...
151  |           .into_iter()
     |            ^^^^^^^^^^^ `Iterator::Item` is `VultrRegion` here
152  |           .map(|region| crate::vultr::models::VultrRegion {
     |  __________-
153  | |             id: region.id,
154  | |             city: region.city,
155  | |             country: region.country,
156  | |             continent: region.continent,
157  | |             options: region.options,
158  | |         })
     | |__________- `Iterator::Item` remains `VultrRegion` here
note: required by a bound in `std::iter::Iterator::collect`
    --> C:\Users\<USER>\.rustup\toolchains\stable-x86_64-pc-windows-gnu\lib/rustlib/src/rust\library\core\src\iter\traits\iterator
.rs:1967:19
     |
1967 |     fn collect<B: FromIterator<Self::Item>>(self) -> B
     |                   ^^^^^^^^^^^^^^^^^^^^^^^^ required by this bound in `Iterator::collect`

error[E0308]: mismatched types
   --> src\controllers\vultr.rs:161:25
    |
161 |     Ok(success_response(converted_regions))
    |        ---------------- ^^^^^^^^^^^^^^^^^ expected `vultr::models::VultrRegion`, found `models::instance::VultrRegion`
    |        |
    |        arguments to this function are incorrect
    |
    = note: `models::instance::VultrRegion` and `vultr::models::VultrRegion` have similar names, but are actually distinct types
note: `models::instance::VultrRegion` is defined in module `crate::models::instance` of the current crate
   --> src\models\instance.rs:113:1
    |
113 | pub struct VultrRegion {
    | ^^^^^^^^^^^^^^^^^^^^^^
note: `vultr::models::VultrRegion` is defined in module `crate::vultr::models` of the current crate
   --> src\vultr\models.rs:155:1
    |
155 | pub struct VultrRegion {
    | ^^^^^^^^^^^^^^^^^^^^^^
help: the return type of this call is `Vec<models::instance::VultrRegion>` due to the type of the argument passed
   --> src\controllers\vultr.rs:161:8
    |
161 |     Ok(success_response(converted_regions))
    |        ^^^^^^^^^^^^^^^^^-----------------^
    |                         |
    |                         this argument influences the return type of `success_response`
note: function defined here
   --> src\controllers\mod.rs:76:8
    |
76  | pub fn success_response<T>(data: T) -> Json<ApiResponse<T>> {
    |        ^^^^^^^^^^^^^^^^    -------

error[E0277]: a value of type `Vec<models::instance::VultrOS>` cannot be built from an iterator over elements of type 
`vultr::models::VultrOS`
    --> src\controllers\vultr.rs:183:10
     |
183  |         .collect();
     |          ^^^^^^^ value of type `Vec<models::instance::VultrOS>` cannot be built from 
`std::iter::Iterator<Item=vultr::models::VultrOS>`
     |
     = help: the trait `FromIterator<vultr::models::VultrOS>` is not implemented for `Vec<models::instance::VultrOS>`
             but trait `FromIterator<models::instance::VultrOS>` is implemented for it
     = help: for that trait implementation, expected `models::instance::VultrOS`, found `vultr::models::VultrOS`
note: the method call chain might not have had the expected associated types
    --> src\controllers\vultr.rs:176:10
     |
168  |       let os_list = state
     |  ___________________-
169  | |         .vultr_client
170  | |         .list_os()
171  | |         .await
172  | |         .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("Vultr API error: {}", e)))?;
     | |__________________________________________________________________________________________________________- this 
expression has type `Vec<VultrOS>`
...
176  |           .into_iter()
     |            ^^^^^^^^^^^ `Iterator::Item` is `VultrOS` here
177  |           .map(|os| crate::vultr::models::VultrOS {
     |  __________-
178  | |             id: os.id,
179  | |             name: os.name,
180  | |             arch: os.arch,
181  | |             family: os.family,
182  | |         })
     | |__________- `Iterator::Item` remains `VultrOS` here
note: required by a bound in `std::iter::Iterator::collect`
    --> C:\Users\<USER>\.rustup\toolchains\stable-x86_64-pc-windows-gnu\lib/rustlib/src/rust\library\core\src\iter\traits\iterator
.rs:1967:19
     |
1967 |     fn collect<B: FromIterator<Self::Item>>(self) -> B
     |                   ^^^^^^^^^^^^^^^^^^^^^^^^ required by this bound in `Iterator::collect`

error[E0308]: mismatched types
   --> src\controllers\vultr.rs:185:25
    |
185 |     Ok(success_response(converted_os))
    |        ---------------- ^^^^^^^^^^^^ expected `vultr::models::VultrOS`, found `models::instance::VultrOS`
    |        |
    |        arguments to this function are incorrect
    |
    = note: `models::instance::VultrOS` and `vultr::models::VultrOS` have similar names, but are actually distinct types
note: `models::instance::VultrOS` is defined in module `crate::models::instance` of the current crate
   --> src\models\instance.rs:122:1
    |
122 | pub struct VultrOS {
    | ^^^^^^^^^^^^^^^^^^
note: `vultr::models::VultrOS` is defined in module `crate::vultr::models` of the current crate
   --> src\vultr\models.rs:171:1
    |
171 | pub struct VultrOS {
    | ^^^^^^^^^^^^^^^^^^
help: the return type of this call is `Vec<models::instance::VultrOS>` due to the type of the argument passed
   --> src\controllers\vultr.rs:185:8
    |
185 |     Ok(success_response(converted_os))
    |        ^^^^^^^^^^^^^^^^^------------^
    |                         |
    |                         this argument influences the return type of `success_response`
note: function defined here
   --> src\controllers\mod.rs:76:8
    |
76  | pub fn success_response<T>(data: T) -> Json<ApiResponse<T>> {
    |        ^^^^^^^^^^^^^^^^    -------

error[E0308]: mismatched types
   --> src\controllers\vultr.rs:450:25
    |
450 |     Ok(success_response(bare_metal))
    |        ---------------- ^^^^^^^^^^ expected `models::instance::VultrBareMetal`, found `vultr::models::VultrBareMetal`
    |        |
    |        arguments to this function are incorrect
    |
    = note: `vultr::models::VultrBareMetal` and `models::instance::VultrBareMetal` have similar names, but are actually 
distinct types
note: `vultr::models::VultrBareMetal` is defined in module `crate::vultr::models` of the current crate
   --> src\vultr\models.rs:230:1
    |
230 | pub struct VultrBareMetal {
    | ^^^^^^^^^^^^^^^^^^^^^^^^^
note: `models::instance::VultrBareMetal` is defined in module `crate::models::instance` of the current crate
   --> src\models\instance.rs:131:1
    |
131 | pub struct VultrBareMetal {
    | ^^^^^^^^^^^^^^^^^^^^^^^^^
help: the return type of this call is `vultr::models::VultrBareMetal` due to the type of the argument passed
   --> src\controllers\vultr.rs:450:8
    |
450 |     Ok(success_response(bare_metal))
    |        ^^^^^^^^^^^^^^^^^----------^
    |                         |
    |                         this argument influences the return type of `success_response`
note: function defined here
   --> src\controllers\mod.rs:76:8
    |
76  | pub fn success_response<T>(data: T) -> Json<ApiResponse<T>> {
    |        ^^^^^^^^^^^^^^^^    -------

error[E0308]: mismatched types
   --> src\middleware\mod.rs:25:29
    |
15  | pub async fn request_id_middleware<B>(
    |                                    - found this type parameter
...
25  |     let response = next.run(req).await;
    |                         --- ^^^ expected `Request<Body>`, found `Request<B>`
    |                         |
    |                         arguments to this method are incorrect
    |
    = note: expected struct `axum::http::Request<axum::body::Body>`
               found struct `axum::http::Request<B>`
note: method defined here
   --> C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\axum-0.7.9\src\middleware\from_fn.rs:341:18
    |
341 |     pub async fn run(mut self, req: Request) -> Response {
    |                  ^^^

error[E0308]: mismatched types
   --> src\middleware\mod.rs:38:29
    |
30  | pub async fn timing_middleware<B>(
    |                                - found this type parameter
...
38  |     let response = next.run(req).await;
    |                         --- ^^^ expected `Request<Body>`, found `Request<B>`
    |                         |
    |                         arguments to this method are incorrect
    |
    = note: expected struct `axum::http::Request<axum::body::Body>`
               found struct `axum::http::Request<B>`
note: method defined here
   --> C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\axum-0.7.9\src\middleware\from_fn.rs:341:18
    |
341 |     pub async fn run(mut self, req: Request) -> Response {
    |                  ^^^

error[E0308]: mismatched types
   --> src\middleware\mod.rs:69:29
    |
65  | pub async fn error_handling_middleware<B>(
    |                                        - found this type parameter
...
69  |     let response = next.run(req).await;
    |                         --- ^^^ expected `Request<Body>`, found `Request<B>`
    |                         |
    |                         arguments to this method are incorrect
    |
    = note: expected struct `axum::http::Request<axum::body::Body>`
               found struct `axum::http::Request<B>`
note: method defined here
   --> C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\axum-0.7.9\src\middleware\from_fn.rs:341:18
    |
341 |     pub async fn run(mut self, req: Request) -> Response {
    |                  ^^^

error[E0308]: mismatched types
   --> src\observability\mod.rs:102:29
    |
94  | pub async fn record_http_metrics<B>(
    |                                  - found this type parameter
...
102 |     let response = next.run(req).await;
    |                         --- ^^^ expected `Request<Body>`, found `Request<B>`
    |                         |
    |                         arguments to this method are incorrect
    |
    = note: expected struct `axum::http::Request<axum::body::Body>`
               found struct `axum::http::Request<B>`
note: method defined here
   --> C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\axum-0.7.9\src\middleware\from_fn.rs:341:18
    |
341 |     pub async fn run(mut self, req: Request) -> Response {
    |                  ^^^

error[E0599]: `impl Future<Output = Result<Cursor<Instance>, Error>>` is not an iterator
  --> src\services\instance.rs:53:14
   |
50 |           let instances: Vec<Instance> = self
   |  ________________________________________-
51 | |             .instances
52 | |             .find(doc! { "user_id": user_object_id }, None)
53 | |             .skip(pagination_info.offset())
   | |             -^^^^ `impl Future<Output = Result<Cursor<Instance>, Error>>` is not an iterator
   | |_____________|
   |
   |
   = note: the following trait bounds were not satisfied:
           `impl futures::Future<Output = Result<mongodb::Cursor<models::instance::Instance>, mongodb::error::Error>>: Iterator`
           which is required by `&mut impl futures::Future<Output = Result<mongodb::Cursor<models::instance::Instance>, 
mongodb::error::Error>>: Iterator`

warning: unused variable: `instance`
   --> src\services\instance.rs:210:13
    |
210 |         let instance = self
    |             ^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_instance`

error[E0308]: mismatched types
   --> src\utils\retry.rs:111:17
    |
110 |             .map_err(|e| match e {
    |                                - this expression has type `RetryError`
111 |                 backoff::Error::Permanent(err) => err,
    |                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ expected `RetryError`, found `Error<_>`
    |
    = note: expected enum `RetryError`
               found enum `backoff::Error<_>`

error[E0308]: mismatched types
   --> src\utils\retry.rs:112:17
    |
110 |             .map_err(|e| match e {
    |                                - this expression has type `RetryError`
111 |                 backoff::Error::Permanent(err) => err,
112 |                 backoff::Error::Transient { err, .. } => err,
    |                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ expected `RetryError`, found `Error<_>`
    |
    = note: expected enum `RetryError`
               found enum `backoff::Error<_>`

error[E0308]: mismatched types
   --> src\vultr\mod.rs:75:12
    |
75  |         Ok(instances_response.instances)
    |         -- ^^^^^^^^^^^^^^^^^^^^^^^^^^^^ expected `Vec<VultrInstance>`, found `Vec<VultrInstanceDetailed>`
    |         |
    |         arguments to this enum variant are incorrect
    |
    = note: expected struct `Vec<vultr::models::VultrInstance>`
               found struct `Vec<vultr::models::VultrInstanceDetailed>`
help: the type constructed contains `Vec<vultr::models::VultrInstanceDetailed>` due to the type of the argument passed
   --> src\vultr\mod.rs:75:9
    |
75  |         Ok(instances_response.instances)
    |         ^^^----------------------------^
    |            |
    |            this argument influences the type of `Ok`
note: tuple variant defined here
   --> C:\Users\<USER>\.rustup\toolchains\stable-x86_64-pc-windows-gnu\lib/rustlib/src/rust\library\core\src\result.rs:532:5
    |
532 |     Ok(#[stable(feature = "rust1", since = "1.0.0")] T),
    |     ^^

error[E0599]: no method named `json` found for opaque type `impl futures::Future<Output = utils::retry::RequestBuilder>` in the 
current scope
  --> src\vultr\mod.rs:90:47
   |
90 |         let response = self.client.post(&url).json(&request).send().await?;
   |                                               ^^^^ method not found in `impl Future<Output = RequestBuilder>`
   |
help: consider `await`ing on the `Future` and calling the method on its `Output`
   |
90 |         let response = self.client.post(&url).await.json(&request).send().await?;
   |                                               ++++++

error[E0599]: no method named `send` found for opaque type `impl futures::Future<Output = utils::retry::RequestBuilder>` in the 
current scope
   --> src\vultr\mod.rs:121:47
    |
121 |         let response = self.client.post(&url).send().await?;
    |                                               ^^^^ method not found in `impl Future<Output = RequestBuilder>`
    |
help: consider `await`ing on the `Future` and calling the method on its `Output`
    |
121 |         let response = self.client.post(&url).await.send().await?;
    |                                               ++++++

error[E0599]: no method named `send` found for opaque type `impl futures::Future<Output = utils::retry::RequestBuilder>` in the 
current scope
   --> src\vultr\mod.rs:136:47
    |
136 |         let response = self.client.post(&url).send().await?;
    |                                               ^^^^ method not found in `impl Future<Output = RequestBuilder>`
    |
help: consider `await`ing on the `Future` and calling the method on its `Output`
    |
136 |         let response = self.client.post(&url).await.send().await?;
    |                                               ++++++

error[E0599]: no method named `send` found for opaque type `impl futures::Future<Output = utils::retry::RequestBuilder>` in the 
current scope
   --> src\vultr\mod.rs:151:47
    |
151 |         let response = self.client.post(&url).send().await?;
    |                                               ^^^^ method not found in `impl Future<Output = RequestBuilder>`
    |
help: consider `await`ing on the `Future` and calling the method on its `Output`
    |
151 |         let response = self.client.post(&url).await.send().await?;
    |                                               ++++++

error[E0599]: no method named `json` found for opaque type `impl futures::Future<Output = utils::retry::RequestBuilder>` in the 
current scope
   --> src\vultr\mod.rs:250:47
    |
250 |         let response = self.client.post(&url).json(&request).send().await?;
    |                                               ^^^^ method not found in `impl Future<Output = RequestBuilder>`
    |
help: consider `await`ing on the `Future` and calling the method on its `Output`
    |
250 |         let response = self.client.post(&url).await.json(&request).send().await?;
    |                                               ++++++

error[E0599]: no method named `create_bare_metal` found for struct `Arc<VultrApiClient>` in the current scope
  --> src\vultr\client.rs:91:25
   |
91 |         self.api_client.create_bare_metal(request).await
   |                         ^^^^^^^^^^^^^^^^^
   |
help: there is a method `get_bare_metal` with a similar name
   |
91 -         self.api_client.create_bare_metal(request).await
91 +         self.api_client.get_bare_metal(request).await
   |

error[E0599]: no method named `update_bare_metal` found for struct `Arc<VultrApiClient>` in the current scope
   --> src\vultr\client.rs:101:25
    |
101 |         self.api_client.update_bare_metal(baremetal_id, request).await
    |                         ^^^^^^^^^^^^^^^^^
    |
help: there is a method `get_bare_metal` with a similar name, but with different arguments
   --> src\vultr\mod.rs:220:5
    |
220 |     pub async fn get_bare_metal(&self, bare_metal_id: &str) -> Result<VultrBareMetal> {
    |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

error[E0599]: no method named `delete_bare_metal` found for struct `Arc<VultrApiClient>` in the current scope
   --> src\vultr\client.rs:106:25
    |
106 |         self.api_client.delete_bare_metal(baremetal_id).await
    |                         ^^^^^^^^^^^^^^^^^
    |
help: there is a method `get_bare_metal` with a similar name
    |
106 -         self.api_client.delete_bare_metal(baremetal_id).await
106 +         self.api_client.get_bare_metal(baremetal_id).await
    |

error[E0599]: no method named `get_bare_metal_ipv4` found for struct `Arc<VultrApiClient>` in the current scope
   --> src\vultr\client.rs:111:25
    |
111 |         self.api_client.get_bare_metal_ipv4(baremetal_id).await
    |                         ^^^^^^^^^^^^^^^^^^^
    |
help: there is a method `get_bare_metal` with a similar name
    |
111 -         self.api_client.get_bare_metal_ipv4(baremetal_id).await
111 +         self.api_client.get_bare_metal(baremetal_id).await
    |

error[E0599]: no method named `get_bare_metal_ipv6` found for struct `Arc<VultrApiClient>` in the current scope
   --> src\vultr\client.rs:116:25
    |
116 |         self.api_client.get_bare_metal_ipv6(baremetal_id).await
    |                         ^^^^^^^^^^^^^^^^^^^
    |
help: there is a method `get_bare_metal` with a similar name
    |
116 -         self.api_client.get_bare_metal_ipv6(baremetal_id).await
116 +         self.api_client.get_bare_metal(baremetal_id).await
    |

error[E0599]: no method named `create_bare_metal_ipv4_reverse` found for struct `Arc<VultrApiClient>` in the current scope
   --> src\vultr\client.rs:121:25
    |
121 |         self.api_client.create_bare_metal_ipv4_reverse(baremetal_id, ip, reverse).await
    |                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ method not found in `Arc<VultrApiClient>`

error[E0599]: no method named `create_bare_metal_ipv6_reverse` found for struct `Arc<VultrApiClient>` in the current scope
   --> src\vultr\client.rs:126:25
    |
126 |         self.api_client.create_bare_metal_ipv6_reverse(baremetal_id, ip, reverse).await
    |                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ method not found in `Arc<VultrApiClient>`

error[E0599]: no method named `reset_bare_metal_ipv4_reverse` found for struct `Arc<VultrApiClient>` in the current scope
   --> src\vultr\client.rs:131:25
    |
131 |         self.api_client.reset_bare_metal_ipv4_reverse(baremetal_id, ip).await
    |                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
help: there is a method `list_bare_metal` with a similar name, but with different arguments
   --> src\vultr\mod.rs:211:5
    |
211 |     pub async fn list_bare_metal(&self) -> Result<Vec<VultrBareMetal>> {
    |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

error[E0599]: no method named `delete_bare_metal_ipv6_reverse` found for struct `Arc<VultrApiClient>` in the current scope
   --> src\vultr\client.rs:136:25
    |
136 |         self.api_client.delete_bare_metal_ipv6_reverse(baremetal_id, ip).await
    |                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ method not found in `Arc<VultrApiClient>`

error[E0599]: no method named `start_bare_metal` found for struct `Arc<VultrApiClient>` in the current scope
   --> src\vultr\client.rs:141:25
    |
141 |         self.api_client.start_bare_metal(baremetal_id).await
    |                         ^^^^^^^^^^^^^^^^
    |
help: there is a method `get_bare_metal` with a similar name
    |
141 -         self.api_client.start_bare_metal(baremetal_id).await
141 +         self.api_client.get_bare_metal(baremetal_id).await
    |

error[E0599]: no method named `reboot_bare_metal` found for struct `Arc<VultrApiClient>` in the current scope
   --> src\vultr\client.rs:146:25
    |
146 |         self.api_client.reboot_bare_metal(baremetal_id).await
    |                         ^^^^^^^^^^^^^^^^^
    |
help: there is a method `get_bare_metal` with a similar name
    |
146 -         self.api_client.reboot_bare_metal(baremetal_id).await
146 +         self.api_client.get_bare_metal(baremetal_id).await
    |

error[E0599]: no method named `reinstall_bare_metal` found for struct `Arc<VultrApiClient>` in the current scope
   --> src\vultr\client.rs:151:25
    |
151 |         self.api_client.reinstall_bare_metal(baremetal_id).await
    |                         ^^^^^^^^^^^^^^^^^^^^
    |
help: there is a method `list_bare_metal` with a similar name, but with different arguments
   --> src\vultr\mod.rs:211:5
    |
211 |     pub async fn list_bare_metal(&self) -> Result<Vec<VultrBareMetal>> {
    |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

error[E0599]: no method named `halt_bare_metal` found for struct `Arc<VultrApiClient>` in the current scope
   --> src\vultr\client.rs:156:25
    |
156 |         self.api_client.halt_bare_metal(baremetal_id).await
    |                         ^^^^^^^^^^^^^^^
    |
help: there is a method `get_bare_metal` with a similar name
    |
156 -         self.api_client.halt_bare_metal(baremetal_id).await
156 +         self.api_client.get_bare_metal(baremetal_id).await
    |

error[E0599]: no method named `get_bare_metal_bandwidth` found for struct `Arc<VultrApiClient>` in the current scope
   --> src\vultr\client.rs:161:25
    |
161 |         self.api_client.get_bare_metal_bandwidth(baremetal_id).await
    |                         ^^^^^^^^^^^^^^^^^^^^^^^^
    |
help: there is a method `get_bare_metal` with a similar name
    |
161 -         self.api_client.get_bare_metal_bandwidth(baremetal_id).await
161 +         self.api_client.get_bare_metal(baremetal_id).await
    |

error[E0599]: no method named `halt_bare_metals` found for struct `Arc<VultrApiClient>` in the current scope
   --> src\vultr\client.rs:166:25
    |
166 |         self.api_client.halt_bare_metals(instance_ids).await
    |                         ^^^^^^^^^^^^^^^^
    |
help: there is a method `get_bare_metal` with a similar name
    |
166 -         self.api_client.halt_bare_metals(instance_ids).await
166 +         self.api_client.get_bare_metal(instance_ids).await
    |

error[E0599]: no method named `reboot_bare_metals` found for struct `Arc<VultrApiClient>` in the current scope
   --> src\vultr\client.rs:171:25
    |
171 |         self.api_client.reboot_bare_metals(instance_ids).await
    |                         ^^^^^^^^^^^^^^^^^^
    |
help: there is a method `get_bare_metal` with a similar name
    |
171 -         self.api_client.reboot_bare_metals(instance_ids).await
171 +         self.api_client.get_bare_metal(instance_ids).await
    |

error[E0599]: no method named `start_bare_metals` found for struct `Arc<VultrApiClient>` in the current scope
   --> src\vultr\client.rs:176:25
    |
176 |         self.api_client.start_bare_metals(instance_ids).await
    |                         ^^^^^^^^^^^^^^^^^
    |
help: there is a method `get_bare_metal` with a similar name
    |
176 -         self.api_client.start_bare_metals(instance_ids).await
176 +         self.api_client.get_bare_metal(instance_ids).await
    |

error[E0599]: no method named `get_bare_metal_user_data` found for struct `Arc<VultrApiClient>` in the current scope
   --> src\vultr\client.rs:181:25
    |
181 |         self.api_client.get_bare_metal_user_data(baremetal_id).await
    |                         ^^^^^^^^^^^^^^^^^^^^^^^^
    |
help: there is a method `get_bare_metal` with a similar name
    |
181 -         self.api_client.get_bare_metal_user_data(baremetal_id).await
181 +         self.api_client.get_bare_metal(baremetal_id).await
    |

error[E0599]: no method named `get_bare_metal_upgrades` found for struct `Arc<VultrApiClient>` in the current scope
   --> src\vultr\client.rs:186:25
    |
186 |         self.api_client.get_bare_metal_upgrades(baremetal_id).await
    |                         ^^^^^^^^^^^^^^^^^^^^^^^
    |
help: there is a method `get_bare_metal` with a similar name
    |
186 -         self.api_client.get_bare_metal_upgrades(baremetal_id).await
186 +         self.api_client.get_bare_metal(baremetal_id).await
    |

error[E0599]: no method named `get_bare_metal_vnc` found for struct `Arc<VultrApiClient>` in the current scope
   --> src\vultr\client.rs:191:25
    |
191 |         self.api_client.get_bare_metal_vnc(baremetal_id).await
    |                         ^^^^^^^^^^^^^^^^^^
    |
help: there is a method `get_bare_metal` with a similar name
    |
191 -         self.api_client.get_bare_metal_vnc(baremetal_id).await
191 +         self.api_client.get_bare_metal(baremetal_id).await
    |

error[E0599]: no method named `attach_bare_metal_vpc` found for struct `Arc<VultrApiClient>` in the current scope
   --> src\vultr\client.rs:196:25
    |
196 |         self.api_client.attach_bare_metal_vpc(baremetal_id, vpc_id).await
    |                         ^^^^^^^^^^^^^^^^^^^^^
    |
help: there is a method `get_bare_metal` with a similar name, but with different arguments
   --> src\vultr\mod.rs:220:5
    |
220 |     pub async fn get_bare_metal(&self, bare_metal_id: &str) -> Result<VultrBareMetal> {
    |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

error[E0599]: no method named `detach_bare_metal_vpc` found for struct `Arc<VultrApiClient>` in the current scope
   --> src\vultr\client.rs:201:25
    |
201 |         self.api_client.detach_bare_metal_vpc(baremetal_id, vpc_id).await
    |                         ^^^^^^^^^^^^^^^^^^^^^
    |
help: there is a method `get_bare_metal` with a similar name, but with different arguments
   --> src\vultr\mod.rs:220:5
    |
220 |     pub async fn get_bare_metal(&self, bare_metal_id: &str) -> Result<VultrBareMetal> {
    |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

error[E0599]: no method named `list_bare_metal_vpcs` found for struct `Arc<VultrApiClient>` in the current scope
   --> src\vultr\client.rs:206:25
    |
206 |         self.api_client.list_bare_metal_vpcs(baremetal_id).await
    |                         ^^^^^^^^^^^^^^^^^^^^
    |
help: there is a method `list_bare_metal` with a similar name, but with different arguments
   --> src\vultr\mod.rs:211:5
    |
211 |     pub async fn list_bare_metal(&self) -> Result<Vec<VultrBareMetal>> {
    |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

