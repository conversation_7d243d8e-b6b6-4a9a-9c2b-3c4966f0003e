cargo :    Compiling achidas v0.1.0 (D:\workspace\.rust\achidas)
At line:1 char:1
+ cargo build 2>&1 | tee build_log.txt
+ ~~~~~~~~~~~~~~~~
    + CategoryInfo          : NotSpecified: (   Compiling ac...\.rust\achidas):String) [], RemoteException
    + FullyQualifiedErrorId : NativeCommandError
 
error: expected `;`, found `h`
    --> src\controllers\vultr.rs:3073:15
     |
3073 |         .await
     |               ^ help: add `;` here
3074 | h so w
     | - unexpected token

error: expected one of `!`, `.`, `::`, `;`, `?`, `{`, `}`, or an operator, found `so`
    --> src\controllers\vultr.rs:3074:3
     |
3074 | h so w
     |   ^^ expected one of 8 possible tokens

error[E0583]: file not found for module `cors`
 --> src\middleware\mod.rs:2:1
  |
2 | pub mod cors;
  | ^^^^^^^^^^^^^
  |
  = help: to create the module `cors`, create file "src\middleware\cors.rs" or "src\middleware\cors\mod.rs"
  = note: if there is a `mod cors` elsewhere in the crate already, import it with `use crate::...` instead

error[E0583]: file not found for module `logging`
 --> src\middleware\mod.rs:3:1
  |
3 | pub mod logging;
  | ^^^^^^^^^^^^^^^^
  |
  = help: to create the module `logging`, create file "src\middleware\logging.rs" or "src\middleware\logging\mod.rs"
  = note: if there is a `mod logging` elsewhere in the crate already, import it with `use crate::...` instead

error[E0583]: file not found for module `rate_limit`
 --> src\middleware\mod.rs:4:1
  |
4 | pub mod rate_limit;
  | ^^^^^^^^^^^^^^^^^^^
  |
  = help: to create the module `rate_limit`, create file "src\middleware\rate_limit.rs" or "src\middleware\rate_limit\mod.rs"
  = note: if there is a `mod rate_limit` elsewhere in the crate already, import it with `use crate::...` instead

error[E0428]: the name `list_os` is defined multiple times
    --> src\controllers\vultr.rs:2784:1
     |
165  | / pub async fn list_os(
166  | |     State(state): State<Arc<AppState>>,
167  | | ) -> ControllerResult<Json<crate::models::ApiResponse<Vec<VultrOS>>>> {
168  | |     let os_list = state
...    |
185  | |     Ok(success_response(converted_os))
186  | | }
     | |_- previous definition of the value `list_os` here
...
2784 | / pub async fn list_os(
2785 | |     State(state): State<Arc<AppState>>,
2786 | | ) -> ControllerResult<Json<crate::models::ApiResponse<Vec<VultrOS>>>> {
2787 | |     let os_list = state
...    |
2793 | |     Ok(success_response(os_list))
2794 | | }
     | |_^ `list_os` redefined here
     |
     = note: `list_os` must be defined only once in the value namespace of this module

error[E0428]: the name `list_plans` is defined multiple times
    --> src\controllers\vultr.rs:2798:1
     |
112  | / pub async fn list_plans(
113  | |     State(state): State<Arc<AppState>>,
114  | | ) -> ControllerResult<Json<crate::models::ApiResponse<Vec<VultrPlan>>>> {
115  | |     let plans = state
...    |
136  | |     Ok(success_response(converted_plans))
137  | | }
     | |_- previous definition of the value `list_plans` here
...
2798 | / pub async fn list_plans(
2799 | |     State(state): State<Arc<AppState>>,
2800 | | ) -> ControllerResult<Json<crate::models::ApiResponse<Vec<VultrPlan>>>> {
2801 | |     let plans = state
...    |
2807 | |     Ok(success_response(plans))
2808 | | }
     | |_^ `list_plans` redefined here
     |
     = note: `list_plans` must be defined only once in the value namespace of this module

error[E0428]: the name `list_regions` is defined multiple times
    --> src\controllers\vultr.rs:3095:1
     |
140  | / pub async fn list_regions(
141  | |     State(state): State<Arc<AppState>>,
142  | | ) -> ControllerResult<Json<crate::models::ApiResponse<Vec<VultrRegion>>>> {
143  | |     let regions = state
...    |
161  | |     Ok(success_response(converted_regions))
162  | | }
     | |_- previous definition of the value `list_regions` here
...
3095 | / pub async fn list_regions(
3096 | |     State(state): State<Arc<AppState>>,
3097 | | ) -> ControllerResult<Json<crate::models::ApiResponse<Vec<VultrRegion>>>> {
3098 | |     let regions = state
...    |
3104 | |     Ok(success_response(regions))
3105 | | }
     | |_^ `list_regions` redefined here
     |
     = note: `list_regions` must be defined only once in the value namespace of this module

error: no rules expected `,`
   --> src\observability\mod.rs:114:5
    |
114 | /     metrics::histogram!("http_request_duration_seconds", duration.as_secs_f64(),
115 | |         "method" => method.to_string(),
116 | |         "path" => path,
117 | |         "status" => status.to_string()
118 | |     );
    | |_____^ no rules expected this token in macro call
    |
note: while trying to match `=>`
   --> C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\metrics-0.22.4\src\macros.rs:48:36
    |
48  |     ($name:expr, $($label_key:expr => $label_value:expr),*) => {{
    |                                    ^^
    = note: this error originates in the macro `$crate::histogram` which comes from the expansion of the macro `metrics::histogram` (in 
Nightly builds, run with -Z macro-backtrace for more info)

error[E0428]: the name `VultrInstancesResponse` is defined multiple times
    --> src\vultr\models.rs:1126:1
     |
102  | pub struct VultrInstancesResponse {
     | --------------------------------- previous definition of the type `VultrInstancesResponse` here
...
1126 | pub struct VultrInstancesResponse {
     | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ `VultrInstancesResponse` redefined here
     |
     = note: `VultrInstancesResponse` must be defined only once in the type namespace of this module

error[E0428]: the name `VultrLoadBalancerGenericInfo` is defined multiple times
    --> src\vultr\models.rs:1558:1
     |
499  | pub struct VultrLoadBalancerGenericInfo {
     | --------------------------------------- previous definition of the type `VultrLoadBalancerGenericInfo` here
...
1558 | pub struct VultrLoadBalancerGenericInfo {
     | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ `VultrLoadBalancerGenericInfo` redefined here
     |
     = note: `VultrLoadBalancerGenericInfo` must be defined only once in the type namespace of this module

error[E0428]: the name `VultrHealthCheck` is defined multiple times
    --> src\vultr\models.rs:1571:1
     |
514  | pub struct VultrHealthCheck {
     | --------------------------- previous definition of the type `VultrHealthCheck` here
...
1571 | pub struct VultrHealthCheck {
     | ^^^^^^^^^^^^^^^^^^^^^^^^^^^ `VultrHealthCheck` redefined here
     |
     = note: `VultrHealthCheck` must be defined only once in the type namespace of this module

error[E0428]: the name `VultrManagedDatabase` is defined multiple times
    --> src\vultr\models.rs:1664:1
     |
542  | pub struct VultrManagedDatabase {
     | ------------------------------- previous definition of the type `VultrManagedDatabase` here
...
1664 | pub struct VultrManagedDatabase {
     | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ `VultrManagedDatabase` redefined here
     |
     = note: `VultrManagedDatabase` must be defined only once in the type namespace of this module

error[E0428]: the name `VultrObjectStorage` is defined multiple times
    --> src\vultr\models.rs:2153:1
     |
576  | pub struct VultrObjectStorage {
     | ----------------------------- previous definition of the type `VultrObjectStorage` here
...
2153 | pub struct VultrObjectStorage {
     | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ `VultrObjectStorage` redefined here
     |
     = note: `VultrObjectStorage` must be defined only once in the type namespace of this module

error[E0428]: the name `VultrOS` is defined multiple times
    --> src\vultr\models.rs:2213:1
     |
175  | pub struct VultrOS {
     | ------------------ previous definition of the type `VultrOS` here
...
2213 | pub struct VultrOS {
     | ^^^^^^^^^^^^^^^^^^ `VultrOS` redefined here
     |
     = note: `VultrOS` must be defined only once in the type namespace of this module

error[E0428]: the name `VultrOSResponse` is defined multiple times
    --> src\vultr\models.rs:2221:1
     |
183  | pub struct VultrOSResponse {
     | -------------------------- previous definition of the type `VultrOSResponse` here
...
2221 | pub struct VultrOSResponse {
     | ^^^^^^^^^^^^^^^^^^^^^^^^^^ `VultrOSResponse` redefined here
     |
     = note: `VultrOSResponse` must be defined only once in the type namespace of this module

error[E0428]: the name `VultrPlan` is defined multiple times
    --> src\vultr\models.rs:2228:1
     |
138  | pub struct VultrPlan {
     | -------------------- previous definition of the type `VultrPlan` here
...
2228 | pub struct VultrPlan {
     | ^^^^^^^^^^^^^^^^^^^^ `VultrPlan` redefined here
     |
     = note: `VultrPlan` must be defined only once in the type namespace of this module

error[E0428]: the name `VultrPlansResponse` is defined multiple times
    --> src\vultr\models.rs:2242:1
     |
152  | pub struct VultrPlansResponse {
     | ----------------------------- previous definition of the type `VultrPlansResponse` here
...
2242 | pub struct VultrPlansResponse {
     | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ `VultrPlansResponse` redefined here
     |
     = note: `VultrPlansResponse` must be defined only once in the type namespace of this module

error[E0428]: the name `VultrRegion` is defined multiple times
    --> src\vultr\models.rs:2374:1
     |
159  | pub struct VultrRegion {
     | ---------------------- previous definition of the type `VultrRegion` here
...
2374 | pub struct VultrRegion {
     | ^^^^^^^^^^^^^^^^^^^^^^ `VultrRegion` redefined here
     |
     = note: `VultrRegion` must be defined only once in the type namespace of this module

error[E0428]: the name `VultrRegionsResponse` is defined multiple times
    --> src\vultr\models.rs:2383:1
     |
168  | pub struct VultrRegionsResponse {
     | ------------------------------- previous definition of the type `VultrRegionsResponse` here
...
2383 | pub struct VultrRegionsResponse {
     | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ `VultrRegionsResponse` redefined here
     |
     = note: `VultrRegionsResponse` must be defined only once in the type namespace of this module

error[E0428]: the name `VultrSSHKey` is defined multiple times
    --> src\vultr\models.rs:2454:1
     |
190  | pub struct VultrSSHKey {
     | ---------------------- previous definition of the type `VultrSSHKey` here
...
2454 | pub struct VultrSSHKey {
     | ^^^^^^^^^^^^^^^^^^^^^^ `VultrSSHKey` redefined here
     |
     = note: `VultrSSHKey` must be defined only once in the type namespace of this module

error[E0428]: the name `VultrSSHKeysResponse` is defined multiple times
    --> src\vultr\models.rs:2462:1
     |
198  | pub struct VultrSSHKeysResponse {
     | ------------------------------- previous definition of the type `VultrSSHKeysResponse` here
...
2462 | pub struct VultrSSHKeysResponse {
     | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ `VultrSSHKeysResponse` redefined here
     |
     = note: `VultrSSHKeysResponse` must be defined only once in the type namespace of this module

error[E0428]: the name `VultrSnapshot` is defined multiple times
    --> src\vultr\models.rs:2665:1
     |
2395 | pub struct VultrSnapshot {
     | ------------------------ previous definition of the type `VultrSnapshot` here
...
2665 | pub struct VultrSnapshot {
     | ^^^^^^^^^^^^^^^^^^^^^^^^ `VultrSnapshot` redefined here
     |
     = note: `VultrSnapshot` must be defined only once in the type namespace of this module

error[E0428]: the name `VultrStartupScript` is defined multiple times
    --> src\vultr\models.rs:2689:1
     |
2481 | pub struct VultrStartupScript {
     | ----------------------------- previous definition of the type `VultrStartupScript` here
...
2689 | pub struct VultrStartupScript {
     | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ `VultrStartupScript` redefined here
     |
     = note: `VultrStartupScript` must be defined only once in the type namespace of this module

error[E0428]: the name `VultrISO` is defined multiple times
    --> src\vultr\models.rs:2701:1
     |
1336 | pub struct VultrISO {
     | ------------------- previous definition of the type `VultrISO` here
...
2701 | pub struct VultrISO {
     | ^^^^^^^^^^^^^^^^^^^ `VultrISO` redefined here
     |
     = note: `VultrISO` must be defined only once in the type namespace of this module

error[E0428]: the name `VultrStorageGateway` is defined multiple times
    --> src\vultr\models.rs:2758:1
     |
2513 | pub struct VultrStorageGateway {
     | ------------------------------ previous definition of the type `VultrStorageGateway` here
...
2758 | pub struct VultrStorageGateway {
     | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ `VultrStorageGateway` redefined here
     |
     = note: `VultrStorageGateway` must be defined only once in the type namespace of this module

error[E0428]: the name `VultrUser` is defined multiple times
    --> src\vultr\models.rs:2772:1
     |
2557 | pub struct VultrUser {
     | -------------------- previous definition of the type `VultrUser` here
...
2772 | pub struct VultrUser {
     | ^^^^^^^^^^^^^^^^^^^^ `VultrUser` redefined here
     |
     = note: `VultrUser` must be defined only once in the type namespace of this module

error[E0252]: the name `VultrOS` is defined multiple times
  --> src\controllers\vultr.rs:49:9
   |
6  |         UpdateBareMetalRequest, VultrOS, VultrPlan, VultrRegion, VultrBareMetal,
   |                                 ------- previous import of the type `VultrOS` here
...
49 |         VultrOS, VultrPlan, VultrMetalPlan, VultrInference, CreateInferenceRequest,
   |         ^^^^^^^ `VultrOS` reimported here
   |
   = note: `VultrOS` must be defined only once in the type namespace of this module
help: you can use `as` to change the binding name of the import
   |
49 |         VultrOS as OtherVultrOS, VultrPlan, VultrMetalPlan, VultrInference, CreateInferenceRequest,
   |                 +++++++++++++++

error[E0252]: the name `VultrPlan` is defined multiple times
  --> src\controllers\vultr.rs:49:18
   |
6  |         UpdateBareMetalRequest, VultrOS, VultrPlan, VultrRegion, VultrBareMetal,
   |                                          --------- previous import of the type `VultrPlan` here
...
49 |         VultrOS, VultrPlan, VultrMetalPlan, VultrInference, CreateInferenceRequest,
   |                  ^^^^^^^^^ `VultrPlan` reimported here
   |
   = note: `VultrPlan` must be defined only once in the type namespace of this module
help: you can use `as` to change the binding name of the import
   |
49 |         VultrOS, VultrPlan as OtherVultrPlan, VultrMetalPlan, VultrInference, CreateInferenceRequest,
   |                            +++++++++++++++++

error[E0252]: the name `VultrRegion` is defined multiple times
  --> src\controllers\vultr.rs:53:9
   |
6  |         UpdateBareMetalRequest, VultrOS, VultrPlan, VultrRegion, VultrBareMetal,
   |                                                     ----------- previous import of the type `VultrRegion` here
...
53 |         VultrRegion, VultrRegionAvailablePlans, VultrSnapshot, CreateSnapshotRequest,
   |         ^^^^^^^^^^^ `VultrRegion` reimported here
   |
   = note: `VultrRegion` must be defined only once in the type namespace of this module
help: you can use `as` to change the binding name of the import
   |
53 |         VultrRegion as OtherVultrRegion, VultrRegionAvailablePlans, VultrSnapshot, CreateSnapshotRequest,
   |                     +++++++++++++++++++

error[E0252]: the name `VultrSSHKey` is defined multiple times
  --> src\controllers\vultr.rs:55:34
   |
9  |         VultrSSHKey, VultrAccount, VultrAccountBGP, VultrAccountBandwidth,
   |         ----------- previous import of the type `VultrSSHKey` here
...
55 |         CreateSubaccountRequest, VultrSSHKey, CreateSSHKeyRequest, UpdateSSHKeyRequest,
   |                                  ^^^^^^^^^^^--
   |                                  |
   |                                  `VultrSSHKey` reimported here
   |                                  help: remove unnecessary import
   |
   = note: `VultrSSHKey` must be defined only once in the type namespace of this module

error[E0433]: failed to resolve: could not find `sdk` in `opentelemetry`
 --> src\observability\mod.rs:5:5
  |
5 |     sdk::{
  |     ^^^ could not find `sdk` in `opentelemetry`

error[E0432]: unresolved import `opentelemetry::sdk`
 --> src\observability\mod.rs:5:5
  |
5 |     sdk::{
  |     ^^^ could not find `sdk` in `opentelemetry`

error[E0432]: unresolved import `crate::services::admin`
 --> src\controllers\admin.rs:4:16
  |
4 |     services::{admin::AdminService, billing::BillingService},
  |                ^^^^^ could not find `admin` in `services`

error[E0433]: failed to resolve: could not find `register_counter` in `metrics`
  --> src\observability\mod.rs:81:14
   |
81 |     metrics::register_counter!("http_requests_total", "Total number of HTTP requests");
   |              ^^^^^^^^^^^^^^^^ could not find `register_counter` in `metrics`

error[E0433]: failed to resolve: could not find `register_histogram` in `metrics`
  --> src\observability\mod.rs:82:14
   |
82 |     metrics::register_histogram!("http_request_duration_seconds", "HTTP request duration in seconds");
   |              ^^^^^^^^^^^^^^^^^^ could not find `register_histogram` in `metrics`

error[E0433]: failed to resolve: could not find `register_gauge` in `metrics`
  --> src\observability\mod.rs:83:14
   |
83 |     metrics::register_gauge!("active_connections", "Number of active connections");
   |              ^^^^^^^^^^^^^^ could not find `register_gauge` in `metrics`

error[E0433]: failed to resolve: could not find `register_counter` in `metrics`
  --> src\observability\mod.rs:84:14
   |
84 |     metrics::register_counter!("vultr_api_calls_total", "Total number of Vultr API calls");
   |              ^^^^^^^^^^^^^^^^ could not find `register_counter` in `metrics`

error[E0433]: failed to resolve: could not find `register_counter` in `metrics`
  --> src\observability\mod.rs:85:14
   |
85 |     metrics::register_counter!("vultr_api_errors_total", "Total number of Vultr API errors");
   |              ^^^^^^^^^^^^^^^^ could not find `register_counter` in `metrics`

error[E0433]: failed to resolve: could not find `register_gauge` in `metrics`
  --> src\observability\mod.rs:86:14
   |
86 |     metrics::register_gauge!("active_instances", "Number of active instances");
   |              ^^^^^^^^^^^^^^ could not find `register_gauge` in `metrics`

error[E0433]: failed to resolve: could not find `register_counter` in `metrics`
  --> src\observability\mod.rs:87:14
   |
87 |     metrics::register_counter!("user_registrations_total", "Total number of user registrations");
   |              ^^^^^^^^^^^^^^^^ could not find `register_counter` in `metrics`

error[E0433]: failed to resolve: could not find `register_counter` in `metrics`
  --> src\observability\mod.rs:88:14
   |
88 |     metrics::register_counter!("user_logins_total", "Total number of user logins");
   |              ^^^^^^^^^^^^^^^^ could not find `register_counter` in `metrics`

error[E0433]: failed to resolve: could not find `increment_counter` in `metrics`
   --> src\observability\mod.rs:108:14
    |
108 |     metrics::increment_counter!("http_requests_total", 
    |              ^^^^^^^^^^^^^^^^^ could not find `increment_counter` in `metrics`

error[E0412]: cannot find type `HashMap` in this scope
    --> src\controllers\vultr.rs:1636:55
     |
1636 | ) -> ControllerResult<Json<crate::models::ApiResponse<HashMap<String, InstanceBandwidth>>>> {
     |                                                       ^^^^^^^ not found in this scope
     |
help: consider importing this struct
     |
1    + use std::collections::HashMap;
     |

error[E0425]: cannot find function `patch` in this scope
   --> src\routes\mod.rs:142:50
    |
142 |         .route("/vultr/dns/domains/:domain/soa", patch(controllers::vultr::update_dns_domain_soa))
    |                                                  ^^^^^ not found in this scope
    |
help: consider importing this function
    |
1   + use axum::routing::patch;
    |

error[E0425]: cannot find function `patch` in this scope
   --> src\routes\mod.rs:147:65
    |
147 |         .route("/vultr/dns/domains/:domain/records/:record_id", patch(controllers::vultr::update_dns_domain_record))
    |                                                                 ^^^^^ not found in this scope
    |
help: consider importing this function
    |
1   + use axum::routing::patch;
    |

error[E0425]: cannot find function `patch` in this scope
   --> src\routes\mod.rs:165:49
    |
165 |         .route("/vultr/instances/:instance_id", patch(controllers::vultr::update_instance_detailed))
    |                                                 ^^^^^ not found in this scope
    |
help: consider importing this function
    |
1   + use axum::routing::patch;
    |

error[E0425]: cannot find function `patch` in this scope
   --> src\routes\mod.rs:218:78
    |
218 |         .route("/vultr/kubernetes/clusters/:vke_id/node-pools/:nodepool_id", patch(controllers::vultr::update_nodepool))
    |                                                                              ^^^^^ not found in this scope
    |
help: consider importing this function
    |
1   + use axum::routing::patch;
    |

error[E0425]: cannot find function `patch` in this scope
   --> src\routes\mod.rs:229:59
    |
229 |         .route("/vultr/load-balancers/:load_balancer_id", patch(controllers::vultr::update_load_balancer))
    |                                                           ^^^^^ not found in this scope
    |
help: consider importing this function
    |
1   + use axum::routing::patch;
    |

error[E0425]: cannot find function `patch` in this scope
   --> src\routes\mod.rs:279:50
    |
279 |         .route("/vultr/inference/:inference_id", patch(controllers::vultr::update_inference))
    |                                                  ^^^^^ not found in this scope
    |
help: consider importing this function
    |
1   + use axum::routing::patch;
    |

error[E0425]: cannot find function `patch` in this scope
   --> src\routes\mod.rs:294:52
    |
294 |         .route("/vultr/reserved-ips/:reserved_ip", patch(controllers::vultr::update_reserved_ip))
    |                                                    ^^^^^ not found in this scope
    |
help: consider importing this function
    |
1   + use axum::routing::patch;
    |

error[E0425]: cannot find value `create_ssh_key` in module `controllers::vultr`
   --> src\routes\mod.rs:318:60
    |
318 |         .route("/vultr/ssh-keys", post(controllers::vultr::create_ssh_key))
    |                                                            ^^^^^^^^^^^^^^ not found in `controllers::vultr`

error[E0425]: cannot find value `get_ssh_key` in module `controllers::vultr`
   --> src\routes\mod.rs:319:71
    |
319 |         .route("/vultr/ssh-keys/:ssh_key_id", get(controllers::vultr::get_ssh_key))
    |                                                                       ^^^^^^^^^^^ not found in `controllers::vultr`

error[E0425]: cannot find function `patch` in this scope
   --> src\routes\mod.rs:320:47
    |
320 |         .route("/vultr/ssh-keys/:ssh_key_id", patch(controllers::vultr::update_ssh_key))
    |                                               ^^^^^ not found in this scope
    |
help: consider importing this function
    |
1   + use axum::routing::patch;
    |

error[E0425]: cannot find value `update_ssh_key` in module `controllers::vultr`
   --> src\routes\mod.rs:320:73
    |
320 |         .route("/vultr/ssh-keys/:ssh_key_id", patch(controllers::vultr::update_ssh_key))
    |                                                                         ^^^^^^^^^^^^^^ not found in `controllers::vultr`

error[E0425]: cannot find value `delete_ssh_key` in module `controllers::vultr`
   --> src\routes\mod.rs:321:74
    |
321 |         .route("/vultr/ssh-keys/:ssh_key_id", delete(controllers::vultr::delete_ssh_key))
    |                                                                          ^^^^^^^^^^^^^^ not found in `controllers::vultr`

error[E0425]: cannot find value `list_startup_scripts` in module `controllers::vultr`
   --> src\routes\mod.rs:324:66
    |
324 |         .route("/vultr/startup-scripts", get(controllers::vultr::list_startup_scripts))
    |                                                                  ^^^^^^^^^^^^^^^^^^^^ not found in `controllers::vultr`

error[E0425]: cannot find value `create_startup_script` in module `controllers::vultr`
   --> src\routes\mod.rs:325:67
    |
325 |         .route("/vultr/startup-scripts", post(controllers::vultr::create_startup_script))
    |                                                                   ^^^^^^^^^^^^^^^^^^^^^ not found in `controllers::vultr`

error[E0425]: cannot find value `get_startup_script` in module `controllers::vultr`
   --> src\routes\mod.rs:326:77
    |
326 |         .route("/vultr/startup-scripts/:script_id", get(controllers::vultr::get_startup_script))
    |                                                                             ^^^^^^^^^^^^^^^^^^ not found in `controllers::vultr`

error[E0425]: cannot find function `patch` in this scope
   --> src\routes\mod.rs:327:53
    |
327 |         .route("/vultr/startup-scripts/:script_id", patch(controllers::vultr::update_startup_script))
    |                                                     ^^^^^ not found in this scope
    |
help: consider importing this function
    |
1   + use axum::routing::patch;
    |

error[E0425]: cannot find value `update_startup_script` in module `controllers::vultr`
   --> src\routes\mod.rs:327:79
    |
327 |         .route("/vultr/startup-scripts/:script_id", patch(controllers::vultr::update_startup_script))
    |                                                                               ^^^^^^^^^^^^^^^^^^^^^ not found in `controllers::vultr`

error[E0425]: cannot find value `delete_startup_script` in module `controllers::vultr`
   --> src\routes\mod.rs:328:80
    |
328 |         .route("/vultr/startup-scripts/:script_id", delete(controllers::vultr::delete_startup_script))
    |                                                                                ^^^^^^^^^^^^^^^^^^^^^ not found in `controllers::vultr`

error[E0425]: cannot find value `list_storage_gateways` in module `controllers::vultr`
   --> src\routes\mod.rs:331:67
    |
331 |         .route("/vultr/storage-gateways", get(controllers::vultr::list_storage_gateways))
    |                                                                   ^^^^^^^^^^^^^^^^^^^^^ not found in `controllers::vultr`

error[E0425]: cannot find value `create_storage_gateway` in module `controllers::vultr`
   --> src\routes\mod.rs:332:68
    |
332 |         .route("/vultr/storage-gateways", post(controllers::vultr::create_storage_gateway))
    |                                                                    ^^^^^^^^^^^^^^^^^^^^^^ not found in `controllers::vultr`

error[E0425]: cannot find value `get_storage_gateway` in module `controllers::vultr`
   --> src\routes\mod.rs:333:79
    |
333 |         .route("/vultr/storage-gateways/:gateway_id", get(controllers::vultr::get_storage_gateway))
    |                                                                               ^^^^^^^^^^^^^^^^^^^ not found in `controllers::vultr`

error[E0425]: cannot find value `update_storage_gateway` in module `controllers::vultr`
   --> src\routes\mod.rs:334:79
    |
334 |         .route("/vultr/storage-gateways/:gateway_id", put(controllers::vultr::update_storage_gateway))
    |                                                                               ^^^^^^^^^^^^^^^^^^^^^^ not found in `controllers::vultr`

error[E0425]: cannot find value `delete_storage_gateway` in module `controllers::vultr`
   --> src\routes\mod.rs:335:82
    |
335 |         .route("/vultr/storage-gateways/:gateway_id", delete(controllers::vultr::delete_storage_gateway))
    |                                                                                  ^^^^^^^^^^^^^^^^^^^^^^ not found in `controllers::vultr`

error[E0425]: cannot find value `add_storage_gateway_export` in module `controllers::vultr`
   --> src\routes\mod.rs:336:88
    |
336 |         .route("/vultr/storage-gateways/:gateway_id/exports", post(controllers::vultr::add_storage_gateway_export))
    |                                                                                        ^^^^^^^^^^^^^^^^^^^^^^^^^^ not found in 
`controllers::vultr`

error[E0425]: cannot find value `delete_storage_gateway_export` in module `controllers::vultr`
   --> src\routes\mod.rs:337:101
    |
337 |         .route("/vultr/storage-gateways/:gateway_id/exports/:export_id", delete(controllers::vultr::delete_storage_gateway_export))
    |                                                                                                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ not 
found in `controllers::vultr`

error[E0425]: cannot find value `list_users` in module `controllers::vultr`
    --> src\routes\mod.rs:340:56
     |
340  |           .route("/vultr/users", get(controllers::vultr::list_users))
     |                                                          ^^^^^^^^^^
     |
    ::: src\controllers\vultr.rs:1964:1
     |
1964 | / pub async fn list_isos(
1965 | |     State(state): State<Arc<AppState>>,
1966 | | ) -> ControllerResult<Json<crate::models::ApiResponse<Vec<VultrISO>>>> {
1967 | |     let isos = state
...    |
1973 | |     Ok(success_response(isos))
1974 | | }
     | |_- similarly named function `list_isos` defined here
     |
help: a function with a similar name exists
     |
340  -         .route("/vultr/users", get(controllers::vultr::list_users))
340  +         .route("/vultr/users", get(controllers::vultr::list_isos))
     |
help: consider importing this function
     |
1    + use crate::controllers::admin::list_users;
     |
help: if you import `list_users`, refer to it directly
     |
340  -         .route("/vultr/users", get(controllers::vultr::list_users))
340  +         .route("/vultr/users", get(list_users))
     |

error[E0425]: cannot find value `create_user` in module `controllers::vultr`
    --> src\routes\mod.rs:341:57
     |
341  |           .route("/vultr/users", post(controllers::vultr::create_user))
     |                                                           ^^^^^^^^^^^ help: a function with a similar name exists: `create_iso`
     |
    ::: src\controllers\vultr.rs:1977:1
     |
1977 | / pub async fn create_iso(
1978 | |     State(state): State<Arc<AppState>>,
1979 | |     Json(request): Json<CreateISORequest>,
1980 | | ) -> ControllerResult<Json<crate::models::ApiResponse<VultrISO>>> {
...    |
1987 | |     Ok(success_response(iso))
1988 | | }
     | |_- similarly named function `create_iso` defined here

error[E0425]: cannot find value `get_user` in module `controllers::vultr`
   --> src\routes\mod.rs:342:65
    |
342 |         .route("/vultr/users/:user_id", get(controllers::vultr::get_user))
    |                                                                 ^^^^^^^^ not found in `controllers::vultr`
    |
help: consider importing this function
    |
1   + use crate::controllers::admin::get_user;
    |
help: if you import `get_user`, refer to it directly
    |
342 -         .route("/vultr/users/:user_id", get(controllers::vultr::get_user))
342 +         .route("/vultr/users/:user_id", get(get_user))
    |

error[E0425]: cannot find function `patch` in this scope
   --> src\routes\mod.rs:343:41
    |
343 |         .route("/vultr/users/:user_id", patch(controllers::vultr::update_user))
    |                                         ^^^^^ not found in this scope
    |
help: consider importing this function
    |
1   + use axum::routing::patch;
    |

error[E0425]: cannot find value `update_user` in module `controllers::vultr`
   --> src\routes\mod.rs:343:67
    |
343 |         .route("/vultr/users/:user_id", patch(controllers::vultr::update_user))
    |                                                                   ^^^^^^^^^^^ not found in `controllers::vultr`

error[E0425]: cannot find value `delete_user` in module `controllers::vultr`
    --> src\routes\mod.rs:344:68
     |
344  |           .route("/vultr/users/:user_id", delete(controllers::vultr::delete_user))
     |                                                                      ^^^^^^^^^^^ help: a function with a similar name exists: 
`delete_iso`
     |
    ::: src\controllers\vultr.rs:2005:1
     |
2005 | / pub async fn delete_iso(
2006 | |     State(state): State<Arc<AppState>>,
2007 | |     Path(iso_id): Path<String>,
2008 | | ) -> ControllerResult<Json<crate::models::ApiResponse<()>>> {
...    |
2015 | |     Ok(success_response(()))
2016 | | }
     | |_- similarly named function `delete_iso` defined here

error[E0425]: cannot find value `list_vfs_regions` in module `controllers::vultr`
   --> src\routes\mod.rs:347:62
    |
347 |           .route("/vultr/vfs/regions", get(controllers::vultr::list_vfs_regions))
    |                                                                ^^^^^^^^^^^^^^^^ help: a function with a similar name exists: 
`list_regions`
    |
   ::: src\controllers\vultr.rs:140:1
    |
140 | / pub async fn list_regions(
141 | |     State(state): State<Arc<AppState>>,
142 | | ) -> ControllerResult<Json<crate::models::ApiResponse<Vec<VultrRegion>>>> {
143 | |     let regions = state
...   |
161 | |     Ok(success_response(converted_regions))
162 | | }
    | |_- similarly named function `list_regions` defined here

error[E0425]: cannot find value `list_vfs` in module `controllers::vultr`
   --> src\routes\mod.rs:348:54
    |
348 |           .route("/vultr/vfs", get(controllers::vultr::list_vfs))
    |                                                        ^^^^^^^^ help: a function with a similar name exists: `list_os`
    |
   ::: src\controllers\vultr.rs:165:1
    |
165 | / pub async fn list_os(
166 | |     State(state): State<Arc<AppState>>,
167 | | ) -> ControllerResult<Json<crate::models::ApiResponse<Vec<VultrOS>>>> {
168 | |     let os_list = state
...   |
185 | |     Ok(success_response(converted_os))
186 | | }
    | |_- similarly named function `list_os` defined here

error[E0425]: cannot find value `create_vfs` in module `controllers::vultr`
    --> src\routes\mod.rs:349:55
     |
349  |           .route("/vultr/vfs", post(controllers::vultr::create_vfs))
     |                                                         ^^^^^^^^^^ help: a function with a similar name exists: `create_vpc`
     |
    ::: src\controllers\vultr.rs:2923:1
     |
2923 | / pub async fn create_vpc(
2924 | |     State(state): State<Arc<AppState>>,
2925 | |     Json(request): Json<CreateVPCRequest>,
2926 | | ) -> ControllerResult<Json<crate::models::ApiResponse<VultrVPCDetailed>>> {
...    |
2933 | |     Ok(success_response(vpc))
2934 | | }
     | |_- similarly named function `create_vpc` defined here

error[E0425]: cannot find value `get_vfs` in module `controllers::vultr`
    --> src\routes\mod.rs:350:62
     |
350  |           .route("/vultr/vfs/:vfs_id", get(controllers::vultr::get_vfs))
     |                                                                ^^^^^^^ help: a function with a similar name exists: `get_vpc`
     |
    ::: src\controllers\vultr.rs:2937:1
     |
2937 | / pub async fn get_vpc(
2938 | |     State(state): State<Arc<AppState>>,
2939 | |     Path(vpc_id): Path<String>,
2940 | | ) -> ControllerResult<Json<crate::models::ApiResponse<VultrVPCDetailed>>> {
...    |
2947 | |     Ok(success_response(vpc))
2948 | | }
     | |_- similarly named function `get_vpc` defined here

error[E0425]: cannot find value `update_vfs` in module `controllers::vultr`
    --> src\routes\mod.rs:351:62
     |
351  |           .route("/vultr/vfs/:vfs_id", put(controllers::vultr::update_vfs))
     |                                                                ^^^^^^^^^^ help: a function with a similar name exists: `update_vpc`
     |
    ::: src\controllers\vultr.rs:2951:1
     |
2951 | / pub async fn update_vpc(
2952 | |     State(state): State<Arc<AppState>>,
2953 | |     Path(vpc_id): Path<String>,
2954 | |     Json(request): Json<UpdateVPCRequest>,
...    |
2962 | |     Ok(success_response(()))
2963 | | }
     | |_- similarly named function `update_vpc` defined here

error[E0425]: cannot find value `delete_vfs` in module `controllers::vultr`
    --> src\routes\mod.rs:352:65
     |
352  |           .route("/vultr/vfs/:vfs_id", delete(controllers::vultr::delete_vfs))
     |                                                                   ^^^^^^^^^^ help: a function with a similar name exists: `delete_vpc`
     |
    ::: src\controllers\vultr.rs:2966:1
     |
2966 | / pub async fn delete_vpc(
2967 | |     State(state): State<Arc<AppState>>,
2968 | |     Path(vpc_id): Path<String>,
2969 | | ) -> ControllerResult<Json<crate::models::ApiResponse<()>>> {
...    |
2976 | |     Ok(success_response(()))
2977 | | }
     | |_- similarly named function `delete_vpc` defined here

error[E0425]: cannot find value `list_vfs_attachments` in module `controllers::vultr`
   --> src\routes\mod.rs:353:74
    |
353 |         .route("/vultr/vfs/:vfs_id/attachments", get(controllers::vultr::list_vfs_attachments))
    |                                                                          ^^^^^^^^^^^^^^^^^^^^ not found in `controllers::vultr`

error[E0425]: cannot find value `create_vfs_attachment` in module `controllers::vultr`
   --> src\routes\mod.rs:354:75
    |
354 |         .route("/vultr/vfs/:vfs_id/attachments", post(controllers::vultr::create_vfs_attachment))
    |                                                                           ^^^^^^^^^^^^^^^^^^^^^ not found in `controllers::vultr`

error[E0425]: cannot find value `get_vfs_attachment` in module `controllers::vultr`
   --> src\routes\mod.rs:355:89
    |
355 |         .route("/vultr/vfs/:vfs_id/attachments/:attachment_id", get(controllers::vultr::get_vfs_attachment))
    |                                                                                         ^^^^^^^^^^^^^^^^^^ not found in 
`controllers::vultr`

error[E0425]: cannot find value `delete_vfs_attachment` in module `controllers::vultr`
   --> src\routes\mod.rs:356:92
    |
356 |         .route("/vultr/vfs/:vfs_id/attachments/:attachment_id", delete(controllers::vultr::delete_vfs_attachment))
    |                                                                                            ^^^^^^^^^^^^^^^^^^^^^ not found in 
`controllers::vultr`

error[E0433]: failed to resolve: could not find `ErrorKind` in `reqwest`
  --> src\utils\retry.rs:80:51
   |
80 |                     reqwest::Error::from(reqwest::ErrorKind::Request)
   |                                                   ^^^^^^^^^ could not find `ErrorKind` in `reqwest`
   |
help: consider importing one of these enums
   |
1  + use std::io::ErrorKind;
   |
1  + use axum::extract::path::ErrorKind;
   |
1  + use bson::raw::ErrorKind;
   |
1  + use futures::io::ErrorKind;
   |
     and 3 other candidates
help: if you import `ErrorKind`, refer to it directly
   |
80 -                     reqwest::Error::from(reqwest::ErrorKind::Request)
80 +                     reqwest::Error::from(ErrorKind::Request)
   |

error[E0433]: failed to resolve: could not find `ErrorKind` in `reqwest`
  --> src\utils\retry.rs:88:59
   |
88 | ...                   reqwest::Error::from(reqwest::ErrorKind::Request)
   |                                                     ^^^^^^^^^ could not find `ErrorKind` in `reqwest`
   |
help: consider importing one of these enums
   |
1  + use std::io::ErrorKind;
   |
1  + use axum::extract::path::ErrorKind;
   |
1  + use bson::raw::ErrorKind;
   |
1  + use futures::io::ErrorKind;
   |
     and 3 other candidates
help: if you import `ErrorKind`, refer to it directly
   |
88 -                             reqwest::Error::from(reqwest::ErrorKind::Request)
88 +                             reqwest::Error::from(ErrorKind::Request)
   |

error[E0412]: cannot find type `HashMap` in this scope
   --> src\vultr\client.rs:717:77
    |
717 |     pub async fn get_instance_bandwidth(&self, instance_id: &str) -> Result<HashMap<String, InstanceBandwidth>> {
    |                                                                             ^^^^^^^ not found in this scope
    |
help: consider importing one of these structs
    |
1   + use crate::vultr::HashMap;
    |
1   + use std::collections::HashMap;
    |

warning: unused import: `billing::BillingService`
 --> src\controllers\admin.rs:4:37
  |
4 |     services::{admin::AdminService, billing::BillingService},
  |                                     ^^^^^^^^^^^^^^^^^^^^^^^
  |
  = note: `#[warn(unused_imports)]` on by default

warning: unused imports: `AddDatabaseReadReplicaRequest`, `AddStorageGatewayExportRequest`, `CreateDatabaseConnectionPoolRequest`, 
`CreateDatabaseConnectorRequest`, `CreateDatabaseDBRequest`, `CreateDatabaseQuotaRequest`, `CreateDatabaseTopicRequest`, 
`CreateSSHKeyRequest`, `CreateStartupScriptRequest`, `CreateStorageGatewayRequest`, `CreateUserRequest`, `CreateVFSAttachmentRequest`, 
`CreateVFSRequest`, `ForkDatabaseRequest`, `RestoreDatabaseFromBackupRequest`, `StartDatabaseMigrationRequest`, 
`StartDatabaseVersionUpgradeRequest`, `UpdateDatabaseConnectionPoolRequest`, `UpdateDatabaseConnectorRequest`, `UpdateDatabaseTopicRequest`, 
`UpdateSSHKeyRequest`, `UpdateStartupScriptRequest`, `UpdateStorageGatewayRequest`, `UpdateUserRequest`, `UpdateVFSRequest`, 
`VultrDatabaseAdvancedOptions`, `VultrDatabaseBackupInfo`, `VultrDatabaseConnectionPool`, `VultrDatabaseConnectorStatus`, 
`VultrDatabaseConnector`, `VultrDatabaseDB`, `VultrDatabaseMaintenanceUpdate`, `VultrDatabaseMigrationStatus`, `VultrDatabaseQuota`, 
`VultrDatabaseReadReplica`, `VultrDatabaseServiceAlert`, `VultrDatabaseTopic`, `VultrDatabaseVersions`, `VultrOS`, `VultrPlan`, `VultrRegion`, 
`VultrSSHKey`, `VultrStartupScript`, `VultrStorageGatewayExport`, `VultrStorageGateway`, `VultrUser`, `VultrVFSAttachment`, and `VultrVFS`
  --> src\controllers\vultr.rs:37:36
   |
37 |         SetDatabaseUserACLRequest, VultrDatabaseDB, CreateDatabaseDBRequest,
   |                                    ^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^^
38 |         VultrDatabaseTopic, CreateDatabaseTopicRequest, UpdateDatabaseTopicRequest,
   |         ^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^^^^^
39 |         VultrDatabaseQuota, CreateDatabaseQuotaRequest, VultrDatabaseConnector,
   |         ^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^
40 |         CreateDatabaseConnectorRequest, UpdateDatabaseConnectorRequest,
   |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
41 |         VultrDatabaseConnectorStatus, VultrDatabaseMaintenanceUpdate,
   |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
42 |         VultrDatabaseServiceAlert, VultrDatabaseMigrationStatus, StartDatabaseMigrationRequest,
   |         ^^^^^^^^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
43 |         AddDatabaseReadReplicaRequest, VultrDatabaseReadReplica, VultrDatabaseBackupInfo,
   |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^^
44 |         RestoreDatabaseFromBackupRequest, ForkDatabaseRequest, VultrDatabaseConnectionPool,
   |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^^^^^^
45 |         CreateDatabaseConnectionPoolRequest, UpdateDatabaseConnectionPoolRequest,
   |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
46 |         VultrDatabaseAdvancedOptions, VultrDatabaseVersions, StartDatabaseVersionUpgradeRequest,
   |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
...
49 |         VultrOS, VultrPlan, VultrMetalPlan, VultrInference, CreateInferenceRequest,
   |         ^^^^^^^  ^^^^^^^^^
...
53 |         VultrRegion, VultrRegionAvailablePlans, VultrSnapshot, CreateSnapshotRequest,
   |         ^^^^^^^^^^^
54 |         CreateSnapshotFromUrlRequest, UpdateSnapshotRequest, VultrSubaccount,
55 |         CreateSubaccountRequest, VultrSSHKey, CreateSSHKeyRequest, UpdateSSHKeyRequest,
   |                                  ^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^
56 |         VultrStartupScript, CreateStartupScriptRequest, UpdateStartupScriptRequest,
   |         ^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^^^^^
57 |         VultrStorageGateway, CreateStorageGatewayRequest, UpdateStorageGatewayRequest,
   |         ^^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^^^^^^
58 |         VultrStorageGatewayExport, AddStorageGatewayExportRequest, VultrUser,
   |         ^^^^^^^^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^  ^^^^^^^^^
59 |         CreateUserRequest, UpdateUserRequest, VultrVFS, CreateVFSRequest,
   |         ^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^  ^^^^^^^^  ^^^^^^^^^^^^^^^^
60 |         UpdateVFSRequest, VultrVFSAttachment, CreateVFSAttachmentRequest,
   |         ^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `serde_json::json`
  --> src\controllers\mod.rs:15:5
   |
15 | use serde_json::json;
   |     ^^^^^^^^^^^^^^^^

warning: unused import: `error`
  --> src\middleware\auth.rs:13:15
   |
13 | use tracing::{error, warn};
   |               ^^^^^

warning: unused import: `StatusCode`
 --> src\middleware\mod.rs:7:21
  |
7 |     http::{Request, StatusCode},
  |                     ^^^^^^^^^^

warning: unused import: `bson::oid::ObjectId`
 --> src\models\mod.rs:1:5
  |
1 | use bson::oid::ObjectId;
  |     ^^^^^^^^^^^^^^^^^^^

warning: unused imports: `DateTime` and `Utc`
 --> src\models\mod.rs:2:14
  |
2 | use chrono::{DateTime, Utc};
  |              ^^^^^^^^  ^^^

warning: unused import: `uuid::Uuid`
 --> src\models\mod.rs:4:5
  |
4 | use uuid::Uuid;
  |     ^^^^^^^^^^

warning: unused import: `global`
 --> src\observability\mod.rs:4:5
  |
4 |     global,
  |     ^^^^^^

warning: unused import: `Level`
  --> src\observability\mod.rs:12:21
   |
12 | use tracing::{info, Level};
   |                     ^^^^^

warning: unused import: `error`
  --> src\services\auth.rs:15:15
   |
15 | use tracing::{error, info, instrument};
   |               ^^^^^

warning: unused import: `database::Database`
 --> src\services\billing.rs:2:5
  |
2 |     database::Database,
  |     ^^^^^^^^^^^^^^^^^^

warning: unused import: `error`
  --> src\services\instance.rs:14:15
   |
14 | use tracing::{error, info, instrument};
   |               ^^^^^

warning: unused import: `DateTime`
 --> src\utils\mod.rs:3:14
  |
3 | use chrono::{DateTime, Utc};
  |              ^^^^^^^^

warning: unused import: `Response`
 --> src\vultr\mod.rs:3:42
  |
3 | use reqwest::{header::HeaderMap, Client, Response};
  |                                          ^^^^^^^^

warning: unused imports: `Deserialize` and `Serialize`
 --> src\vultr\mod.rs:4:13
  |
4 | use serde::{Deserialize, Serialize};
  |             ^^^^^^^^^^^  ^^^^^^^^^

warning: unused import: `futures::TryStreamExt`
 --> src\vultr\mod.rs:7:5
  |
7 | use futures::TryStreamExt;
  |     ^^^^^^^^^^^^^^^^^^^^^

warning: unused imports: `DateTime` and `Utc`
 --> src\vultr\models.rs:1:14
  |
1 | use chrono::{DateTime, Utc};
  |              ^^^^^^^^  ^^^

error[E0119]: conflicting implementations of trait `Clone` for type `vultr::models::VultrInstancesResponse`
    --> src\vultr\models.rs:1125:17
     |
101  | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                 ----- first implementation here
...
1125 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                 ^^^^^ conflicting implementation for `vultr::models::VultrInstancesResponse`
     |
     = note: this error originates in the derive macro `Clone` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0119]: conflicting implementations of trait `Clone` for type `vultr::models::VultrLoadBalancerGenericInfo`
    --> src\vultr\models.rs:1557:17
     |
498  | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                 ----- first implementation here
...
1557 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                 ^^^^^ conflicting implementation for `vultr::models::VultrLoadBalancerGenericInfo`
     |
     = note: this error originates in the derive macro `Clone` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0119]: conflicting implementations of trait `Clone` for type `vultr::models::VultrHealthCheck`
    --> src\vultr\models.rs:1570:17
     |
513  | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                 ----- first implementation here
...
1570 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                 ^^^^^ conflicting implementation for `vultr::models::VultrHealthCheck`
     |
     = note: this error originates in the derive macro `Clone` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0119]: conflicting implementations of trait `Clone` for type `vultr::models::VultrManagedDatabase`
    --> src\vultr\models.rs:1663:17
     |
541  | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                 ----- first implementation here
...
1663 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                 ^^^^^ conflicting implementation for `vultr::models::VultrManagedDatabase`
     |
     = note: this error originates in the derive macro `Clone` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0119]: conflicting implementations of trait `Clone` for type `vultr::models::VultrObjectStorage`
    --> src\vultr\models.rs:2152:17
     |
575  | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                 ----- first implementation here
...
2152 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                 ^^^^^ conflicting implementation for `vultr::models::VultrObjectStorage`
     |
     = note: this error originates in the derive macro `Clone` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0119]: conflicting implementations of trait `Clone` for type `vultr::models::VultrOS`
    --> src\vultr\models.rs:2212:17
     |
174  | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                 ----- first implementation here
...
2212 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                 ^^^^^ conflicting implementation for `vultr::models::VultrOS`
     |
     = note: this error originates in the derive macro `Clone` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0119]: conflicting implementations of trait `Clone` for type `vultr::models::VultrOSResponse`
    --> src\vultr\models.rs:2220:17
     |
182  | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                 ----- first implementation here
...
2220 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                 ^^^^^ conflicting implementation for `vultr::models::VultrOSResponse`
     |
     = note: this error originates in the derive macro `Clone` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0119]: conflicting implementations of trait `Clone` for type `vultr::models::VultrPlan`
    --> src\vultr\models.rs:2227:17
     |
137  | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                 ----- first implementation here
...
2227 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                 ^^^^^ conflicting implementation for `vultr::models::VultrPlan`
     |
     = note: this error originates in the derive macro `Clone` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0119]: conflicting implementations of trait `Clone` for type `vultr::models::VultrPlansResponse`
    --> src\vultr\models.rs:2241:17
     |
151  | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                 ----- first implementation here
...
2241 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                 ^^^^^ conflicting implementation for `vultr::models::VultrPlansResponse`
     |
     = note: this error originates in the derive macro `Clone` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0119]: conflicting implementations of trait `Clone` for type `vultr::models::VultrRegion`
    --> src\vultr\models.rs:2373:17
     |
158  | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                 ----- first implementation here
...
2373 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                 ^^^^^ conflicting implementation for `vultr::models::VultrRegion`
     |
     = note: this error originates in the derive macro `Clone` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0119]: conflicting implementations of trait `Clone` for type `vultr::models::VultrRegionsResponse`
    --> src\vultr\models.rs:2382:17
     |
167  | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                 ----- first implementation here
...
2382 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                 ^^^^^ conflicting implementation for `vultr::models::VultrRegionsResponse`
     |
     = note: this error originates in the derive macro `Clone` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0119]: conflicting implementations of trait `Clone` for type `vultr::models::VultrSSHKey`
    --> src\vultr\models.rs:2453:17
     |
189  | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                 ----- first implementation here
...
2453 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                 ^^^^^ conflicting implementation for `vultr::models::VultrSSHKey`
     |
     = note: this error originates in the derive macro `Clone` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0119]: conflicting implementations of trait `Clone` for type `vultr::models::VultrSSHKeysResponse`
    --> src\vultr\models.rs:2461:17
     |
197  | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                 ----- first implementation here
...
2461 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                 ^^^^^ conflicting implementation for `vultr::models::VultrSSHKeysResponse`
     |
     = note: this error originates in the derive macro `Clone` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0119]: conflicting implementations of trait `Clone` for type `vultr::models::VultrSnapshot`
    --> src\vultr\models.rs:2664:17
     |
2394 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                 ----- first implementation here
...
2664 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                 ^^^^^ conflicting implementation for `vultr::models::VultrSnapshot`
     |
     = note: this error originates in the derive macro `Clone` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0119]: conflicting implementations of trait `Clone` for type `vultr::models::VultrStartupScript`
    --> src\vultr\models.rs:2688:17
     |
2480 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                 ----- first implementation here
...
2688 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                 ^^^^^ conflicting implementation for `vultr::models::VultrStartupScript`
     |
     = note: this error originates in the derive macro `Clone` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0119]: conflicting implementations of trait `Clone` for type `vultr::models::VultrISO`
    --> src\vultr\models.rs:2700:17
     |
1335 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                 ----- first implementation here
...
2700 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                 ^^^^^ conflicting implementation for `vultr::models::VultrISO`
     |
     = note: this error originates in the derive macro `Clone` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0119]: conflicting implementations of trait `Clone` for type `vultr::models::VultrStorageGateway`
    --> src\vultr\models.rs:2757:17
     |
2512 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                 ----- first implementation here
...
2757 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                 ^^^^^ conflicting implementation for `vultr::models::VultrStorageGateway`
     |
     = note: this error originates in the derive macro `Clone` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0119]: conflicting implementations of trait `Clone` for type `vultr::models::VultrUser`
    --> src\vultr\models.rs:2771:17
     |
2556 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                 ----- first implementation here
...
2771 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                 ^^^^^ conflicting implementation for `vultr::models::VultrUser`
     |
     = note: this error originates in the derive macro `Clone` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0119]: conflicting implementations of trait `std::fmt::Debug` for type `vultr::models::VultrInstancesResponse`
    --> src\vultr\models.rs:1125:10
     |
101  | #[derive(Debug, Clone, Serialize, Deserialize)]
     |          ----- first implementation here
...
1125 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |          ^^^^^ conflicting implementation for `vultr::models::VultrInstancesResponse`
     |
     = note: this error originates in the derive macro `Debug` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0119]: conflicting implementations of trait `std::fmt::Debug` for type `vultr::models::VultrLoadBalancerGenericInfo`
    --> src\vultr\models.rs:1557:10
     |
498  | #[derive(Debug, Clone, Serialize, Deserialize)]
     |          ----- first implementation here
...
1557 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |          ^^^^^ conflicting implementation for `vultr::models::VultrLoadBalancerGenericInfo`
     |
     = note: this error originates in the derive macro `Debug` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0119]: conflicting implementations of trait `std::fmt::Debug` for type `vultr::models::VultrHealthCheck`
    --> src\vultr\models.rs:1570:10
     |
513  | #[derive(Debug, Clone, Serialize, Deserialize)]
     |          ----- first implementation here
...
1570 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |          ^^^^^ conflicting implementation for `vultr::models::VultrHealthCheck`
     |
     = note: this error originates in the derive macro `Debug` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0119]: conflicting implementations of trait `std::fmt::Debug` for type `vultr::models::VultrManagedDatabase`
    --> src\vultr\models.rs:1663:10
     |
541  | #[derive(Debug, Clone, Serialize, Deserialize)]
     |          ----- first implementation here
...
1663 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |          ^^^^^ conflicting implementation for `vultr::models::VultrManagedDatabase`
     |
     = note: this error originates in the derive macro `Debug` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0119]: conflicting implementations of trait `std::fmt::Debug` for type `vultr::models::VultrObjectStorage`
    --> src\vultr\models.rs:2152:10
     |
575  | #[derive(Debug, Clone, Serialize, Deserialize)]
     |          ----- first implementation here
...
2152 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |          ^^^^^ conflicting implementation for `vultr::models::VultrObjectStorage`
     |
     = note: this error originates in the derive macro `Debug` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0119]: conflicting implementations of trait `std::fmt::Debug` for type `vultr::models::VultrOS`
    --> src\vultr\models.rs:2212:10
     |
174  | #[derive(Debug, Clone, Serialize, Deserialize)]
     |          ----- first implementation here
...
2212 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |          ^^^^^ conflicting implementation for `vultr::models::VultrOS`
     |
     = note: this error originates in the derive macro `Debug` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0119]: conflicting implementations of trait `std::fmt::Debug` for type `vultr::models::VultrOSResponse`
    --> src\vultr\models.rs:2220:10
     |
182  | #[derive(Debug, Clone, Serialize, Deserialize)]
     |          ----- first implementation here
...
2220 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |          ^^^^^ conflicting implementation for `vultr::models::VultrOSResponse`
     |
     = note: this error originates in the derive macro `Debug` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0119]: conflicting implementations of trait `std::fmt::Debug` for type `vultr::models::VultrPlan`
    --> src\vultr\models.rs:2227:10
     |
137  | #[derive(Debug, Clone, Serialize, Deserialize)]
     |          ----- first implementation here
...
2227 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |          ^^^^^ conflicting implementation for `vultr::models::VultrPlan`
     |
     = note: this error originates in the derive macro `Debug` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0119]: conflicting implementations of trait `std::fmt::Debug` for type `vultr::models::VultrPlansResponse`
    --> src\vultr\models.rs:2241:10
     |
151  | #[derive(Debug, Clone, Serialize, Deserialize)]
     |          ----- first implementation here
...
2241 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |          ^^^^^ conflicting implementation for `vultr::models::VultrPlansResponse`
     |
     = note: this error originates in the derive macro `Debug` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0119]: conflicting implementations of trait `std::fmt::Debug` for type `vultr::models::VultrRegion`
    --> src\vultr\models.rs:2373:10
     |
158  | #[derive(Debug, Clone, Serialize, Deserialize)]
     |          ----- first implementation here
...
2373 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |          ^^^^^ conflicting implementation for `vultr::models::VultrRegion`
     |
     = note: this error originates in the derive macro `Debug` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0119]: conflicting implementations of trait `std::fmt::Debug` for type `vultr::models::VultrRegionsResponse`
    --> src\vultr\models.rs:2382:10
     |
167  | #[derive(Debug, Clone, Serialize, Deserialize)]
     |          ----- first implementation here
...
2382 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |          ^^^^^ conflicting implementation for `vultr::models::VultrRegionsResponse`
     |
     = note: this error originates in the derive macro `Debug` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0119]: conflicting implementations of trait `std::fmt::Debug` for type `vultr::models::VultrSSHKey`
    --> src\vultr\models.rs:2453:10
     |
189  | #[derive(Debug, Clone, Serialize, Deserialize)]
     |          ----- first implementation here
...
2453 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |          ^^^^^ conflicting implementation for `vultr::models::VultrSSHKey`
     |
     = note: this error originates in the derive macro `Debug` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0119]: conflicting implementations of trait `std::fmt::Debug` for type `vultr::models::VultrSSHKeysResponse`
    --> src\vultr\models.rs:2461:10
     |
197  | #[derive(Debug, Clone, Serialize, Deserialize)]
     |          ----- first implementation here
...
2461 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |          ^^^^^ conflicting implementation for `vultr::models::VultrSSHKeysResponse`
     |
     = note: this error originates in the derive macro `Debug` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0119]: conflicting implementations of trait `std::fmt::Debug` for type `vultr::models::VultrSnapshot`
    --> src\vultr\models.rs:2664:10
     |
2394 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |          ----- first implementation here
...
2664 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |          ^^^^^ conflicting implementation for `vultr::models::VultrSnapshot`
     |
     = note: this error originates in the derive macro `Debug` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0119]: conflicting implementations of trait `std::fmt::Debug` for type `vultr::models::VultrStartupScript`
    --> src\vultr\models.rs:2688:10
     |
2480 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |          ----- first implementation here
...
2688 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |          ^^^^^ conflicting implementation for `vultr::models::VultrStartupScript`
     |
     = note: this error originates in the derive macro `Debug` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0119]: conflicting implementations of trait `std::fmt::Debug` for type `vultr::models::VultrISO`
    --> src\vultr\models.rs:2700:10
     |
1335 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |          ----- first implementation here
...
2700 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |          ^^^^^ conflicting implementation for `vultr::models::VultrISO`
     |
     = note: this error originates in the derive macro `Debug` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0119]: conflicting implementations of trait `std::fmt::Debug` for type `vultr::models::VultrStorageGateway`
    --> src\vultr\models.rs:2757:10
     |
2512 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |          ----- first implementation here
...
2757 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |          ^^^^^ conflicting implementation for `vultr::models::VultrStorageGateway`
     |
     = note: this error originates in the derive macro `Debug` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0119]: conflicting implementations of trait `std::fmt::Debug` for type `vultr::models::VultrUser`
    --> src\vultr\models.rs:2771:10
     |
2556 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |          ----- first implementation here
...
2771 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |          ^^^^^ conflicting implementation for `vultr::models::VultrUser`
     |
     = note: this error originates in the derive macro `Debug` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0119]: conflicting implementations of trait `Serialize` for type `vultr::models::VultrInstancesResponse`
    --> src\vultr\models.rs:1125:24
     |
101  | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                        --------- first implementation here
...
1125 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                        ^^^^^^^^^ conflicting implementation for `vultr::models::VultrInstancesResponse`
     |
     = note: this error originates in the derive macro `Serialize` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0119]: conflicting implementations of trait `Serialize` for type `vultr::models::VultrLoadBalancerGenericInfo`
    --> src\vultr\models.rs:1557:24
     |
498  | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                        --------- first implementation here
...
1557 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                        ^^^^^^^^^ conflicting implementation for `vultr::models::VultrLoadBalancerGenericInfo`
     |
     = note: this error originates in the derive macro `Serialize` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0119]: conflicting implementations of trait `Serialize` for type `vultr::models::VultrHealthCheck`
    --> src\vultr\models.rs:1570:24
     |
513  | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                        --------- first implementation here
...
1570 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                        ^^^^^^^^^ conflicting implementation for `vultr::models::VultrHealthCheck`
     |
     = note: this error originates in the derive macro `Serialize` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0119]: conflicting implementations of trait `Serialize` for type `vultr::models::VultrManagedDatabase`
    --> src\vultr\models.rs:1663:24
     |
541  | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                        --------- first implementation here
...
1663 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                        ^^^^^^^^^ conflicting implementation for `vultr::models::VultrManagedDatabase`
     |
     = note: this error originates in the derive macro `Serialize` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0119]: conflicting implementations of trait `Serialize` for type `vultr::models::VultrObjectStorage`
    --> src\vultr\models.rs:2152:24
     |
575  | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                        --------- first implementation here
...
2152 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                        ^^^^^^^^^ conflicting implementation for `vultr::models::VultrObjectStorage`
     |
     = note: this error originates in the derive macro `Serialize` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0119]: conflicting implementations of trait `Serialize` for type `vultr::models::VultrOS`
    --> src\vultr\models.rs:2212:24
     |
174  | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                        --------- first implementation here
...
2212 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                        ^^^^^^^^^ conflicting implementation for `vultr::models::VultrOS`
     |
     = note: this error originates in the derive macro `Serialize` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0119]: conflicting implementations of trait `Serialize` for type `vultr::models::VultrOSResponse`
    --> src\vultr\models.rs:2220:24
     |
182  | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                        --------- first implementation here
...
2220 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                        ^^^^^^^^^ conflicting implementation for `vultr::models::VultrOSResponse`
     |
     = note: this error originates in the derive macro `Serialize` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0119]: conflicting implementations of trait `Serialize` for type `vultr::models::VultrPlan`
    --> src\vultr\models.rs:2227:24
     |
137  | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                        --------- first implementation here
...
2227 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                        ^^^^^^^^^ conflicting implementation for `vultr::models::VultrPlan`
     |
     = note: this error originates in the derive macro `Serialize` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0119]: conflicting implementations of trait `Serialize` for type `vultr::models::VultrPlansResponse`
    --> src\vultr\models.rs:2241:24
     |
151  | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                        --------- first implementation here
...
2241 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                        ^^^^^^^^^ conflicting implementation for `vultr::models::VultrPlansResponse`
     |
     = note: this error originates in the derive macro `Serialize` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0119]: conflicting implementations of trait `Serialize` for type `vultr::models::VultrRegion`
    --> src\vultr\models.rs:2373:24
     |
158  | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                        --------- first implementation here
...
2373 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                        ^^^^^^^^^ conflicting implementation for `vultr::models::VultrRegion`
     |
     = note: this error originates in the derive macro `Serialize` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0119]: conflicting implementations of trait `Serialize` for type `vultr::models::VultrRegionsResponse`
    --> src\vultr\models.rs:2382:24
     |
167  | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                        --------- first implementation here
...
2382 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                        ^^^^^^^^^ conflicting implementation for `vultr::models::VultrRegionsResponse`
     |
     = note: this error originates in the derive macro `Serialize` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0119]: conflicting implementations of trait `Serialize` for type `vultr::models::VultrSSHKey`
    --> src\vultr\models.rs:2453:24
     |
189  | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                        --------- first implementation here
...
2453 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                        ^^^^^^^^^ conflicting implementation for `vultr::models::VultrSSHKey`
     |
     = note: this error originates in the derive macro `Serialize` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0119]: conflicting implementations of trait `Serialize` for type `vultr::models::VultrSSHKeysResponse`
    --> src\vultr\models.rs:2461:24
     |
197  | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                        --------- first implementation here
...
2461 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                        ^^^^^^^^^ conflicting implementation for `vultr::models::VultrSSHKeysResponse`
     |
     = note: this error originates in the derive macro `Serialize` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0119]: conflicting implementations of trait `Serialize` for type `vultr::models::VultrSnapshot`
    --> src\vultr\models.rs:2664:24
     |
2394 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                        --------- first implementation here
...
2664 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                        ^^^^^^^^^ conflicting implementation for `vultr::models::VultrSnapshot`
     |
     = note: this error originates in the derive macro `Serialize` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0119]: conflicting implementations of trait `Serialize` for type `vultr::models::VultrStartupScript`
    --> src\vultr\models.rs:2688:24
     |
2480 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                        --------- first implementation here
...
2688 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                        ^^^^^^^^^ conflicting implementation for `vultr::models::VultrStartupScript`
     |
     = note: this error originates in the derive macro `Serialize` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0119]: conflicting implementations of trait `Serialize` for type `vultr::models::VultrISO`
    --> src\vultr\models.rs:2700:24
     |
1335 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                        --------- first implementation here
...
2700 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                        ^^^^^^^^^ conflicting implementation for `vultr::models::VultrISO`
     |
     = note: this error originates in the derive macro `Serialize` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0119]: conflicting implementations of trait `Serialize` for type `vultr::models::VultrStorageGateway`
    --> src\vultr\models.rs:2757:24
     |
2512 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                        --------- first implementation here
...
2757 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                        ^^^^^^^^^ conflicting implementation for `vultr::models::VultrStorageGateway`
     |
     = note: this error originates in the derive macro `Serialize` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0119]: conflicting implementations of trait `Serialize` for type `vultr::models::VultrUser`
    --> src\vultr\models.rs:2771:24
     |
2556 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                        --------- first implementation here
...
2771 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                        ^^^^^^^^^ conflicting implementation for `vultr::models::VultrUser`
     |
     = note: this error originates in the derive macro `Serialize` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0119]: conflicting implementations of trait `Deserialize<'_>` for type `vultr::models::VultrInstancesResponse`
    --> src\vultr\models.rs:1125:35
     |
101  | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                                   ----------- first implementation here
...
1125 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                                   ^^^^^^^^^^^ conflicting implementation for `vultr::models::VultrInstancesResponse`
     |
     = note: this error originates in the derive macro `Deserialize` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0119]: conflicting implementations of trait `Deserialize<'_>` for type `vultr::models::VultrLoadBalancerGenericInfo`
    --> src\vultr\models.rs:1557:35
     |
498  | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                                   ----------- first implementation here
...
1557 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                                   ^^^^^^^^^^^ conflicting implementation for `vultr::models::VultrLoadBalancerGenericInfo`
     |
     = note: this error originates in the derive macro `Deserialize` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0119]: conflicting implementations of trait `Deserialize<'_>` for type `vultr::models::VultrHealthCheck`
    --> src\vultr\models.rs:1570:35
     |
513  | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                                   ----------- first implementation here
...
1570 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                                   ^^^^^^^^^^^ conflicting implementation for `vultr::models::VultrHealthCheck`
     |
     = note: this error originates in the derive macro `Deserialize` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0119]: conflicting implementations of trait `Deserialize<'_>` for type `vultr::models::VultrManagedDatabase`
    --> src\vultr\models.rs:1663:35
     |
541  | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                                   ----------- first implementation here
...
1663 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                                   ^^^^^^^^^^^ conflicting implementation for `vultr::models::VultrManagedDatabase`
     |
     = note: this error originates in the derive macro `Deserialize` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0119]: conflicting implementations of trait `Deserialize<'_>` for type `vultr::models::VultrObjectStorage`
    --> src\vultr\models.rs:2152:35
     |
575  | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                                   ----------- first implementation here
...
2152 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                                   ^^^^^^^^^^^ conflicting implementation for `vultr::models::VultrObjectStorage`
     |
     = note: this error originates in the derive macro `Deserialize` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0119]: conflicting implementations of trait `Deserialize<'_>` for type `vultr::models::VultrOS`
    --> src\vultr\models.rs:2212:35
     |
174  | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                                   ----------- first implementation here
...
2212 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                                   ^^^^^^^^^^^ conflicting implementation for `vultr::models::VultrOS`
     |
     = note: this error originates in the derive macro `Deserialize` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0119]: conflicting implementations of trait `Deserialize<'_>` for type `vultr::models::VultrOSResponse`
    --> src\vultr\models.rs:2220:35
     |
182  | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                                   ----------- first implementation here
...
2220 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                                   ^^^^^^^^^^^ conflicting implementation for `vultr::models::VultrOSResponse`
     |
     = note: this error originates in the derive macro `Deserialize` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0119]: conflicting implementations of trait `Deserialize<'_>` for type `vultr::models::VultrPlan`
    --> src\vultr\models.rs:2227:35
     |
137  | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                                   ----------- first implementation here
...
2227 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                                   ^^^^^^^^^^^ conflicting implementation for `vultr::models::VultrPlan`
     |
     = note: this error originates in the derive macro `Deserialize` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0119]: conflicting implementations of trait `Deserialize<'_>` for type `vultr::models::VultrPlansResponse`
    --> src\vultr\models.rs:2241:35
     |
151  | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                                   ----------- first implementation here
...
2241 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                                   ^^^^^^^^^^^ conflicting implementation for `vultr::models::VultrPlansResponse`
     |
     = note: this error originates in the derive macro `Deserialize` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0119]: conflicting implementations of trait `Deserialize<'_>` for type `vultr::models::VultrRegion`
    --> src\vultr\models.rs:2373:35
     |
158  | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                                   ----------- first implementation here
...
2373 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                                   ^^^^^^^^^^^ conflicting implementation for `vultr::models::VultrRegion`
     |
     = note: this error originates in the derive macro `Deserialize` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0119]: conflicting implementations of trait `Deserialize<'_>` for type `vultr::models::VultrRegionsResponse`
    --> src\vultr\models.rs:2382:35
     |
167  | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                                   ----------- first implementation here
...
2382 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                                   ^^^^^^^^^^^ conflicting implementation for `vultr::models::VultrRegionsResponse`
     |
     = note: this error originates in the derive macro `Deserialize` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0119]: conflicting implementations of trait `Deserialize<'_>` for type `vultr::models::VultrSSHKey`
    --> src\vultr\models.rs:2453:35
     |
189  | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                                   ----------- first implementation here
...
2453 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                                   ^^^^^^^^^^^ conflicting implementation for `vultr::models::VultrSSHKey`
     |
     = note: this error originates in the derive macro `Deserialize` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0119]: conflicting implementations of trait `Deserialize<'_>` for type `vultr::models::VultrSSHKeysResponse`
    --> src\vultr\models.rs:2461:35
     |
197  | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                                   ----------- first implementation here
...
2461 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                                   ^^^^^^^^^^^ conflicting implementation for `vultr::models::VultrSSHKeysResponse`
     |
     = note: this error originates in the derive macro `Deserialize` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0119]: conflicting implementations of trait `Deserialize<'_>` for type `vultr::models::VultrSnapshot`
    --> src\vultr\models.rs:2664:35
     |
2394 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                                   ----------- first implementation here
...
2664 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                                   ^^^^^^^^^^^ conflicting implementation for `vultr::models::VultrSnapshot`
     |
     = note: this error originates in the derive macro `Deserialize` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0119]: conflicting implementations of trait `Deserialize<'_>` for type `vultr::models::VultrStartupScript`
    --> src\vultr\models.rs:2688:35
     |
2480 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                                   ----------- first implementation here
...
2688 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                                   ^^^^^^^^^^^ conflicting implementation for `vultr::models::VultrStartupScript`
     |
     = note: this error originates in the derive macro `Deserialize` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0119]: conflicting implementations of trait `Deserialize<'_>` for type `vultr::models::VultrISO`
    --> src\vultr\models.rs:2700:35
     |
1335 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                                   ----------- first implementation here
...
2700 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                                   ^^^^^^^^^^^ conflicting implementation for `vultr::models::VultrISO`
     |
     = note: this error originates in the derive macro `Deserialize` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0119]: conflicting implementations of trait `Deserialize<'_>` for type `vultr::models::VultrStorageGateway`
    --> src\vultr\models.rs:2757:35
     |
2512 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                                   ----------- first implementation here
...
2757 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                                   ^^^^^^^^^^^ conflicting implementation for `vultr::models::VultrStorageGateway`
     |
     = note: this error originates in the derive macro `Deserialize` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0119]: conflicting implementations of trait `Deserialize<'_>` for type `vultr::models::VultrUser`
    --> src\vultr\models.rs:2771:35
     |
2556 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                                   ----------- first implementation here
...
2771 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                                   ^^^^^^^^^^^ conflicting implementation for `vultr::models::VultrUser`
     |
     = note: this error originates in the derive macro `Deserialize` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0107]: struct takes 0 generic arguments but 1 generic argument was supplied
   --> src\middleware\mod.rs:17:11
    |
17  |     next: Next<B>,
    |           ^^^^--- help: remove the unnecessary generics
    |           |
    |           expected 0 generic arguments
    |
note: struct defined here, with 0 generic parameters
   --> C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\axum-0.7.9\src\middleware\from_fn.rs:335:12
    |
335 | pub struct Next {
    |            ^^^^

error[E0107]: struct takes 0 generic arguments but 1 generic argument was supplied
   --> src\middleware\mod.rs:32:11
    |
32  |     next: Next<B>,
    |           ^^^^--- help: remove the unnecessary generics
    |           |
    |           expected 0 generic arguments
    |
note: struct defined here, with 0 generic parameters
   --> C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\axum-0.7.9\src\middleware\from_fn.rs:335:12
    |
335 | pub struct Next {
    |            ^^^^

error[E0107]: struct takes 0 generic arguments but 1 generic argument was supplied
   --> src\middleware\mod.rs:67:11
    |
67  |     next: Next<B>,
    |           ^^^^--- help: remove the unnecessary generics
    |           |
    |           expected 0 generic arguments
    |
note: struct defined here, with 0 generic parameters
   --> C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\axum-0.7.9\src\middleware\from_fn.rs:335:12
    |
335 | pub struct Next {
    |            ^^^^

error[E0107]: struct takes 0 generic arguments but 1 generic argument was supplied
   --> src\observability\mod.rs:96:29
    |
96  |     next: axum::middleware::Next<B>,
    |                             ^^^^--- help: remove the unnecessary generics
    |                             |
    |                             expected 0 generic arguments
    |
note: struct defined here, with 0 generic parameters
   --> C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\axum-0.7.9\src\middleware\from_fn.rs:335:12
    |
335 | pub struct Next {
    |            ^^^^

error[E0107]: struct takes 0 lifetime arguments but 1 lifetime argument was supplied
  --> src\services\billing.rs:21:10
   |
21 | impl<'a> BillingService<'a> {
   |          ^^^^^^^^^^^^^^---- help: remove the unnecessary generics
   |          |
   |          expected 0 lifetime arguments
   |
note: struct defined here, with 0 lifetime parameters
  --> src\services\billing.rs:15:12
   |
15 | pub struct BillingService {
   |            ^^^^^^^^^^^^^^

error[E0107]: struct takes 0 lifetime arguments but 1 lifetime argument was supplied
  --> src\services\user.rs:15:10
   |
15 | impl<'a> UserService<'a> {
   |          ^^^^^^^^^^^---- help: remove the unnecessary generics
   |          |
   |          expected 0 lifetime arguments
   |
note: struct defined here, with 0 lifetime parameters
  --> src\services\user.rs:11:12
   |
11 | pub struct UserService {
   |            ^^^^^^^^^^^

warning: unused variable: `state`
  --> src\controllers\auth.rs:46:11
   |
46 |     State(state): State<Arc<AppState>>,
   |           ^^^^^ help: if this is intentional, prefix it with an underscore: `_state`
   |
   = note: `#[warn(unused_variables)]` on by default

error[E0277]: `?` couldn't convert the error to `ControllerError`
  --> src\controllers\billing.rs:21:40
   |
21 |     let claims = get_current_user(&req)?;
   |                  ----------------------^ the trait `From<axum::http::StatusCode>` is not implemented for `ControllerError`
   |                  |
   |                  this can't be annotated with `?` because it has type `Result<_, axum::http::StatusCode>`
   |
   = note: the question mark operation (`?`) implicitly performs a conversion on the error value using the `From` trait
   = help: the following other types implement trait `From<T>`:
             `ControllerError` implements `From<mongodb::error::Error>`
             `ControllerError` implements `From<services::ServiceError>`
   = note: required for `Result<axum::Json<ApiResponse<models::billing::BillingResponse>>, ControllerError>` to implement 
`FromResidual<Result<Infallible, axum::http::StatusCode>>`

error[E0599]: no function or associated item named `new` found for struct `BillingService` in the current scope
  --> src\controllers\billing.rs:23:43
   |
23 |     let billing_service = BillingService::new(&state.database);
   |                                           ^^^ function or associated item not found in `BillingService`
   |
  ::: src\services\billing.rs:15:1
   |
15 | pub struct BillingService {
   | ------------------------- function or associated item `new` not found for this struct
   |
   = help: items from traits can only be used if the trait is implemented and in scope
   = note: the following traits define an item `new`, perhaps you need to implement one of them:
           candidate #1: `ahash::HashMapExt`
           candidate #2: `ahash::HashSetExt`
           candidate #3: `bitvec::store::BitStore`
           candidate #4: `crypto_common::KeyInit`
           candidate #5: `crypto_common::KeyIvInit`
           candidate #6: `digest::VariableOutput`
           candidate #7: `digest::core_api::VariableOutputCore`
           candidate #8: `digest::digest::Digest`
           candidate #9: `digest::mac::Mac`
           candidate #10: `parking_lot_core::thread_parker::ThreadParkerT`
           candidate #11: `radium::Radium`
           candidate #12: `rand::distr::uniform::UniformSampler`
           candidate #13: `rand::distributions::uniform::UniformSampler`
           candidate #14: `ring::aead::BoundKey`
           candidate #15: `serde_with::duplicate_key_impls::error_on_duplicate::PreventDuplicateInsertsMap`
           candidate #16: `serde_with::duplicate_key_impls::error_on_duplicate::PreventDuplicateInsertsSet`
           candidate #17: `serde_with::duplicate_key_impls::first_value_wins::DuplicateInsertsFirstWinsMap`
           candidate #18: `serde_with::duplicate_key_impls::first_value_wins::DuplicateInsertsFirstWinsSet`
           candidate #19: `serde_with::duplicate_key_impls::last_value_wins::DuplicateInsertsLastWinsSet`
           candidate #20: `trust_dns_proto::Executor`
           candidate #21: `typenum::marker_traits::Bit`

error[E0277]: `?` couldn't convert the error to `ControllerError`
  --> src\controllers\billing.rs:35:40
   |
35 |     let claims = get_current_user(&req)?;
   |                  ----------------------^ the trait `From<axum::http::StatusCode>` is not implemented for `ControllerError`
   |                  |
   |                  this can't be annotated with `?` because it has type `Result<_, axum::http::StatusCode>`
   |
   = note: the question mark operation (`?`) implicitly performs a conversion on the error value using the `From` trait
   = help: the following other types implement trait `From<T>`:
             `ControllerError` implements `From<mongodb::error::Error>`
             `ControllerError` implements `From<services::ServiceError>`
   = note: required for `Result<Json<ApiResponse<PaginatedResponse<InvoiceResponse>>>, ...>` to implement `FromResidual<Result<Infallible, 
axum::http::StatusCode>>`
   = note: the full name for the type has been written to 
'D:\workspace\.rust\achidas\target\debug\deps\achidas-4d377389ccdf1153.long-type-6768453638641027346.txt'
   = note: consider using `--verbose` to print the full type name to the console

error[E0599]: no function or associated item named `new` found for struct `BillingService` in the current scope
  --> src\controllers\billing.rs:41:43
   |
41 |     let billing_service = BillingService::new(&state.database);
   |                                           ^^^ function or associated item not found in `BillingService`
   |
  ::: src\services\billing.rs:15:1
   |
15 | pub struct BillingService {
   | ------------------------- function or associated item `new` not found for this struct
   |
   = help: items from traits can only be used if the trait is implemented and in scope
   = note: the following traits define an item `new`, perhaps you need to implement one of them:
           candidate #1: `ahash::HashMapExt`
           candidate #2: `ahash::HashSetExt`
           candidate #3: `bitvec::store::BitStore`
           candidate #4: `crypto_common::KeyInit`
           candidate #5: `crypto_common::KeyIvInit`
           candidate #6: `digest::VariableOutput`
           candidate #7: `digest::core_api::VariableOutputCore`
           candidate #8: `digest::digest::Digest`
           candidate #9: `digest::mac::Mac`
           candidate #10: `parking_lot_core::thread_parker::ThreadParkerT`
           candidate #11: `radium::Radium`
           candidate #12: `rand::distr::uniform::UniformSampler`
           candidate #13: `rand::distributions::uniform::UniformSampler`
           candidate #14: `ring::aead::BoundKey`
           candidate #15: `serde_with::duplicate_key_impls::error_on_duplicate::PreventDuplicateInsertsMap`
           candidate #16: `serde_with::duplicate_key_impls::error_on_duplicate::PreventDuplicateInsertsSet`
           candidate #17: `serde_with::duplicate_key_impls::first_value_wins::DuplicateInsertsFirstWinsMap`
           candidate #18: `serde_with::duplicate_key_impls::first_value_wins::DuplicateInsertsFirstWinsSet`
           candidate #19: `serde_with::duplicate_key_impls::last_value_wins::DuplicateInsertsLastWinsSet`
           candidate #20: `trust_dns_proto::Executor`
           candidate #21: `typenum::marker_traits::Bit`

error[E0277]: `?` couldn't convert the error to `ControllerError`
  --> src\controllers\billing.rs:55:40
   |
55 |     let claims = get_current_user(&req)?;
   |                  ----------------------^ the trait `From<axum::http::StatusCode>` is not implemented for `ControllerError`
   |                  |
   |                  this can't be annotated with `?` because it has type `Result<_, axum::http::StatusCode>`
   |
   = note: the question mark operation (`?`) implicitly performs a conversion on the error value using the `From` trait
   = help: the following other types implement trait `From<T>`:
             `ControllerError` implements `From<mongodb::error::Error>`
             `ControllerError` implements `From<services::ServiceError>`
   = note: required for `Result<axum::Json<ApiResponse<models::billing::InvoiceResponse>>, ControllerError>` to implement 
`FromResidual<Result<Infallible, axum::http::StatusCode>>`

error[E0599]: no function or associated item named `new` found for struct `BillingService` in the current scope
  --> src\controllers\billing.rs:57:43
   |
57 |     let billing_service = BillingService::new(&state.database);
   |                                           ^^^ function or associated item not found in `BillingService`
   |
  ::: src\services\billing.rs:15:1
   |
15 | pub struct BillingService {
   | ------------------------- function or associated item `new` not found for this struct
   |
   = help: items from traits can only be used if the trait is implemented and in scope
   = note: the following traits define an item `new`, perhaps you need to implement one of them:
           candidate #1: `ahash::HashMapExt`
           candidate #2: `ahash::HashSetExt`
           candidate #3: `bitvec::store::BitStore`
           candidate #4: `crypto_common::KeyInit`
           candidate #5: `crypto_common::KeyIvInit`
           candidate #6: `digest::VariableOutput`
           candidate #7: `digest::core_api::VariableOutputCore`
           candidate #8: `digest::digest::Digest`
           candidate #9: `digest::mac::Mac`
           candidate #10: `parking_lot_core::thread_parker::ThreadParkerT`
           candidate #11: `radium::Radium`
           candidate #12: `rand::distr::uniform::UniformSampler`
           candidate #13: `rand::distributions::uniform::UniformSampler`
           candidate #14: `ring::aead::BoundKey`
           candidate #15: `serde_with::duplicate_key_impls::error_on_duplicate::PreventDuplicateInsertsMap`
           candidate #16: `serde_with::duplicate_key_impls::error_on_duplicate::PreventDuplicateInsertsSet`
           candidate #17: `serde_with::duplicate_key_impls::first_value_wins::DuplicateInsertsFirstWinsMap`
           candidate #18: `serde_with::duplicate_key_impls::first_value_wins::DuplicateInsertsFirstWinsSet`
           candidate #19: `serde_with::duplicate_key_impls::last_value_wins::DuplicateInsertsLastWinsSet`
           candidate #20: `trust_dns_proto::Executor`
           candidate #21: `typenum::marker_traits::Bit`

error[E0277]: `?` couldn't convert the error to `ControllerError`
  --> src\controllers\billing.rs:71:40
   |
71 |     let claims = get_current_user(&req)?;
   |                  ----------------------^ the trait `From<axum::http::StatusCode>` is not implemented for `ControllerError`
   |                  |
   |                  this can't be annotated with `?` because it has type `Result<_, axum::http::StatusCode>`
   |
   = note: the question mark operation (`?`) implicitly performs a conversion on the error value using the `From` trait
   = help: the following other types implement trait `From<T>`:
             `ControllerError` implements `From<mongodb::error::Error>`
             `ControllerError` implements `From<services::ServiceError>`
   = note: required for `Result<axum::Json<ApiResponse<serde_json::Value>>, ControllerError>` to implement `FromResidual<Result<Infallible, 
axum::http::StatusCode>>`

error[E0599]: no function or associated item named `new` found for struct `BillingService` in the current scope
  --> src\controllers\billing.rs:73:43
   |
73 |     let billing_service = BillingService::new(&state.database);
   |                                           ^^^ function or associated item not found in `BillingService`
   |
  ::: src\services\billing.rs:15:1
   |
15 | pub struct BillingService {
   | ------------------------- function or associated item `new` not found for this struct
   |
   = help: items from traits can only be used if the trait is implemented and in scope
   = note: the following traits define an item `new`, perhaps you need to implement one of them:
           candidate #1: `ahash::HashMapExt`
           candidate #2: `ahash::HashSetExt`
           candidate #3: `bitvec::store::BitStore`
           candidate #4: `crypto_common::KeyInit`
           candidate #5: `crypto_common::KeyIvInit`
           candidate #6: `digest::VariableOutput`
           candidate #7: `digest::core_api::VariableOutputCore`
           candidate #8: `digest::digest::Digest`
           candidate #9: `digest::mac::Mac`
           candidate #10: `parking_lot_core::thread_parker::ThreadParkerT`
           candidate #11: `radium::Radium`
           candidate #12: `rand::distr::uniform::UniformSampler`
           candidate #13: `rand::distributions::uniform::UniformSampler`
           candidate #14: `ring::aead::BoundKey`
           candidate #15: `serde_with::duplicate_key_impls::error_on_duplicate::PreventDuplicateInsertsMap`
           candidate #16: `serde_with::duplicate_key_impls::error_on_duplicate::PreventDuplicateInsertsSet`
           candidate #17: `serde_with::duplicate_key_impls::first_value_wins::DuplicateInsertsFirstWinsMap`
           candidate #18: `serde_with::duplicate_key_impls::first_value_wins::DuplicateInsertsFirstWinsSet`
           candidate #19: `serde_with::duplicate_key_impls::last_value_wins::DuplicateInsertsLastWinsSet`
           candidate #20: `trust_dns_proto::Executor`
           candidate #21: `typenum::marker_traits::Bit`

error[E0277]: `?` couldn't convert the error to `ControllerError`
  --> src\controllers\instances.rs:22:40
   |
22 |     let claims = get_current_user(&req)?;
   |                  ----------------------^ the trait `From<axum::http::StatusCode>` is not implemented for `ControllerError`
   |                  |
   |                  this can't be annotated with `?` because it has type `Result<_, axum::http::StatusCode>`
   |
   = note: the question mark operation (`?`) implicitly performs a conversion on the error value using the `From` trait
   = help: the following other types implement trait `From<T>`:
             `ControllerError` implements `From<mongodb::error::Error>`
             `ControllerError` implements `From<services::ServiceError>`
   = note: required for `Result<Json<ApiResponse<PaginatedResponse<InstanceResponse>>>, ...>` to implement `FromResidual<Result<Infallible, 
axum::http::StatusCode>>`
   = note: the full name for the type has been written to 
'D:\workspace\.rust\achidas\target\debug\deps\achidas-4d377389ccdf1153.long-type-15968301607589995171.txt'
   = note: consider using `--verbose` to print the full type name to the console

error[E0277]: `?` couldn't convert the error to `ControllerError`
  --> src\controllers\instances.rs:42:40
   |
42 |     let claims = get_current_user(&req)?;
   |                  ----------------------^ the trait `From<axum::http::StatusCode>` is not implemented for `ControllerError`
   |                  |
   |                  this can't be annotated with `?` because it has type `Result<_, axum::http::StatusCode>`
   |
   = note: the question mark operation (`?`) implicitly performs a conversion on the error value using the `From` trait
   = help: the following other types implement trait `From<T>`:
             `ControllerError` implements `From<mongodb::error::Error>`
             `ControllerError` implements `From<services::ServiceError>`
   = note: required for `Result<axum::Json<ApiResponse<models::instance::InstanceResponse>>, ControllerError>` to implement 
`FromResidual<Result<Infallible, axum::http::StatusCode>>`

error[E0277]: `?` couldn't convert the error to `ControllerError`
  --> src\controllers\instances.rs:58:40
   |
58 |     let claims = get_current_user(&req)?;
   |                  ----------------------^ the trait `From<axum::http::StatusCode>` is not implemented for `ControllerError`
   |                  |
   |                  this can't be annotated with `?` because it has type `Result<_, axum::http::StatusCode>`
   |
   = note: the question mark operation (`?`) implicitly performs a conversion on the error value using the `From` trait
   = help: the following other types implement trait `From<T>`:
             `ControllerError` implements `From<mongodb::error::Error>`
             `ControllerError` implements `From<services::ServiceError>`
   = note: required for `Result<axum::Json<ApiResponse<models::instance::InstanceResponse>>, ControllerError>` to implement 
`FromResidual<Result<Infallible, axum::http::StatusCode>>`

error[E0277]: `?` couldn't convert the error to `ControllerError`
  --> src\controllers\instances.rs:79:40
   |
79 |     let claims = get_current_user(&req)?;
   |                  ----------------------^ the trait `From<axum::http::StatusCode>` is not implemented for `ControllerError`
   |                  |
   |                  this can't be annotated with `?` because it has type `Result<_, axum::http::StatusCode>`
   |
   = note: the question mark operation (`?`) implicitly performs a conversion on the error value using the `From` trait
   = help: the following other types implement trait `From<T>`:
             `ControllerError` implements `From<mongodb::error::Error>`
             `ControllerError` implements `From<services::ServiceError>`
   = note: required for `Result<axum::Json<ApiResponse<models::instance::InstanceResponse>>, ControllerError>` to implement 
`FromResidual<Result<Infallible, axum::http::StatusCode>>`

error[E0277]: `?` couldn't convert the error to `ControllerError`
  --> src\controllers\instances.rs:95:40
   |
95 |     let claims = get_current_user(&req)?;
   |                  ----------------------^ the trait `From<axum::http::StatusCode>` is not implemented for `ControllerError`
   |                  |
   |                  this can't be annotated with `?` because it has type `Result<_, axum::http::StatusCode>`
   |
   = note: the question mark operation (`?`) implicitly performs a conversion on the error value using the `From` trait
   = help: the following other types implement trait `From<T>`:
             `ControllerError` implements `From<mongodb::error::Error>`
             `ControllerError` implements `From<services::ServiceError>`
   = note: required for `Result<axum::Json<ApiResponse<()>>, ControllerError>` to implement `FromResidual<Result<Infallible, 
axum::http::StatusCode>>`

error[E0277]: `?` couldn't convert the error to `ControllerError`
   --> src\controllers\instances.rs:111:40
    |
111 |     let claims = get_current_user(&req)?;
    |                  ----------------------^ the trait `From<axum::http::StatusCode>` is not implemented for `ControllerError`
    |                  |
    |                  this can't be annotated with `?` because it has type `Result<_, axum::http::StatusCode>`
    |
    = note: the question mark operation (`?`) implicitly performs a conversion on the error value using the `From` trait
    = help: the following other types implement trait `From<T>`:
              `ControllerError` implements `From<mongodb::error::Error>`
              `ControllerError` implements `From<services::ServiceError>`
    = note: required for `Result<axum::Json<ApiResponse<()>>, ControllerError>` to implement `FromResidual<Result<Infallible, 
axum::http::StatusCode>>`

error[E0277]: `?` couldn't convert the error to `ControllerError`
   --> src\controllers\instances.rs:127:40
    |
127 |     let claims = get_current_user(&req)?;
    |                  ----------------------^ the trait `From<axum::http::StatusCode>` is not implemented for `ControllerError`
    |                  |
    |                  this can't be annotated with `?` because it has type `Result<_, axum::http::StatusCode>`
    |
    = note: the question mark operation (`?`) implicitly performs a conversion on the error value using the `From` trait
    = help: the following other types implement trait `From<T>`:
              `ControllerError` implements `From<mongodb::error::Error>`
              `ControllerError` implements `From<services::ServiceError>`
    = note: required for `Result<axum::Json<ApiResponse<()>>, ControllerError>` to implement `FromResidual<Result<Infallible, 
axum::http::StatusCode>>`

error[E0277]: `?` couldn't convert the error to `ControllerError`
   --> src\controllers\instances.rs:143:40
    |
143 |     let claims = get_current_user(&req)?;
    |                  ----------------------^ the trait `From<axum::http::StatusCode>` is not implemented for `ControllerError`
    |                  |
    |                  this can't be annotated with `?` because it has type `Result<_, axum::http::StatusCode>`
    |
    = note: the question mark operation (`?`) implicitly performs a conversion on the error value using the `From` trait
    = help: the following other types implement trait `From<T>`:
              `ControllerError` implements `From<mongodb::error::Error>`
              `ControllerError` implements `From<services::ServiceError>`
    = note: required for `Result<axum::Json<ApiResponse<()>>, ControllerError>` to implement `FromResidual<Result<Infallible, 
axum::http::StatusCode>>`

error[E0277]: `?` couldn't convert the error to `ControllerError`
  --> src\controllers\users.rs:17:40
   |
17 |     let claims = get_current_user(&req)?;
   |                  ----------------------^ the trait `From<axum::http::StatusCode>` is not implemented for `ControllerError`
   |                  |
   |                  this can't be annotated with `?` because it has type `Result<_, axum::http::StatusCode>`
   |
   = note: the question mark operation (`?`) implicitly performs a conversion on the error value using the `From` trait
   = help: the following other types implement trait `From<T>`:
             `ControllerError` implements `From<mongodb::error::Error>`
             `ControllerError` implements `From<services::ServiceError>`
   = note: required for `Result<axum::Json<ApiResponse<models::user::UserProfile>>, ControllerError>` to implement 
`FromResidual<Result<Infallible, axum::http::StatusCode>>`

error[E0599]: no function or associated item named `new` found for struct `UserService` in the current scope
  --> src\controllers\users.rs:19:37
   |
19 |     let user_service = UserService::new(&state.database);
   |                                     ^^^ function or associated item not found in `UserService`
   |
  ::: src\services\user.rs:11:1
   |
11 | pub struct UserService {
   | ---------------------- function or associated item `new` not found for this struct
   |
   = help: items from traits can only be used if the trait is implemented and in scope
   = note: the following traits define an item `new`, perhaps you need to implement one of them:
           candidate #1: `ahash::HashMapExt`
           candidate #2: `ahash::HashSetExt`
           candidate #3: `bitvec::store::BitStore`
           candidate #4: `crypto_common::KeyInit`
           candidate #5: `crypto_common::KeyIvInit`
           candidate #6: `digest::VariableOutput`
           candidate #7: `digest::core_api::VariableOutputCore`
           candidate #8: `digest::digest::Digest`
           candidate #9: `digest::mac::Mac`
           candidate #10: `parking_lot_core::thread_parker::ThreadParkerT`
           candidate #11: `radium::Radium`
           candidate #12: `rand::distr::uniform::UniformSampler`
           candidate #13: `rand::distributions::uniform::UniformSampler`
           candidate #14: `ring::aead::BoundKey`
           candidate #15: `serde_with::duplicate_key_impls::error_on_duplicate::PreventDuplicateInsertsMap`
           candidate #16: `serde_with::duplicate_key_impls::error_on_duplicate::PreventDuplicateInsertsSet`
           candidate #17: `serde_with::duplicate_key_impls::first_value_wins::DuplicateInsertsFirstWinsMap`
           candidate #18: `serde_with::duplicate_key_impls::first_value_wins::DuplicateInsertsFirstWinsSet`
           candidate #19: `serde_with::duplicate_key_impls::last_value_wins::DuplicateInsertsLastWinsSet`
           candidate #20: `trust_dns_proto::Executor`
           candidate #21: `typenum::marker_traits::Bit`

error[E0277]: `?` couldn't convert the error to `ControllerError`
  --> src\controllers\users.rs:31:40
   |
31 |     let claims = get_current_user(&req)?;
   |                  ----------------------^ the trait `From<axum::http::StatusCode>` is not implemented for `ControllerError`
   |                  |
   |                  this can't be annotated with `?` because it has type `Result<_, axum::http::StatusCode>`
   |
   = note: the question mark operation (`?`) implicitly performs a conversion on the error value using the `From` trait
   = help: the following other types implement trait `From<T>`:
             `ControllerError` implements `From<mongodb::error::Error>`
             `ControllerError` implements `From<services::ServiceError>`
   = note: required for `Result<axum::Json<ApiResponse<models::user::UserProfile>>, ControllerError>` to implement 
`FromResidual<Result<Infallible, axum::http::StatusCode>>`

error[E0599]: no function or associated item named `new` found for struct `UserService` in the current scope
  --> src\controllers\users.rs:33:37
   |
33 |     let user_service = UserService::new(&state.database);
   |                                     ^^^ function or associated item not found in `UserService`
   |
  ::: src\services\user.rs:11:1
   |
11 | pub struct UserService {
   | ---------------------- function or associated item `new` not found for this struct
   |
   = help: items from traits can only be used if the trait is implemented and in scope
   = note: the following traits define an item `new`, perhaps you need to implement one of them:
           candidate #1: `ahash::HashMapExt`
           candidate #2: `ahash::HashSetExt`
           candidate #3: `bitvec::store::BitStore`
           candidate #4: `crypto_common::KeyInit`
           candidate #5: `crypto_common::KeyIvInit`
           candidate #6: `digest::VariableOutput`
           candidate #7: `digest::core_api::VariableOutputCore`
           candidate #8: `digest::digest::Digest`
           candidate #9: `digest::mac::Mac`
           candidate #10: `parking_lot_core::thread_parker::ThreadParkerT`
           candidate #11: `radium::Radium`
           candidate #12: `rand::distr::uniform::UniformSampler`
           candidate #13: `rand::distributions::uniform::UniformSampler`
           candidate #14: `ring::aead::BoundKey`
           candidate #15: `serde_with::duplicate_key_impls::error_on_duplicate::PreventDuplicateInsertsMap`
           candidate #16: `serde_with::duplicate_key_impls::error_on_duplicate::PreventDuplicateInsertsSet`
           candidate #17: `serde_with::duplicate_key_impls::first_value_wins::DuplicateInsertsFirstWinsMap`
           candidate #18: `serde_with::duplicate_key_impls::first_value_wins::DuplicateInsertsFirstWinsSet`
           candidate #19: `serde_with::duplicate_key_impls::last_value_wins::DuplicateInsertsLastWinsSet`
           candidate #20: `trust_dns_proto::Executor`
           candidate #21: `typenum::marker_traits::Bit`

error[E0308]: mismatched types
   --> src\controllers\vultr.rs:178:17
    |
178 |             id: os.id,
    |                 ^^^^^- help: try using a conversion method: `.to_string()`
    |                 |
    |                 expected `String`, found `u32`

error[E0308]: mismatched types
   --> src\controllers\vultr.rs:225:25
    |
225 |     Ok(success_response(bare_metals))
    |        ---------------- ^^^^^^^^^^^ expected `models::instance::VultrBareMetal`, found `vultr::models::VultrBareMetal`
    |        |
    |        arguments to this function are incorrect
    |
    = note: `vultr::models::VultrBareMetal` and `models::instance::VultrBareMetal` have similar names, but are actually distinct types
note: `vultr::models::VultrBareMetal` is defined in module `crate::vultr::models` of the current crate
   --> src\vultr\models.rs:234:1
    |
234 | pub struct VultrBareMetal {
    | ^^^^^^^^^^^^^^^^^^^^^^^^^
note: `models::instance::VultrBareMetal` is defined in module `crate::models::instance` of the current crate
   --> src\models\instance.rs:131:1
    |
131 | pub struct VultrBareMetal {
    | ^^^^^^^^^^^^^^^^^^^^^^^^^
help: the return type of this call is `Vec<vultr::models::VultrBareMetal>` due to the type of the argument passed
   --> src\controllers\vultr.rs:225:8
    |
225 |     Ok(success_response(bare_metals))
    |        ^^^^^^^^^^^^^^^^^-----------^
    |                         |
    |                         this argument influences the return type of `success_response`
note: function defined here
   --> src\controllers\mod.rs:76:8
    |
76  | pub fn success_response<T>(data: T) -> Json<ApiResponse<T>> {
    |        ^^^^^^^^^^^^^^^^    -------

error[E0308]: mismatched types
   --> src\controllers\vultr.rs:235:28
    |
235 |         .create_bare_metal(request)
    |          ----------------- ^^^^^^^ expected `vultr::models::CreateBareMetalRequest`, found `models::instance::CreateBareMetalRequest`
    |          |
    |          arguments to this method are incorrect
    |
    = note: `models::instance::CreateBareMetalRequest` and `vultr::models::CreateBareMetalRequest` have similar names, but are actually 
distinct types
note: `models::instance::CreateBareMetalRequest` is defined in module `crate::models::instance` of the current crate
   --> src\models\instance.rs:158:1
    |
158 | pub struct CreateBareMetalRequest {
    | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
note: `vultr::models::CreateBareMetalRequest` is defined in module `crate::vultr::models` of the current crate
   --> src\vultr\models.rs:267:1
    |
267 | pub struct CreateBareMetalRequest {
    | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
note: method defined here
   --> src\vultr\client.rs:90:18
    |
90  |     pub async fn create_bare_metal(&self, request: CreateBareMetalRequest) -> Result<VultrBareMetal> {
    |                  ^^^^^^^^^^^^^^^^^        -------------------------------

error[E0308]: mismatched types
   --> src\controllers\vultr.rs:239:25
    |
239 |     Ok(success_response(bare_metal))
    |        ---------------- ^^^^^^^^^^ expected `models::instance::VultrBareMetal`, found `vultr::models::VultrBareMetal`
    |        |
    |        arguments to this function are incorrect
    |
    = note: `vultr::models::VultrBareMetal` and `models::instance::VultrBareMetal` have similar names, but are actually distinct types
note: `vultr::models::VultrBareMetal` is defined in module `crate::vultr::models` of the current crate
   --> src\vultr\models.rs:234:1
    |
234 | pub struct VultrBareMetal {
    | ^^^^^^^^^^^^^^^^^^^^^^^^^
note: `models::instance::VultrBareMetal` is defined in module `crate::models::instance` of the current crate
   --> src\models\instance.rs:131:1
    |
131 | pub struct VultrBareMetal {
    | ^^^^^^^^^^^^^^^^^^^^^^^^^
help: the return type of this call is `vultr::models::VultrBareMetal` due to the type of the argument passed
   --> src\controllers\vultr.rs:239:8
    |
239 |     Ok(success_response(bare_metal))
    |        ^^^^^^^^^^^^^^^^^----------^
    |                         |
    |                         this argument influences the return type of `success_response`
note: function defined here
   --> src\controllers\mod.rs:76:8
    |
76  | pub fn success_response<T>(data: T) -> Json<ApiResponse<T>> {
    |        ^^^^^^^^^^^^^^^^    -------

error[E0308]: mismatched types
   --> src\controllers\vultr.rs:253:25
    |
253 |     Ok(success_response(bare_metal))
    |        ---------------- ^^^^^^^^^^ expected `models::instance::VultrBareMetal`, found `vultr::models::VultrBareMetal`
    |        |
    |        arguments to this function are incorrect
    |
    = note: `vultr::models::VultrBareMetal` and `models::instance::VultrBareMetal` have similar names, but are actually distinct types
note: `vultr::models::VultrBareMetal` is defined in module `crate::vultr::models` of the current crate
   --> src\vultr\models.rs:234:1
    |
234 | pub struct VultrBareMetal {
    | ^^^^^^^^^^^^^^^^^^^^^^^^^
note: `models::instance::VultrBareMetal` is defined in module `crate::models::instance` of the current crate
   --> src\models\instance.rs:131:1
    |
131 | pub struct VultrBareMetal {
    | ^^^^^^^^^^^^^^^^^^^^^^^^^
help: the return type of this call is `vultr::models::VultrBareMetal` due to the type of the argument passed
   --> src\controllers\vultr.rs:253:8
    |
253 |     Ok(success_response(bare_metal))
    |        ^^^^^^^^^^^^^^^^^----------^
    |                         |
    |                         this argument influences the return type of `success_response`
note: function defined here
   --> src\controllers\mod.rs:76:8
    |
76  | pub fn success_response<T>(data: T) -> Json<ApiResponse<T>> {
    |        ^^^^^^^^^^^^^^^^    -------

error[E0308]: mismatched types
   --> src\controllers\vultr.rs:264:43
    |
264 |         .update_bare_metal(&baremetal_id, request)
    |          -----------------                ^^^^^^^ expected `vultr::models::UpdateBareMetalRequest`, found 
`models::instance::UpdateBareMetalRequest`
    |          |
    |          arguments to this method are incorrect
    |
    = note: `models::instance::UpdateBareMetalRequest` and `vultr::models::UpdateBareMetalRequest` have similar names, but are actually 
distinct types
note: `models::instance::UpdateBareMetalRequest` is defined in module `crate::models::instance` of the current crate
   --> src\models\instance.rs:177:1
    |
177 | pub struct UpdateBareMetalRequest {
    | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
note: `vultr::models::UpdateBareMetalRequest` is defined in module `crate::vultr::models` of the current crate
   --> src\vultr\models.rs:286:1
    |
286 | pub struct UpdateBareMetalRequest {
    | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
note: method defined here
   --> src\vultr\client.rs:100:18
    |
100 |     pub async fn update_bare_metal(&self, baremetal_id: &str, request: UpdateBareMetalRequest) -> Result<VultrBareMetal> {
    |                  ^^^^^^^^^^^^^^^^^                            -------------------------------

error[E0308]: mismatched types
   --> src\controllers\vultr.rs:268:25
    |
268 |     Ok(success_response(bare_metal))
    |        ---------------- ^^^^^^^^^^ expected `models::instance::VultrBareMetal`, found `vultr::models::VultrBareMetal`
    |        |
    |        arguments to this function are incorrect
    |
    = note: `vultr::models::VultrBareMetal` and `models::instance::VultrBareMetal` have similar names, but are actually distinct types
note: `vultr::models::VultrBareMetal` is defined in module `crate::vultr::models` of the current crate
   --> src\vultr\models.rs:234:1
    |
234 | pub struct VultrBareMetal {
    | ^^^^^^^^^^^^^^^^^^^^^^^^^
note: `models::instance::VultrBareMetal` is defined in module `crate::models::instance` of the current crate
   --> src\models\instance.rs:131:1
    |
131 | pub struct VultrBareMetal {
    | ^^^^^^^^^^^^^^^^^^^^^^^^^
help: the return type of this call is `vultr::models::VultrBareMetal` due to the type of the argument passed
   --> src\controllers\vultr.rs:268:8
    |
268 |     Ok(success_response(bare_metal))
    |        ^^^^^^^^^^^^^^^^^----------^
    |                         |
    |                         this argument influences the return type of `success_response`
note: function defined here
   --> src\controllers\mod.rs:76:8
    |
76  | pub fn success_response<T>(data: T) -> Json<ApiResponse<T>> {
    |        ^^^^^^^^^^^^^^^^    -------

error[E0308]: mismatched types
   --> src\controllers\vultr.rs:296:25
    |
296 |     Ok(success_response(ipv4_info))
    |        ---------------- ^^^^^^^^^ expected `models::instance::BareMetalIpv4Info`, found `vultr::models::BareMetalIpv4Info`
    |        |
    |        arguments to this function are incorrect
    |
    = note: `vultr::models::BareMetalIpv4Info` and `models::instance::BareMetalIpv4Info` have similar names, but are actually distinct types
note: `vultr::models::BareMetalIpv4Info` is defined in module `crate::vultr::models` of the current crate
   --> src\vultr\models.rs:296:1
    |
296 | pub struct BareMetalIpv4Info {
    | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
note: `models::instance::BareMetalIpv4Info` is defined in module `crate::models::instance` of the current crate
   --> src\models\instance.rs:187:1
    |
187 | pub struct BareMetalIpv4Info {
    | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
help: the return type of this call is `Vec<vultr::models::BareMetalIpv4Info>` due to the type of the argument passed
   --> src\controllers\vultr.rs:296:8
    |
296 |     Ok(success_response(ipv4_info))
    |        ^^^^^^^^^^^^^^^^^---------^
    |                         |
    |                         this argument influences the return type of `success_response`
note: function defined here
   --> src\controllers\mod.rs:76:8
    |
76  | pub fn success_response<T>(data: T) -> Json<ApiResponse<T>> {
    |        ^^^^^^^^^^^^^^^^    -------

error[E0308]: mismatched types
   --> src\controllers\vultr.rs:310:25
    |
310 |     Ok(success_response(ipv6_info))
    |        ---------------- ^^^^^^^^^ expected `models::instance::BareMetalIpv6Info`, found `vultr::models::BareMetalIpv6Info`
    |        |
    |        arguments to this function are incorrect
    |
    = note: `vultr::models::BareMetalIpv6Info` and `models::instance::BareMetalIpv6Info` have similar names, but are actually distinct types
note: `vultr::models::BareMetalIpv6Info` is defined in module `crate::vultr::models` of the current crate
   --> src\vultr\models.rs:306:1
    |
306 | pub struct BareMetalIpv6Info {
    | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
note: `models::instance::BareMetalIpv6Info` is defined in module `crate::models::instance` of the current crate
   --> src\models\instance.rs:197:1
    |
197 | pub struct BareMetalIpv6Info {
    | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
help: the return type of this call is `Vec<vultr::models::BareMetalIpv6Info>` due to the type of the argument passed
   --> src\controllers\vultr.rs:310:8
    |
310 |     Ok(success_response(ipv6_info))
    |        ^^^^^^^^^^^^^^^^^---------^
    |                         |
    |                         this argument influences the return type of `success_response`
note: function defined here
   --> src\controllers\mod.rs:76:8
    |
76  | pub fn success_response<T>(data: T) -> Json<ApiResponse<T>> {
    |        ^^^^^^^^^^^^^^^^    -------

error[E0308]: mismatched types
   --> src\controllers\vultr.rs:324:25
    |
324 |     Ok(success_response(bandwidth))
    |        ---------------- ^^^^^^^^^ expected `models::instance::BareMetalBandwidth`, found `vultr::models::BareMetalBandwidth`
    |        |
    |        arguments to this function are incorrect
    |
    = note: `vultr::models::BareMetalBandwidth` and `models::instance::BareMetalBandwidth` have similar names, but are actually distinct types
note: `vultr::models::BareMetalBandwidth` is defined in module `crate::vultr::models` of the current crate
   --> src\vultr\models.rs:314:1
    |
314 | pub struct BareMetalBandwidth {
    | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
note: `models::instance::BareMetalBandwidth` is defined in module `crate::models::instance` of the current crate
   --> src\models\instance.rs:205:1
    |
205 | pub struct BareMetalBandwidth {
    | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
help: the return type of this call is `vultr::models::BareMetalBandwidth` due to the type of the argument passed
   --> src\controllers\vultr.rs:324:8
    |
324 |     Ok(success_response(bandwidth))
    |        ^^^^^^^^^^^^^^^^^---------^
    |                         |
    |                         this argument influences the return type of `success_response`
note: function defined here
   --> src\controllers\mod.rs:76:8
    |
76  | pub fn success_response<T>(data: T) -> Json<ApiResponse<T>> {
    |        ^^^^^^^^^^^^^^^^    -------

error[E0308]: mismatched types
   --> src\controllers\vultr.rs:338:25
    |
338 |     Ok(success_response(user_data))
    |        ---------------- ^^^^^^^^^ expected `models::instance::BareMetalUserData`, found `vultr::models::BareMetalUserData`
    |        |
    |        arguments to this function are incorrect
    |
    = note: `vultr::models::BareMetalUserData` and `models::instance::BareMetalUserData` have similar names, but are actually distinct types
note: `vultr::models::BareMetalUserData` is defined in module `crate::vultr::models` of the current crate
   --> src\vultr\models.rs:320:1
    |
320 | pub struct BareMetalUserData {
    | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
note: `models::instance::BareMetalUserData` is defined in module `crate::models::instance` of the current crate
   --> src\models\instance.rs:211:1
    |
211 | pub struct BareMetalUserData {
    | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
help: the return type of this call is `vultr::models::BareMetalUserData` due to the type of the argument passed
   --> src\controllers\vultr.rs:338:8
    |
338 |     Ok(success_response(user_data))
    |        ^^^^^^^^^^^^^^^^^---------^
    |                         |
    |                         this argument influences the return type of `success_response`
note: function defined here
   --> src\controllers\mod.rs:76:8
    |
76  | pub fn success_response<T>(data: T) -> Json<ApiResponse<T>> {
    |        ^^^^^^^^^^^^^^^^    -------

error[E0308]: mismatched types
   --> src\controllers\vultr.rs:352:25
    |
352 |     Ok(success_response(upgrades))
    |        ---------------- ^^^^^^^^ expected `models::instance::BareMetalUpgrades`, found `vultr::models::BareMetalUpgrades`
    |        |
    |        arguments to this function are incorrect
    |
    = note: `vultr::models::BareMetalUpgrades` and `models::instance::BareMetalUpgrades` have similar names, but are actually distinct types
note: `vultr::models::BareMetalUpgrades` is defined in module `crate::vultr::models` of the current crate
   --> src\vultr\models.rs:325:1
    |
325 | pub struct BareMetalUpgrades {
    | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
note: `models::instance::BareMetalUpgrades` is defined in module `crate::models::instance` of the current crate
   --> src\models\instance.rs:216:1
    |
216 | pub struct BareMetalUpgrades {
    | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
help: the return type of this call is `vultr::models::BareMetalUpgrades` due to the type of the argument passed
   --> src\controllers\vultr.rs:352:8
    |
352 |     Ok(success_response(upgrades))
    |        ^^^^^^^^^^^^^^^^^--------^
    |                         |
    |                         this argument influences the return type of `success_response`
note: function defined here
   --> src\controllers\mod.rs:76:8
    |
76  | pub fn success_response<T>(data: T) -> Json<ApiResponse<T>> {
    |        ^^^^^^^^^^^^^^^^    -------

error[E0308]: mismatched types
   --> src\controllers\vultr.rs:366:25
    |
366 |     Ok(success_response(vnc))
    |        ---------------- ^^^ expected `models::instance::BareMetalVncInfo`, found `vultr::models::BareMetalVncInfo`
    |        |
    |        arguments to this function are incorrect
    |
    = note: `vultr::models::BareMetalVncInfo` and `models::instance::BareMetalVncInfo` have similar names, but are actually distinct types
note: `vultr::models::BareMetalVncInfo` is defined in module `crate::vultr::models` of the current crate
   --> src\vultr\models.rs:331:1
    |
331 | pub struct BareMetalVncInfo {
    | ^^^^^^^^^^^^^^^^^^^^^^^^^^^
note: `models::instance::BareMetalVncInfo` is defined in module `crate::models::instance` of the current crate
   --> src\models\instance.rs:222:1
    |
222 | pub struct BareMetalVncInfo {
    | ^^^^^^^^^^^^^^^^^^^^^^^^^^^
help: the return type of this call is `vultr::models::BareMetalVncInfo` due to the type of the argument passed
   --> src\controllers\vultr.rs:366:8
    |
366 |     Ok(success_response(vnc))
    |        ^^^^^^^^^^^^^^^^^---^
    |                         |
    |                         this argument influences the return type of `success_response`
note: function defined here
   --> src\controllers\mod.rs:76:8
    |
76  | pub fn success_response<T>(data: T) -> Json<ApiResponse<T>> {
    |        ^^^^^^^^^^^^^^^^    -------

error[E0308]: mismatched types
   --> src\controllers\vultr.rs:380:25
    |
380 |     Ok(success_response(vpcs))
    |        ---------------- ^^^^ expected `models::instance::BareMetalVpcInfo`, found `vultr::models::BareMetalVpcInfo`
    |        |
    |        arguments to this function are incorrect
    |
    = note: `vultr::models::BareMetalVpcInfo` and `models::instance::BareMetalVpcInfo` have similar names, but are actually distinct types
note: `vultr::models::BareMetalVpcInfo` is defined in module `crate::vultr::models` of the current crate
   --> src\vultr\models.rs:336:1
    |
336 | pub struct BareMetalVpcInfo {
    | ^^^^^^^^^^^^^^^^^^^^^^^^^^^
note: `models::instance::BareMetalVpcInfo` is defined in module `crate::models::instance` of the current crate
   --> src\models\instance.rs:227:1
    |
227 | pub struct BareMetalVpcInfo {
    | ^^^^^^^^^^^^^^^^^^^^^^^^^^^
help: the return type of this call is `Vec<vultr::models::BareMetalVpcInfo>` due to the type of the argument passed
   --> src\controllers\vultr.rs:380:8
    |
380 |     Ok(success_response(vpcs))
    |        ^^^^^^^^^^^^^^^^^----^
    |                         |
    |                         this argument influences the return type of `success_response`
note: function defined here
   --> src\controllers\mod.rs:76:8
    |
76  | pub fn success_response<T>(data: T) -> Json<ApiResponse<T>> {
    |        ^^^^^^^^^^^^^^^^    -------

error[E0308]: mismatched types
   --> src\controllers\vultr.rs:450:25
    |
450 |     Ok(success_response(bare_metal))
    |        ---------------- ^^^^^^^^^^ expected `models::instance::VultrBareMetal`, found `vultr::models::VultrBareMetal`
    |        |
    |        arguments to this function are incorrect
    |
    = note: `vultr::models::VultrBareMetal` and `models::instance::VultrBareMetal` have similar names, but are actually distinct types
note: `vultr::models::VultrBareMetal` is defined in module `crate::vultr::models` of the current crate
   --> src\vultr\models.rs:234:1
    |
234 | pub struct VultrBareMetal {
    | ^^^^^^^^^^^^^^^^^^^^^^^^^
note: `models::instance::VultrBareMetal` is defined in module `crate::models::instance` of the current crate
   --> src\models\instance.rs:131:1
    |
131 | pub struct VultrBareMetal {
    | ^^^^^^^^^^^^^^^^^^^^^^^^^
help: the return type of this call is `vultr::models::VultrBareMetal` due to the type of the argument passed
   --> src\controllers\vultr.rs:450:8
    |
450 |     Ok(success_response(bare_metal))
    |        ^^^^^^^^^^^^^^^^^----------^
    |                         |
    |                         this argument influences the return type of `success_response`
note: function defined here
   --> src\controllers\mod.rs:76:8
    |
76  | pub fn success_response<T>(data: T) -> Json<ApiResponse<T>> {
    |        ^^^^^^^^^^^^^^^^    -------

error[E0308]: mismatched types
    --> src\controllers\vultr.rs:2793:25
     |
2793 |     Ok(success_response(os_list))
     |        ---------------- ^^^^^^^ expected `models::instance::VultrOS`, found `vultr::models::VultrOS`
     |        |
     |        arguments to this function are incorrect
     |
     = note: `vultr::models::VultrOS` and `models::instance::VultrOS` have similar names, but are actually distinct types
note: `vultr::models::VultrOS` is defined in module `crate::vultr::models` of the current crate
    --> src\vultr\models.rs:175:1
     |
175  | pub struct VultrOS {
     | ^^^^^^^^^^^^^^^^^^
note: `models::instance::VultrOS` is defined in module `crate::models::instance` of the current crate
    --> src\models\instance.rs:122:1
     |
122  | pub struct VultrOS {
     | ^^^^^^^^^^^^^^^^^^
help: the return type of this call is `Vec<vultr::models::VultrOS>` due to the type of the argument passed
    --> src\controllers\vultr.rs:2793:8
     |
2793 |     Ok(success_response(os_list))
     |        ^^^^^^^^^^^^^^^^^-------^
     |                         |
     |                         this argument influences the return type of `success_response`
note: function defined here
    --> src\controllers\mod.rs:76:8
     |
76   | pub fn success_response<T>(data: T) -> Json<ApiResponse<T>> {
     |        ^^^^^^^^^^^^^^^^    -------

error[E0308]: mismatched types
    --> src\controllers\vultr.rs:2807:25
     |
2807 |     Ok(success_response(plans))
     |        ---------------- ^^^^^ expected `models::instance::VultrPlan`, found `vultr::models::VultrPlan`
     |        |
     |        arguments to this function are incorrect
     |
     = note: `vultr::models::VultrPlan` and `models::instance::VultrPlan` have similar names, but are actually distinct types
note: `vultr::models::VultrPlan` is defined in module `crate::vultr::models` of the current crate
    --> src\vultr\models.rs:138:1
     |
138  | pub struct VultrPlan {
     | ^^^^^^^^^^^^^^^^^^^^
note: `models::instance::VultrPlan` is defined in module `crate::models::instance` of the current crate
    --> src\models\instance.rs:101:1
     |
101  | pub struct VultrPlan {
     | ^^^^^^^^^^^^^^^^^^^^
help: the return type of this call is `Vec<vultr::models::VultrPlan>` due to the type of the argument passed
    --> src\controllers\vultr.rs:2807:8
     |
2807 |     Ok(success_response(plans))
     |        ^^^^^^^^^^^^^^^^^-----^
     |                         |
     |                         this argument influences the return type of `success_response`
note: function defined here
    --> src\controllers\mod.rs:76:8
     |
76   | pub fn success_response<T>(data: T) -> Json<ApiResponse<T>> {
     |        ^^^^^^^^^^^^^^^^    -------

error[E0308]: mismatched types
    --> src\controllers\vultr.rs:3104:25
     |
3104 |     Ok(success_response(regions))
     |        ---------------- ^^^^^^^ expected `models::instance::VultrRegion`, found `vultr::models::VultrRegion`
     |        |
     |        arguments to this function are incorrect
     |
     = note: `vultr::models::VultrRegion` and `models::instance::VultrRegion` have similar names, but are actually distinct types
note: `vultr::models::VultrRegion` is defined in module `crate::vultr::models` of the current crate
    --> src\vultr\models.rs:159:1
     |
159  | pub struct VultrRegion {
     | ^^^^^^^^^^^^^^^^^^^^^^
note: `models::instance::VultrRegion` is defined in module `crate::models::instance` of the current crate
    --> src\models\instance.rs:113:1
     |
113  | pub struct VultrRegion {
     | ^^^^^^^^^^^^^^^^^^^^^^
help: the return type of this call is `Vec<vultr::models::VultrRegion>` due to the type of the argument passed
    --> src\controllers\vultr.rs:3104:8
     |
3104 |     Ok(success_response(regions))
     |        ^^^^^^^^^^^^^^^^^-------^
     |                         |
     |                         this argument influences the return type of `success_response`
note: function defined here
    --> src\controllers\mod.rs:76:8
     |
76   | pub fn success_response<T>(data: T) -> Json<ApiResponse<T>> {
     |        ^^^^^^^^^^^^^^^^    -------

error[E0308]: mismatched types
   --> src\middleware\mod.rs:25:29
    |
15  | pub async fn request_id_middleware<B>(
    |                                    - found this type parameter
...
25  |     let response = next.run(req).await;
    |                         --- ^^^ expected `Request<Body>`, found `Request<B>`
    |                         |
    |                         arguments to this method are incorrect
    |
    = note: expected struct `axum::http::Request<axum::body::Body>`
               found struct `axum::http::Request<B>`
note: method defined here
   --> C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\axum-0.7.9\src\middleware\from_fn.rs:341:18
    |
341 |     pub async fn run(mut self, req: Request) -> Response {
    |                  ^^^

error[E0308]: mismatched types
   --> src\middleware\mod.rs:38:29
    |
30  | pub async fn timing_middleware<B>(
    |                                - found this type parameter
...
38  |     let response = next.run(req).await;
    |                         --- ^^^ expected `Request<Body>`, found `Request<B>`
    |                         |
    |                         arguments to this method are incorrect
    |
    = note: expected struct `axum::http::Request<axum::body::Body>`
               found struct `axum::http::Request<B>`
note: method defined here
   --> C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\axum-0.7.9\src\middleware\from_fn.rs:341:18
    |
341 |     pub async fn run(mut self, req: Request) -> Response {
    |                  ^^^

error[E0308]: mismatched types
   --> src\middleware\mod.rs:69:29
    |
65  | pub async fn error_handling_middleware<B>(
    |                                        - found this type parameter
...
69  |     let response = next.run(req).await;
    |                         --- ^^^ expected `Request<Body>`, found `Request<B>`
    |                         |
    |                         arguments to this method are incorrect
    |
    = note: expected struct `axum::http::Request<axum::body::Body>`
               found struct `axum::http::Request<B>`
note: method defined here
   --> C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\axum-0.7.9\src\middleware\from_fn.rs:341:18
    |
341 |     pub async fn run(mut self, req: Request) -> Response {
    |                  ^^^

error[E0308]: mismatched types
   --> src\observability\mod.rs:102:29
    |
94  | pub async fn record_http_metrics<B>(
    |                                  - found this type parameter
...
102 |     let response = next.run(req).await;
    |                         --- ^^^ expected `Request<Body>`, found `Request<B>`
    |                         |
    |                         arguments to this method are incorrect
    |
    = note: expected struct `axum::http::Request<axum::body::Body>`
               found struct `axum::http::Request<B>`
note: method defined here
   --> C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\axum-0.7.9\src\middleware\from_fn.rs:341:18
    |
341 |     pub async fn run(mut self, req: Request) -> Response {
    |                  ^^^

error[E0599]: `impl Future<Output = Result<Cursor<Instance>, Error>>` is not an iterator
  --> src\services\instance.rs:53:14
   |
50 |           let instances: Vec<Instance> = self
   |  ________________________________________-
51 | |             .instances
52 | |             .find(doc! { "user_id": user_object_id }, None)
53 | |             .skip(pagination_info.offset())
   | |             -^^^^ `impl Future<Output = Result<Cursor<Instance>, Error>>` is not an iterator
   | |_____________|
   |
   |
   = note: the following trait bounds were not satisfied:
           `impl futures::Future<Output = Result<mongodb::Cursor<models::instance::Instance>, mongodb::error::Error>>: Iterator`
           which is required by `&mut impl futures::Future<Output = Result<mongodb::Cursor<models::instance::Instance>, 
mongodb::error::Error>>: Iterator`

warning: unused variable: `instance`
   --> src\services\instance.rs:210:13
    |
210 |         let instance = self
    |             ^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_instance`

error[E0308]: mismatched types
   --> src\utils\retry.rs:111:17
    |
110 |             .map_err(|e| match e {
    |                                - this expression has type `RetryError`
111 |                 backoff::Error::Permanent(err) => err,
    |                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ expected `RetryError`, found `Error<_>`
    |
    = note: expected enum `RetryError`
               found enum `backoff::Error<_>`

error[E0308]: mismatched types
   --> src\utils\retry.rs:112:17
    |
110 |             .map_err(|e| match e {
    |                                - this expression has type `RetryError`
111 |                 backoff::Error::Permanent(err) => err,
112 |                 backoff::Error::Transient { err, .. } => err,
    |                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ expected `RetryError`, found `Error<_>`
    |
    = note: expected enum `RetryError`
               found enum `backoff::Error<_>`

error[E0599]: no method named `json` found for opaque type `impl futures::Future<Output = utils::retry::RequestBuilder>` in the current scope
  --> src\vultr\mod.rs:90:47
   |
90 |         let response = self.client.post(&url).json(&request).send().await?;
   |                                               ^^^^ method not found in `impl Future<Output = RequestBuilder>`
   |
help: consider `await`ing on the `Future` and calling the method on its `Output`
   |
90 |         let response = self.client.post(&url).await.json(&request).send().await?;
   |                                               ++++++

error[E0599]: no method named `send` found for opaque type `impl futures::Future<Output = utils::retry::RequestBuilder>` in the current scope
   --> src\vultr\mod.rs:121:47
    |
121 |         let response = self.client.post(&url).send().await?;
    |                                               ^^^^ method not found in `impl Future<Output = RequestBuilder>`
    |
help: consider `await`ing on the `Future` and calling the method on its `Output`
    |
121 |         let response = self.client.post(&url).await.send().await?;
    |                                               ++++++

error[E0599]: no method named `send` found for opaque type `impl futures::Future<Output = utils::retry::RequestBuilder>` in the current scope
   --> src\vultr\mod.rs:136:47
    |
136 |         let response = self.client.post(&url).send().await?;
    |                                               ^^^^ method not found in `impl Future<Output = RequestBuilder>`
    |
help: consider `await`ing on the `Future` and calling the method on its `Output`
    |
136 |         let response = self.client.post(&url).await.send().await?;
    |                                               ++++++

error[E0599]: no method named `send` found for opaque type `impl futures::Future<Output = utils::retry::RequestBuilder>` in the current scope
   --> src\vultr\mod.rs:151:47
    |
151 |         let response = self.client.post(&url).send().await?;
    |                                               ^^^^ method not found in `impl Future<Output = RequestBuilder>`
    |
help: consider `await`ing on the `Future` and calling the method on its `Output`
    |
151 |         let response = self.client.post(&url).await.send().await?;
    |                                               ++++++

error[E0599]: no method named `json` found for opaque type `impl futures::Future<Output = utils::retry::RequestBuilder>` in the current scope
   --> src\vultr\mod.rs:250:47
    |
250 |         let response = self.client.post(&url).json(&request).send().await?;
    |                                               ^^^^ method not found in `impl Future<Output = RequestBuilder>`
    |
help: consider `await`ing on the `Future` and calling the method on its `Output`
    |
250 |         let response = self.client.post(&url).await.json(&request).send().await?;
    |                                               ++++++

error[E0308]: mismatched types
    --> src\vultr\mod.rs:1354:12
     |
1354 |         Ok(instances_response.instances)
     |         -- ^^^^^^^^^^^^^^^^^^^^^^^^^^^^ expected `Vec<VultrInstanceDetailed>`, found `Vec<VultrInstance>`
     |         |
     |         arguments to this enum variant are incorrect
     |
     = note: expected struct `Vec<vultr::models::VultrInstanceDetailed>`
                found struct `Vec<vultr::models::VultrInstance>`
help: the type constructed contains `Vec<vultr::models::VultrInstance>` due to the type of the argument passed
    --> src\vultr\mod.rs:1354:9
     |
1354 |         Ok(instances_response.instances)
     |         ^^^----------------------------^
     |            |
     |            this argument influences the type of `Ok`
note: tuple variant defined here
    --> C:\Users\<USER>\.rustup\toolchains\stable-x86_64-pc-windows-gnu\lib/rustlib/src/rust\library\core\src\result.rs:532:5
     |
532  |     Ok(#[stable(feature = "rust1", since = "1.0.0")] T),
     |     ^^

error[E0599]: no method named `create_bare_metal` found for struct `Arc<VultrApiClient>` in the current scope
  --> src\vultr\client.rs:91:25
   |
91 |         self.api_client.create_bare_metal(request).await
   |                         ^^^^^^^^^^^^^^^^^
   |
help: there is a method `get_bare_metal` with a similar name
   |
91 -         self.api_client.create_bare_metal(request).await
91 +         self.api_client.get_bare_metal(request).await
   |

error[E0599]: no method named `update_bare_metal` found for struct `Arc<VultrApiClient>` in the current scope
   --> src\vultr\client.rs:101:25
    |
101 |         self.api_client.update_bare_metal(baremetal_id, request).await
    |                         ^^^^^^^^^^^^^^^^^
    |
help: there is a method `get_bare_metal` with a similar name, but with different arguments
   --> src\vultr\mod.rs:220:5
    |
220 |     pub async fn get_bare_metal(&self, bare_metal_id: &str) -> Result<VultrBareMetal> {
    |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

error[E0599]: no method named `delete_bare_metal` found for struct `Arc<VultrApiClient>` in the current scope
   --> src\vultr\client.rs:106:25
    |
106 |         self.api_client.delete_bare_metal(baremetal_id).await
    |                         ^^^^^^^^^^^^^^^^^
    |
help: there is a method `get_bare_metal` with a similar name
    |
106 -         self.api_client.delete_bare_metal(baremetal_id).await
106 +         self.api_client.get_bare_metal(baremetal_id).await
    |

error[E0599]: no method named `get_bare_metal_ipv4` found for struct `Arc<VultrApiClient>` in the current scope
   --> src\vultr\client.rs:111:25
    |
111 |         self.api_client.get_bare_metal_ipv4(baremetal_id).await
    |                         ^^^^^^^^^^^^^^^^^^^
    |
help: there is a method `get_bare_metal` with a similar name
    |
111 -         self.api_client.get_bare_metal_ipv4(baremetal_id).await
111 +         self.api_client.get_bare_metal(baremetal_id).await
    |

error[E0599]: no method named `get_bare_metal_ipv6` found for struct `Arc<VultrApiClient>` in the current scope
   --> src\vultr\client.rs:116:25
    |
116 |         self.api_client.get_bare_metal_ipv6(baremetal_id).await
    |                         ^^^^^^^^^^^^^^^^^^^
    |
help: there is a method `get_bare_metal` with a similar name
    |
116 -         self.api_client.get_bare_metal_ipv6(baremetal_id).await
116 +         self.api_client.get_bare_metal(baremetal_id).await
    |

error[E0599]: no method named `create_bare_metal_ipv4_reverse` found for struct `Arc<VultrApiClient>` in the current scope
   --> src\vultr\client.rs:121:25
    |
121 |         self.api_client.create_bare_metal_ipv4_reverse(baremetal_id, ip, reverse).await
    |                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ method not found in `Arc<VultrApiClient>`

error[E0599]: no method named `create_bare_metal_ipv6_reverse` found for struct `Arc<VultrApiClient>` in the current scope
   --> src\vultr\client.rs:126:25
    |
126 |         self.api_client.create_bare_metal_ipv6_reverse(baremetal_id, ip, reverse).await
    |                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ method not found in `Arc<VultrApiClient>`

error[E0599]: no method named `reset_bare_metal_ipv4_reverse` found for struct `Arc<VultrApiClient>` in the current scope
   --> src\vultr\client.rs:131:25
    |
131 |         self.api_client.reset_bare_metal_ipv4_reverse(baremetal_id, ip).await
    |                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
help: there is a method `list_bare_metal` with a similar name, but with different arguments
   --> src\vultr\mod.rs:211:5
    |
211 |     pub async fn list_bare_metal(&self) -> Result<Vec<VultrBareMetal>> {
    |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

error[E0599]: no method named `delete_bare_metal_ipv6_reverse` found for struct `Arc<VultrApiClient>` in the current scope
   --> src\vultr\client.rs:136:25
    |
136 |         self.api_client.delete_bare_metal_ipv6_reverse(baremetal_id, ip).await
    |                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ method not found in `Arc<VultrApiClient>`

error[E0599]: no method named `start_bare_metal` found for struct `Arc<VultrApiClient>` in the current scope
   --> src\vultr\client.rs:141:25
    |
141 |         self.api_client.start_bare_metal(baremetal_id).await
    |                         ^^^^^^^^^^^^^^^^
    |
help: there is a method `get_bare_metal` with a similar name
    |
141 -         self.api_client.start_bare_metal(baremetal_id).await
141 +         self.api_client.get_bare_metal(baremetal_id).await
    |

error[E0599]: no method named `reboot_bare_metal` found for struct `Arc<VultrApiClient>` in the current scope
   --> src\vultr\client.rs:146:25
    |
146 |         self.api_client.reboot_bare_metal(baremetal_id).await
    |                         ^^^^^^^^^^^^^^^^^
    |
help: there is a method `get_bare_metal` with a similar name
    |
146 -         self.api_client.reboot_bare_metal(baremetal_id).await
146 +         self.api_client.get_bare_metal(baremetal_id).await
    |

error[E0599]: no method named `reinstall_bare_metal` found for struct `Arc<VultrApiClient>` in the current scope
   --> src\vultr\client.rs:151:25
    |
151 |         self.api_client.reinstall_bare_metal(baremetal_id).await
    |                         ^^^^^^^^^^^^^^^^^^^^
    |
help: there is a method `list_bare_metal` with a similar name, but with different arguments
   --> src\vultr\mod.rs:211:5
    |
211 |     pub async fn list_bare_metal(&self) -> Result<Vec<VultrBareMetal>> {
    |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

error[E0599]: no method named `halt_bare_metal` found for struct `Arc<VultrApiClient>` in the current scope
   --> src\vultr\client.rs:156:25
    |
156 |         self.api_client.halt_bare_metal(baremetal_id).await
    |                         ^^^^^^^^^^^^^^^
    |
help: there is a method `get_bare_metal` with a similar name
    |
156 -         self.api_client.halt_bare_metal(baremetal_id).await
156 +         self.api_client.get_bare_metal(baremetal_id).await
    |

error[E0599]: no method named `get_bare_metal_bandwidth` found for struct `Arc<VultrApiClient>` in the current scope
   --> src\vultr\client.rs:161:25
    |
161 |         self.api_client.get_bare_metal_bandwidth(baremetal_id).await
    |                         ^^^^^^^^^^^^^^^^^^^^^^^^
    |
help: there is a method `get_bare_metal` with a similar name
    |
161 -         self.api_client.get_bare_metal_bandwidth(baremetal_id).await
161 +         self.api_client.get_bare_metal(baremetal_id).await
    |

error[E0599]: no method named `halt_bare_metals` found for struct `Arc<VultrApiClient>` in the current scope
   --> src\vultr\client.rs:166:25
    |
166 |         self.api_client.halt_bare_metals(instance_ids).await
    |                         ^^^^^^^^^^^^^^^^
    |
help: there is a method `get_bare_metal` with a similar name
    |
166 -         self.api_client.halt_bare_metals(instance_ids).await
166 +         self.api_client.get_bare_metal(instance_ids).await
    |

error[E0599]: no method named `reboot_bare_metals` found for struct `Arc<VultrApiClient>` in the current scope
   --> src\vultr\client.rs:171:25
    |
171 |         self.api_client.reboot_bare_metals(instance_ids).await
    |                         ^^^^^^^^^^^^^^^^^^
    |
help: there is a method `get_bare_metal` with a similar name
    |
171 -         self.api_client.reboot_bare_metals(instance_ids).await
171 +         self.api_client.get_bare_metal(instance_ids).await
    |

error[E0599]: no method named `start_bare_metals` found for struct `Arc<VultrApiClient>` in the current scope
   --> src\vultr\client.rs:176:25
    |
176 |         self.api_client.start_bare_metals(instance_ids).await
    |                         ^^^^^^^^^^^^^^^^^
    |
help: there is a method `get_bare_metal` with a similar name
    |
176 -         self.api_client.start_bare_metals(instance_ids).await
176 +         self.api_client.get_bare_metal(instance_ids).await
    |

error[E0599]: no method named `get_bare_metal_user_data` found for struct `Arc<VultrApiClient>` in the current scope
   --> src\vultr\client.rs:181:25
    |
181 |         self.api_client.get_bare_metal_user_data(baremetal_id).await
    |                         ^^^^^^^^^^^^^^^^^^^^^^^^
    |
help: there is a method `get_bare_metal` with a similar name
    |
181 -         self.api_client.get_bare_metal_user_data(baremetal_id).await
181 +         self.api_client.get_bare_metal(baremetal_id).await
    |

error[E0599]: no method named `get_bare_metal_upgrades` found for struct `Arc<VultrApiClient>` in the current scope
   --> src\vultr\client.rs:186:25
    |
186 |         self.api_client.get_bare_metal_upgrades(baremetal_id).await
    |                         ^^^^^^^^^^^^^^^^^^^^^^^
    |
help: there is a method `get_bare_metal` with a similar name
    |
186 -         self.api_client.get_bare_metal_upgrades(baremetal_id).await
186 +         self.api_client.get_bare_metal(baremetal_id).await
    |

error[E0599]: no method named `get_bare_metal_vnc` found for struct `Arc<VultrApiClient>` in the current scope
   --> src\vultr\client.rs:191:25
    |
191 |         self.api_client.get_bare_metal_vnc(baremetal_id).await
    |                         ^^^^^^^^^^^^^^^^^^
    |
help: there is a method `get_bare_metal` with a similar name
    |
191 -         self.api_client.get_bare_metal_vnc(baremetal_id).await
191 +         self.api_client.get_bare_metal(baremetal_id).await
    |

error[E0599]: no method named `attach_bare_metal_vpc` found for struct `Arc<VultrApiClient>` in the current scope
   --> src\vultr\client.rs:196:25
    |
196 |         self.api_client.attach_bare_metal_vpc(baremetal_id, vpc_id).await
    |                         ^^^^^^^^^^^^^^^^^^^^^
    |
help: there is a method `get_bare_metal` with a similar name, but with different arguments
   --> src\vultr\mod.rs:220:5
    |
220 |     pub async fn get_bare_metal(&self, bare_metal_id: &str) -> Result<VultrBareMetal> {
    |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

error[E0599]: no method named `detach_bare_metal_vpc` found for struct `Arc<VultrApiClient>` in the current scope
   --> src\vultr\client.rs:201:25
    |
201 |         self.api_client.detach_bare_metal_vpc(baremetal_id, vpc_id).await
    |                         ^^^^^^^^^^^^^^^^^^^^^
    |
help: there is a method `get_bare_metal` with a similar name, but with different arguments
   --> src\vultr\mod.rs:220:5
    |
220 |     pub async fn get_bare_metal(&self, bare_metal_id: &str) -> Result<VultrBareMetal> {
    |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

error[E0599]: no method named `list_bare_metal_vpcs` found for struct `Arc<VultrApiClient>` in the current scope
   --> src\vultr\client.rs:206:25
    |
206 |         self.api_client.list_bare_metal_vpcs(baremetal_id).await
    |                         ^^^^^^^^^^^^^^^^^^^^
    |
help: there is a method `list_bare_metal` with a similar name, but with different arguments
   --> src\vultr\mod.rs:211:5
    |
211 |     pub async fn list_bare_metal(&self) -> Result<Vec<VultrBareMetal>> {
    |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

error[E0277]: the trait bound `fn(State<Arc<...>>, ..., ...) -> ... {update_profile}: Handler<_, _>` is not satisfied
   --> src\routes\mod.rs:33:38
    |
33  |         .route("/users/profile", put(controllers::users::update_profile))
    |                                  --- ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ the trait `Handler<_, _>` is not implemented for fn item 
`fn(State<Arc<AppState>>, Request<Body>, ...) -> ... {update_profile}`
    |                                  |
    |                                  required by a bound introduced by this call
    |
    = note: Consider using `#[axum::debug_handler]` to improve the error message
    = help: the following other types implement trait `Handler<T, S>`:
              `MethodRouter<S>` implements `Handler<(), S>`
              `axum::handler::Layered<L, H, T, S>` implements `Handler<T, S>`
note: required by a bound in `put`
   --> C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\axum-0.7.9\src\routing\method_routing.rs:444:1
    |
444 | top_level_handler_fn!(put, PUT);
    | ^^^^^^^^^^^^^^^^^^^^^^---^^^^^^
    | |                     |
    | |                     required by a bound in this function
    | required by this bound in `put`
    = note: the full name for the type has been written to 
'D:\workspace\.rust\achidas\target\debug\deps\achidas-4d377389ccdf1153.long-type-15154906199334923306.txt'
    = note: consider using `--verbose` to print the full type name to the console
    = note: this error originates in the macro `top_level_handler_fn` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0277]: the trait bound `fn(State<Arc<...>>, ..., ...) -> ... {create_instance}: Handler<_, _>` is not satisfied
   --> src\routes\mod.rs:39:35
    |
39  |         .route("/instances", post(controllers::instances::create_instance))
    |                              ---- ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ the trait `Handler<_, _>` is not implemented for fn item 
`fn(State<Arc<AppState>>, Request<Body>, ...) -> ... {create_instance}`
    |                              |
    |                              required by a bound introduced by this call
    |
    = note: Consider using `#[axum::debug_handler]` to improve the error message
    = help: the following other types implement trait `Handler<T, S>`:
              `MethodRouter<S>` implements `Handler<(), S>`
              `axum::handler::Layered<L, H, T, S>` implements `Handler<T, S>`
note: required by a bound in `post`
   --> C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\axum-0.7.9\src\routing\method_routing.rs:443:1
    |
443 | top_level_handler_fn!(post, POST);
    | ^^^^^^^^^^^^^^^^^^^^^^----^^^^^^^
    | |                     |
    | |                     required by a bound in this function
    | required by this bound in `post`
    = note: the full name for the type has been written to 
'D:\workspace\.rust\achidas\target\debug\deps\achidas-4d377389ccdf1153.long-type-9856572865851886122.txt'
    = note: consider using `--verbose` to print the full type name to the console
    = note: this error originates in the macro `top_level_handler_fn` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0277]: the trait bound `fn(State<...>, ..., ..., ...) -> ... {update_instance}: Handler<_, _>` is not satisfied
   --> src\routes\mod.rs:41:38
    |
41  |         .route("/instances/:id", put(controllers::instances::update_instance))
    |                                  --- ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ the trait `Handler<_, _>` is not implemented for fn item 
`fn(State<Arc<AppState>>, Path<...>, ..., ...) -> ... {update_instance}`
    |                                  |
    |                                  required by a bound introduced by this call
    |
    = note: Consider using `#[axum::debug_handler]` to improve the error message
    = help: the following other types implement trait `Handler<T, S>`:
              `MethodRouter<S>` implements `Handler<(), S>`
              `axum::handler::Layered<L, H, T, S>` implements `Handler<T, S>`
note: required by a bound in `put`
   --> C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\axum-0.7.9\src\routing\method_routing.rs:444:1
    |
444 | top_level_handler_fn!(put, PUT);
    | ^^^^^^^^^^^^^^^^^^^^^^---^^^^^^
    | |                     |
    | |                     required by a bound in this function
    | required by this bound in `put`
    = note: the full name for the type has been written to 
'D:\workspace\.rust\achidas\target\debug\deps\achidas-4d377389ccdf1153.long-type-6413777456787985394.txt'
    = note: consider using `--verbose` to print the full type name to the console
    = note: this error originates in the macro `top_level_handler_fn` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0277]: the trait bound `FromFn<..., (), ..., _>: Service<...>` is not satisfied
   --> src\routes\mod.rs:368:16
    |
368 |         .layer(middleware::from_fn(auth_middleware))
    |          ----- ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ unsatisfied trait bound
    |          |
    |          required by a bound introduced by this call
    |
    = help: the trait `Service<axum::http::Request<axum::body::Body>>` is not implemented for `FromFn<fn(..., ..., ..., ...) -> ... 
{auth_middleware}, (), ..., _>`
    = help: the following other types implement trait `Service<Request>`:
              axum::middleware::FromFn<F, S, I, (T1, T2)>
              axum::middleware::FromFn<F, S, I, (T1, T2, T3)>
              axum::middleware::FromFn<F, S, I, (T1, T2, T3, T4)>
              axum::middleware::FromFn<F, S, I, (T1, T2, T3, T4, T5)>
              axum::middleware::FromFn<F, S, I, (T1, T2, T3, T4, T5, T6)>
              axum::middleware::FromFn<F, S, I, (T1, T2, T3, T4, T5, T6, T7)>
              axum::middleware::FromFn<F, S, I, (T1, T2, T3, T4, T5, T6, T7, T8)>
              axum::middleware::FromFn<F, S, I, (T1, T2, T3, T4, T5, T6, T7, T8, T9)>
            and 8 others
note: required by a bound in `Router::<S>::layer`
   --> C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\axum-0.7.9\src\routing\mod.rs:284:21
    |
281 |     pub fn layer<L>(self, layer: L) -> Router<S>
    |            ----- required by a bound in this associated function
...
284 |         L::Service: Service<Request> + Clone + Send + 'static,
    |                     ^^^^^^^^^^^^^^^^ required by this bound in `Router::<S>::layer`
    = note: the full name for the type has been written to 
'D:\workspace\.rust\achidas\target\debug\deps\achidas-4d377389ccdf1153.long-type-12244896466008011435.txt'
    = note: consider using `--verbose` to print the full type name to the console

error[E0277]: the trait bound `reqwest::header::HeaderName: From<K>` is not satisfied
   --> src\utils\retry.rs:137:51
    |
137 |             request_builder: self.request_builder.header(key, value),
    |                                                   ^^^^^^ the trait `From<K>` is not implemented for `reqwest::header::HeaderName`
    |
    = note: required for `K` to implement `Into<reqwest::header::HeaderName>`
    = note: required for `reqwest::header::HeaderName` to implement `TryFrom<K>`
help: consider introducing a `where` clause, but there might be an alternative better way to express this requirement
    |
122 | impl RequestBuilder where reqwest::header::HeaderName: From<K> {
    |                     ++++++++++++++++++++++++++++++++++++++++++

error[E0277]: the trait bound `reqwest::header::HeaderValue: From<V>` is not satisfied
   --> src\utils\retry.rs:137:51
    |
137 |             request_builder: self.request_builder.header(key, value),
    |                                                   ^^^^^^ the trait `From<V>` is not implemented for `reqwest::header::HeaderValue`
    |
    = note: required for `V` to implement `Into<reqwest::header::HeaderValue>`
    = note: required for `reqwest::header::HeaderValue` to implement `TryFrom<V>`
help: consider introducing a `where` clause, but there might be an alternative better way to express this requirement
    |
122 | impl RequestBuilder where reqwest::header::HeaderValue: From<V> {
    |                     +++++++++++++++++++++++++++++++++++++++++++

warning: use of deprecated method `chrono::DateTime::<Tz>::timestamp_nanos`: use `timestamp_nanos_opt()` instead
  --> src\utils\mod.rs:40:9
   |
40 |     now.timestamp_nanos().hash(&mut hasher);
   |         ^^^^^^^^^^^^^^^
   |
   = note: `#[warn(deprecated)]` on by default

error[E0308]: mismatched types
    --> src\vultr\models.rs:1125:35
     |
1125 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                                   ^^^^^^^^^^^ expected `Vec<VultrInstance>`, found `Vec<VultrInstanceDetailed>`
     |
     = note: expected struct `Vec<vultr::models::VultrInstance>`
                found struct `Vec<vultr::models::VultrInstanceDetailed>`
     = note: this error originates in the derive macro `Deserialize` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0609]: no field `sticky_session` on type `&vultr::models::VultrLoadBalancerGenericInfo`
    --> src\vultr\models.rs:1561:9
     |
1561 |     pub sticky_session: Option<VultrStickySession>,
     |         ^^^^^^^^^^^^^^ unknown field
     |
help: a field with a similar name exists
     |
1561 |     pub sticky_sessions: Option<VultrStickySession>,
     |                       +

error[E0560]: struct `vultr::models::VultrLoadBalancerGenericInfo` has no field named `sticky_session`
    --> src\vultr\models.rs:1561:5
     |
1561 |     pub sticky_session: Option<VultrStickySession>,
     |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ `vultr::models::VultrLoadBalancerGenericInfo` does not have this field
     |
     = note: all struct fields are already assigned

error[E0560]: struct `vultr::models::VultrLoadBalancerGenericInfo` has no field named `sticky_session`
    --> src\vultr\models.rs:1561:9
     |
1561 |     pub sticky_session: Option<VultrStickySession>,
     |         ^^^^^^^^^^^^^^ `vultr::models::VultrLoadBalancerGenericInfo` does not have this field
     |
     = note: all struct fields are already assigned

error[E0308]: mismatched types
    --> src\vultr\models.rs:1570:35
     |
1570 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                                   ^^^^^^^^^^^ expected `String`, found `Option<String>`
     |
     = note: expected struct `std::string::String`
                  found enum `std::option::Option<std::string::String>`
     = note: this error originates in the derive macro `Deserialize` (in Nightly builds, run with -Z macro-backtrace for more info)
help: consider using `Option::expect` to unwrap the `std::option::Option<std::string::String>` value, panicking if the value is an 
`Option::None`
     |
1570 | #[derive(Debug, Clone, Serialize, Deserialize.expect("REASON"))]
     |                                              +++++++++++++++++

error[E0609]: no field `ferretdb_credentials` on type `&vultr::models::VultrManagedDatabase`
    --> src\vultr\models.rs:1680:9
     |
1680 |     pub ferretdb_credentials: Option<VultrFerretDBCredentials>,
     |         ^^^^^^^^^^^^^^^^^^^^ unknown field
     |
     = note: available fields are: `id`, `date_created`, `plan`, `plan_disk`, `plan_ram` ... and 24 others

error[E0609]: no field `cluster_time_zone` on type `&vultr::models::VultrManagedDatabase`
    --> src\vultr\models.rs:1695:9
     |
1695 |     pub cluster_time_zone: Option<String>,
     |         ^^^^^^^^^^^^^^^^^ unknown field
     |
     = note: available fields are: `id`, `date_created`, `plan`, `plan_disk`, `plan_ram` ... and 24 others

error[E0609]: no field `read_replicas` on type `&vultr::models::VultrManagedDatabase`
    --> src\vultr\models.rs:1696:9
     |
1696 |     pub read_replicas: Vec<VultrDatabaseReadReplica>,
     |         ^^^^^^^^^^^^^ unknown field
     |
help: a field with a similar name exists
     |
1696 -     pub read_replicas: Vec<VultrDatabaseReadReplica>,
1696 +     pub plan_replicas: Vec<VultrDatabaseReadReplica>,
     |

error[E0560]: struct `vultr::models::VultrManagedDatabase` has no field named `ferretdb_credentials`
    --> src\vultr\models.rs:1680:5
     |
1680 |     pub ferretdb_credentials: Option<VultrFerretDBCredentials>,
     |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ `vultr::models::VultrManagedDatabase` does not have this field
     |
     = note: all struct fields are already assigned

error[E0560]: struct `vultr::models::VultrManagedDatabase` has no field named `cluster_time_zone`
    --> src\vultr\models.rs:1695:5
     |
1695 |     pub cluster_time_zone: Option<String>,
     |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ `vultr::models::VultrManagedDatabase` does not have this field
     |
     = note: all struct fields are already assigned

error[E0560]: struct `vultr::models::VultrManagedDatabase` has no field named `read_replicas`
    --> src\vultr\models.rs:1696:5
     |
1696 |     pub read_replicas: Vec<VultrDatabaseReadReplica>,
     |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ `vultr::models::VultrManagedDatabase` does not have this field
     |
     = note: all struct fields are already assigned

error[E0560]: struct `vultr::models::VultrManagedDatabase` has no field named `ferretdb_credentials`
    --> src\vultr\models.rs:1680:9
     |
1680 |     pub ferretdb_credentials: Option<VultrFerretDBCredentials>,
     |         ^^^^^^^^^^^^^^^^^^^^ `vultr::models::VultrManagedDatabase` does not have this field
     |
     = note: all struct fields are already assigned

error[E0308]: mismatched types
    --> src\vultr\models.rs:1663:35
     |
1663 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                                   ^^^^^^^^^^^ expected `Option<u32>`, found `Option<f64>`
     |
     = note: expected enum `std::option::Option<u32>`
                found enum `std::option::Option<f64>`
     = note: this error originates in the derive macro `Deserialize` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0560]: struct `vultr::models::VultrManagedDatabase` has no field named `cluster_time_zone`
    --> src\vultr\models.rs:1695:9
     |
1695 |     pub cluster_time_zone: Option<String>,
     |         ^^^^^^^^^^^^^^^^^ `vultr::models::VultrManagedDatabase` does not have this field
     |
     = note: all struct fields are already assigned

error[E0560]: struct `vultr::models::VultrManagedDatabase` has no field named `read_replicas`
    --> src\vultr\models.rs:1696:9
     |
1696 |     pub read_replicas: Vec<VultrDatabaseReadReplica>,
     |         ^^^^^^^^^^^^^ `vultr::models::VultrManagedDatabase` does not have this field
     |
     = note: all struct fields are already assigned

error[E0308]: mismatched types
    --> src\vultr\models.rs:2227:35
     |
2227 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                                   ^^^^^^^^^^^ expected `Option<u32>`, found `u32`
     |
     = note: expected enum `std::option::Option<u32>`
                found type `u32`
     = note: this error originates in the derive macro `Deserialize` (in Nightly builds, run with -Z macro-backtrace for more info)
help: call `Into::into` on this expression to convert `u32` into `std::option::Option<u32>`
     |
2227 | #[derive(Debug, Clone, Serialize, Deserialize.into())]
     |                                              +++++++

error[E0308]: mismatched types
    --> src\vultr\models.rs:2664:35
     |
2664 | #[derive(Debug, Clone, Serialize, Deserialize)]
     |                                   ^^^^^^^^^^^ expected `u32`, found `Option<u32>`
     |
     = note: expected type `u32`
                found enum `std::option::Option<u32>`
     = note: this error originates in the derive macro `Deserialize` (in Nightly builds, run with -Z macro-backtrace for more info)
help: consider using `Option::expect` to unwrap the `std::option::Option<u32>` value, panicking if the value is an `Option::None`
     |
2664 | #[derive(Debug, Clone, Serialize, Deserialize.expect("REASON"))]
     |                                              +++++++++++++++++

error[E0609]: no field `port` on type `&vultr::models::VultrStorageGateway`
    --> src\vultr\models.rs:2765:9
     |
2765 |     pub port: u32,
     |         ^^^^ unknown field
     |
     = note: available fields are: `id`, `date_created`, `region`, `label`, `status` ... and 2 others

error[E0609]: no field `username` on type `&vultr::models::VultrStorageGateway`
    --> src\vultr\models.rs:2766:9
     |
2766 |     pub username: String,
     |         ^^^^^^^^ unknown field
     |
     = note: available fields are: `id`, `date_created`, `region`, `label`, `status` ... and 2 others

error[E0609]: no field `password` on type `&vultr::models::VultrStorageGateway`
    --> src\vultr\models.rs:2767:9
     |
2767 |     pub password: String,
     |         ^^^^^^^^ unknown field
     |
     = note: available fields are: `id`, `date_created`, `region`, `label`, `status` ... and 2 others

error[E0560]: struct `vultr::models::VultrStorageGateway` has no field named `port`
    --> src\vultr\models.rs:2765:5
     |
2765 |     pub port: u32,
     |     ^^^^^^^^^^^^^ `vultr::models::VultrStorageGateway` does not have this field
     |
     = note: all struct fields are already assigned

error[E0560]: struct `vultr::models::VultrStorageGateway` has no field named `username`
    --> src\vultr\models.rs:2766:5
     |
2766 |     pub username: String,
     |     ^^^^^^^^^^^^^^^^^^^^ `vultr::models::VultrStorageGateway` does not have this field
     |
     = note: all struct fields are already assigned

error[E0560]: struct `vultr::models::VultrStorageGateway` has no field named `password`
    --> src\vultr\models.rs:2767:5
     |
2767 |     pub password: String,
     |     ^^^^^^^^^^^^^^^^^^^^ `vultr::models::VultrStorageGateway` does not have this field
     |
     = note: all struct fields are already assigned

error[E0560]: struct `vultr::models::VultrStorageGateway` has no field named `port`
    --> src\vultr\models.rs:2765:9
     |
2765 |     pub port: u32,
     |         ^^^^ `vultr::models::VultrStorageGateway` does not have this field
     |
     = note: all struct fields are already assigned

error[E0560]: struct `vultr::models::VultrStorageGateway` has no field named `username`
    --> src\vultr\models.rs:2766:9
     |
2766 |     pub username: String,
     |         ^^^^^^^^ `vultr::models::VultrStorageGateway` does not have this field
     |
     = note: all struct fields are already assigned

error[E0560]: struct `vultr::models::VultrStorageGateway` has no field named `password`
    --> src\vultr\models.rs:2767:9
     |
2767 |     pub password: String,
     |         ^^^^^^^^ `vultr::models::VultrStorageGateway` does not have this field
     |
     = note: all struct fields are already assigned

Some errors have detailed explanations: E0107, E0119, E0252, E0277, E0308, E0412, E0425, E0428, E0432...
For more information about an error, try `rustc --explain E0107`.
warning: `achidas` (lib) generated 21 warnings
error: could not compile `achidas` (lib) due to 298 previous errors; 21 warnings emitted
