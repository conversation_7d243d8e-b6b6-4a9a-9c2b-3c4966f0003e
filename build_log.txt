   Compiling achidas v0.1.0 (D:\workspace\.rust\achidas)
error: no rules expected `,`
   --> src\observability\mod.rs:114:5
    |
114 | /     metrics::histogram!("http_request_duration_seconds", duration.as_secs_f64(),
115 | |         "method" => method.to_string(),
116 | |         "path" => path,
117 | |         "status" => status.to_string()
118 | |     );
    | |_____^ no rules expected this token in macro call
    |
note: while trying to match `=>`
   --> C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\metrics-0.22.4\src\macros.rs:48:36
    |
48  |     ($name:expr, $($label_key:expr => $label_value:expr),*) => {{
    |                                    ^^
    = note: this error originates in the macro `$crate::histogram` which comes from the expansion of the macro `metrics::histogram` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0252]: the name `VultrOS` is defined multiple times
  --> src\controllers\vultr.rs:49:9
   |
6  |         UpdateBareMetalRequest, VultrOS, VultrPlan, VultrRegion, VultrBareMetal,
   |                                 ------- previous import of the type `VultrOS` here
...
49 |         VultrOS, VultrPlan, VultrMetalPlan, VultrInference, CreateInferenceRequest,
   |         ^^^^^^^ `VultrOS` reimported here
   |
   = note: `VultrOS` must be defined only once in the type namespace of this module
help: you can use `as` to change the binding name of the import
   |
49 |         VultrOS as OtherVultrOS, VultrPlan, VultrMetalPlan, VultrInference, CreateInferenceRequest,
   |                 +++++++++++++++

error[E0252]: the name `VultrPlan` is defined multiple times
  --> src\controllers\vultr.rs:49:18
   |
6  |         UpdateBareMetalRequest, VultrOS, VultrPlan, VultrRegion, VultrBareMetal,
   |                                          --------- previous import of the type `VultrPlan` here
...
49 |         VultrOS, VultrPlan, VultrMetalPlan, VultrInference, CreateInferenceRequest,
   |                  ^^^^^^^^^ `VultrPlan` reimported here
   |
   = note: `VultrPlan` must be defined only once in the type namespace of this module
help: you can use `as` to change the binding name of the import
   |
49 |         VultrOS, VultrPlan as OtherVultrPlan, VultrMetalPlan, VultrInference, CreateInferenceRequest,
   |                            +++++++++++++++++

error[E0252]: the name `VultrRegion` is defined multiple times
  --> src\controllers\vultr.rs:53:9
   |
6  |         UpdateBareMetalRequest, VultrOS, VultrPlan, VultrRegion, VultrBareMetal,
   |                                                     ----------- previous import of the type `VultrRegion` here
...
53 |         VultrRegion, VultrRegionAvailablePlans, VultrSnapshot, CreateSnapshotRequest,
   |         ^^^^^^^^^^^ `VultrRegion` reimported here
   |
   = note: `VultrRegion` must be defined only once in the type namespace of this module
help: you can use `as` to change the binding name of the import
   |
53 |         VultrRegion as OtherVultrRegion, VultrRegionAvailablePlans, VultrSnapshot, CreateSnapshotRequest,
   |                     +++++++++++++++++++

error[E0252]: the name `VultrSSHKey` is defined multiple times
  --> src\controllers\vultr.rs:55:34
   |
9  |         VultrSSHKey, VultrAccount, VultrAccountBGP, VultrAccountBandwidth,
   |         ----------- previous import of the type `VultrSSHKey` here
...
55 |         CreateSubaccountRequest, VultrSSHKey, CreateSSHKeyRequest, UpdateSSHKeyRequest,
   |                                  ^^^^^^^^^^^--
   |                                  |
   |                                  `VultrSSHKey` reimported here
   |                                  help: remove unnecessary import
   |
   = note: `VultrSSHKey` must be defined only once in the type namespace of this module

error[E0433]: failed to resolve: could not find `sdk` in `opentelemetry`
 --> src\observability\mod.rs:5:5
  |
5 |     sdk::{
  |     ^^^ could not find `sdk` in `opentelemetry`

error[E0432]: unresolved import `opentelemetry::sdk`
 --> src\observability\mod.rs:5:5
  |
5 |     sdk::{
  |     ^^^ could not find `sdk` in `opentelemetry`

error[E0432]: unresolved import `crate::services::admin`
 --> src\controllers\admin.rs:4:16
  |
4 |     services::{admin::AdminService, billing::BillingService},
  |                ^^^^^ could not find `admin` in `services`

error[E0433]: failed to resolve: could not find `register_counter` in `metrics`
  --> src\observability\mod.rs:81:14
   |
81 |     metrics::register_counter!("http_requests_total", "Total number of HTTP requests");
   |              ^^^^^^^^^^^^^^^^ could not find `register_counter` in `metrics`

error[E0433]: failed to resolve: could not find `register_histogram` in `metrics`
  --> src\observability\mod.rs:82:14
   |
82 |     metrics::register_histogram!("http_request_duration_seconds", "HTTP request duration in seconds");
   |              ^^^^^^^^^^^^^^^^^^ could not find `register_histogram` in `metrics`

error[E0433]: failed to resolve: could not find `register_gauge` in `metrics`
  --> src\observability\mod.rs:83:14
   |
83 |     metrics::register_gauge!("active_connections", "Number of active connections");
   |              ^^^^^^^^^^^^^^ could not find `register_gauge` in `metrics`

error[E0433]: failed to resolve: could not find `register_counter` in `metrics`
  --> src\observability\mod.rs:84:14
   |
84 |     metrics::register_counter!("vultr_api_calls_total", "Total number of Vultr API calls");
   |              ^^^^^^^^^^^^^^^^ could not find `register_counter` in `metrics`

error[E0433]: failed to resolve: could not find `register_counter` in `metrics`
  --> src\observability\mod.rs:85:14
   |
85 |     metrics::register_counter!("vultr_api_errors_total", "Total number of Vultr API errors");
   |              ^^^^^^^^^^^^^^^^ could not find `register_counter` in `metrics`

error[E0433]: failed to resolve: could not find `register_gauge` in `metrics`
  --> src\observability\mod.rs:86:14
   |
86 |     metrics::register_gauge!("active_instances", "Number of active instances");
   |              ^^^^^^^^^^^^^^ could not find `register_gauge` in `metrics`

error[E0433]: failed to resolve: could not find `register_counter` in `metrics`
  --> src\observability\mod.rs:87:14
   |
87 |     metrics::register_counter!("user_registrations_total", "Total number of user registrations");
   |              ^^^^^^^^^^^^^^^^ could not find `register_counter` in `metrics`

error[E0433]: failed to resolve: could not find `register_counter` in `metrics`
  --> src\observability\mod.rs:88:14
   |
88 |     metrics::register_counter!("user_logins_total", "Total number of user logins");
   |              ^^^^^^^^^^^^^^^^ could not find `register_counter` in `metrics`

error[E0433]: failed to resolve: could not find `increment_counter` in `metrics`
   --> src\observability\mod.rs:108:14
    |
108 |     metrics::increment_counter!("http_requests_total", 
    |              ^^^^^^^^^^^^^^^^^ could not find `increment_counter` in `metrics`

error[E0412]: cannot find type `HashMap` in this scope
    --> src\controllers\vultr.rs:1607:55
     |
1607 | ) -> ControllerResult<Json<crate::models::ApiResponse<HashMap<String, InstanceBandwidth>>>> {
     |                                                       ^^^^^^^ not found in this scope
     |
help: consider importing this struct
     |
1    + use std::collections::HashMap;
     |

error[E0433]: failed to resolve: use of unresolved module or unlinked crate `services`
  --> src\controllers\mod.rs:46:13
   |
46 |     Service(services::ServiceError),
   |             ^^^^^^^^ use of unresolved module or unlinked crate `services`
   |
   = help: if you wanted to use a crate named `services`, use `cargo add services` to add it to your `Cargo.toml`
help: consider importing this module
   |
9  + use crate::services;
   |

error[E0433]: failed to resolve: use of unresolved module or unlinked crate `services`
  --> src\controllers\mod.rs:96:11
   |
96 | impl From<services::ServiceError> for ControllerError {
   |           ^^^^^^^^ use of unresolved module or unlinked crate `services`
   |
   = help: if you wanted to use a crate named `services`, use `cargo add services` to add it to your `Cargo.toml`
help: consider importing this module
   |
9  + use crate::services;
   |

error[E0433]: failed to resolve: use of unresolved module or unlinked crate `services`
  --> src\controllers\mod.rs:97:18
   |
97 |     fn from(err: services::ServiceError) -> Self {
   |                  ^^^^^^^^ use of unresolved module or unlinked crate `services`
   |
   = help: if you wanted to use a crate named `services`, use `cargo add services` to add it to your `Cargo.toml`
help: consider importing this module
   |
9  + use crate::services;
   |

error[E0412]: cannot find type `Body` in this scope
  --> src\middleware\mod.rs:16:22
   |
16 |     mut req: Request<Body>,
   |                      ^^^^ not found in this scope
   |
help: consider importing one of these items
   |
6  + use axum::body::Body;
   |
6  + use hyper::body::Body;
   |
6  + use reqwest::Body;
   |

error[E0412]: cannot find type `Body` in this scope
  --> src\middleware\mod.rs:31:18
   |
31 |     req: Request<Body>,
   |                  ^^^^ not found in this scope
   |
help: consider importing one of these items
   |
6  + use axum::body::Body;
   |
6  + use hyper::body::Body;
   |
6  + use reqwest::Body;
   |

error[E0412]: cannot find type `Body` in this scope
  --> src\middleware\mod.rs:66:18
   |
66 |     req: Request<Body>,
   |                  ^^^^ not found in this scope
   |
help: consider importing one of these items
   |
6  + use axum::body::Body;
   |
6  + use hyper::body::Body;
   |
6  + use reqwest::Body;
   |

error[E0425]: cannot find value `create_ssh_key` in module `controllers::vultr`
   --> src\routes\mod.rs:318:60
    |
318 |         .route("/vultr/ssh-keys", post(controllers::vultr::create_ssh_key))
    |                                                            ^^^^^^^^^^^^^^ not found in `controllers::vultr`

error[E0425]: cannot find value `get_ssh_key` in module `controllers::vultr`
   --> src\routes\mod.rs:319:71
    |
319 |         .route("/vultr/ssh-keys/:ssh_key_id", get(controllers::vultr::get_ssh_key))
    |                                                                       ^^^^^^^^^^^ not found in `controllers::vultr`

error[E0425]: cannot find value `update_ssh_key` in module `controllers::vultr`
   --> src\routes\mod.rs:320:73
    |
320 |         .route("/vultr/ssh-keys/:ssh_key_id", patch(controllers::vultr::update_ssh_key))
    |                                                                         ^^^^^^^^^^^^^^ not found in `controllers::vultr`

error[E0425]: cannot find value `delete_ssh_key` in module `controllers::vultr`
   --> src\routes\mod.rs:321:74
    |
321 |         .route("/vultr/ssh-keys/:ssh_key_id", delete(controllers::vultr::delete_ssh_key))
    |                                                                          ^^^^^^^^^^^^^^ not found in `controllers::vultr`

error[E0425]: cannot find value `list_startup_scripts` in module `controllers::vultr`
   --> src\routes\mod.rs:324:66
    |
324 |         .route("/vultr/startup-scripts", get(controllers::vultr::list_startup_scripts))
    |                                                                  ^^^^^^^^^^^^^^^^^^^^ not found in `controllers::vultr`

error[E0425]: cannot find value `create_startup_script` in module `controllers::vultr`
   --> src\routes\mod.rs:325:67
    |
325 |         .route("/vultr/startup-scripts", post(controllers::vultr::create_startup_script))
    |                                                                   ^^^^^^^^^^^^^^^^^^^^^ not found in `controllers::vultr`

error[E0425]: cannot find value `get_startup_script` in module `controllers::vultr`
   --> src\routes\mod.rs:326:77
    |
326 |         .route("/vultr/startup-scripts/:script_id", get(controllers::vultr::get_startup_script))
    |                                                                             ^^^^^^^^^^^^^^^^^^ not found in `controllers::vultr`

error[E0425]: cannot find value `update_startup_script` in module `controllers::vultr`
   --> src\routes\mod.rs:327:79
    |
327 |         .route("/vultr/startup-scripts/:script_id", patch(controllers::vultr::update_startup_script))
    |                                                                               ^^^^^^^^^^^^^^^^^^^^^ not found in `controllers::vultr`

error[E0425]: cannot find value `delete_startup_script` in module `controllers::vultr`
   --> src\routes\mod.rs:328:80
    |
328 |         .route("/vultr/startup-scripts/:script_id", delete(controllers::vultr::delete_startup_script))
    |                                                                                ^^^^^^^^^^^^^^^^^^^^^ not found in `controllers::vultr`

error[E0425]: cannot find value `list_storage_gateways` in module `controllers::vultr`
   --> src\routes\mod.rs:331:67
    |
331 |         .route("/vultr/storage-gateways", get(controllers::vultr::list_storage_gateways))
    |                                                                   ^^^^^^^^^^^^^^^^^^^^^ not found in `controllers::vultr`

error[E0425]: cannot find value `create_storage_gateway` in module `controllers::vultr`
   --> src\routes\mod.rs:332:68
    |
332 |         .route("/vultr/storage-gateways", post(controllers::vultr::create_storage_gateway))
    |                                                                    ^^^^^^^^^^^^^^^^^^^^^^ not found in `controllers::vultr`

error[E0425]: cannot find value `get_storage_gateway` in module `controllers::vultr`
   --> src\routes\mod.rs:333:79
    |
333 |         .route("/vultr/storage-gateways/:gateway_id", get(controllers::vultr::get_storage_gateway))
    |                                                                               ^^^^^^^^^^^^^^^^^^^ not found in `controllers::vultr`

error[E0425]: cannot find value `update_storage_gateway` in module `controllers::vultr`
   --> src\routes\mod.rs:334:79
    |
334 |         .route("/vultr/storage-gateways/:gateway_id", put(controllers::vultr::update_storage_gateway))
    |                                                                               ^^^^^^^^^^^^^^^^^^^^^^ not found in `controllers::vultr`

error[E0425]: cannot find value `delete_storage_gateway` in module `controllers::vultr`
   --> src\routes\mod.rs:335:82
    |
335 |         .route("/vultr/storage-gateways/:gateway_id", delete(controllers::vultr::delete_storage_gateway))
    |                                                                                  ^^^^^^^^^^^^^^^^^^^^^^ not found in `controllers::vultr`

error[E0425]: cannot find value `add_storage_gateway_export` in module `controllers::vultr`
   --> src\routes\mod.rs:336:88
    |
336 |         .route("/vultr/storage-gateways/:gateway_id/exports", post(controllers::vultr::add_storage_gateway_export))
    |                                                                                        ^^^^^^^^^^^^^^^^^^^^^^^^^^ not found in `controllers::vultr`

error[E0425]: cannot find value `delete_storage_gateway_export` in module `controllers::vultr`
   --> src\routes\mod.rs:337:101
    |
337 |         .route("/vultr/storage-gateways/:gateway_id/exports/:export_id", delete(controllers::vultr::delete_storage_gateway_export))
    |                                                                                                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ not found in `controllers::vultr`

warning: unused import: `billing::BillingService`
 --> src\controllers\admin.rs:4:37
  |
4 |     services::{admin::AdminService, billing::BillingService},
  |                                     ^^^^^^^^^^^^^^^^^^^^^^^
  |
  = note: `#[warn(unused_imports)]` on by default

warning: unused imports: `AddDatabaseReadReplicaRequest`, `AddStorageGatewayExportRequest`, `BareMetalBandwidth`, `BareMetalIpv4Info`, `BareMetalIpv6Info`, `BareMetalUpgrades`, `BareMetalUserData`, `BareMetalVncInfo`, `BareMetalVpcInfo`, `CreateBareMetalRequest`, `CreateDatabaseConnectionPoolRequest`, `CreateDatabaseConnectorRequest`, `CreateDatabaseDBRequest`, `CreateDatabaseQuotaRequest`, `CreateDatabaseTopicRequest`, `CreateSSHKeyRequest`, `CreateStartupScriptRequest`, `CreateStorageGatewayRequest`, `CreateUserRequest`, `CreateVFSAttachmentRequest`, `CreateVFSRequest`, `ForkDatabaseRequest`, `RestoreDatabaseFromBackupRequest`, `StartDatabaseMigrationRequest`, `StartDatabaseVersionUpgradeRequest`, `UpdateBareMetalRequest`, `UpdateDatabaseConnectionPoolRequest`, `UpdateDatabaseConnectorRequest`, `UpdateDatabaseTopicRequest`, `UpdateSSHKeyRequest`, `UpdateStartupScriptRequest`, `UpdateStorageGatewayRequest`, `UpdateUserRequest`, `UpdateVFSRequest`, `VultrDatabaseAdvancedOptions`, `VultrDatabaseBackupInfo`, `VultrDatabaseConnectionPool`, `VultrDatabaseConnectorStatus`, `VultrDatabaseConnector`, `VultrDatabaseDB`, `VultrDatabaseMaintenanceUpdate`, `VultrDatabaseMigrationStatus`, `VultrDatabaseQuota`, `VultrDatabaseReadReplica`, `VultrDatabaseServiceAlert`, `VultrDatabaseTopic`, `VultrDatabaseVersions`, `VultrOS`, `VultrOS`, `VultrPlan`, `VultrPlan`, `VultrRegion`, `VultrRegion`, `VultrSSHKey`, `VultrStartupScript`, `VultrStorageGatewayExport`, `VultrStorageGateway`, `VultrUser`, `VultrVFSAttachment`, and `VultrVFS`
  --> src\controllers\vultr.rs:4:9
   |
4  |         BareMetalBandwidth, BareMetalIpv4Info, BareMetalIpv6Info, BareMetalUpgrades,
   |         ^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^
5  |         BareMetalUserData, BareMetalVncInfo, BareMetalVpcInfo, CreateBareMetalRequest,
   |         ^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^
6  |         UpdateBareMetalRequest, VultrOS, VultrPlan, VultrRegion, VultrBareMetal,
   |         ^^^^^^^^^^^^^^^^^^^^^^  ^^^^^^^  ^^^^^^^^^  ^^^^^^^^^^^
...
37 |         SetDatabaseUserACLRequest, VultrDatabaseDB, CreateDatabaseDBRequest,
   |                                    ^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^^
38 |         VultrDatabaseTopic, CreateDatabaseTopicRequest, UpdateDatabaseTopicRequest,
   |         ^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^^^^^
39 |         VultrDatabaseQuota, CreateDatabaseQuotaRequest, VultrDatabaseConnector,
   |         ^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^
40 |         CreateDatabaseConnectorRequest, UpdateDatabaseConnectorRequest,
   |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
41 |         VultrDatabaseConnectorStatus, VultrDatabaseMaintenanceUpdate,
   |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
42 |         VultrDatabaseServiceAlert, VultrDatabaseMigrationStatus, StartDatabaseMigrationRequest,
   |         ^^^^^^^^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
43 |         AddDatabaseReadReplicaRequest, VultrDatabaseReadReplica, VultrDatabaseBackupInfo,
   |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^^
44 |         RestoreDatabaseFromBackupRequest, ForkDatabaseRequest, VultrDatabaseConnectionPool,
   |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^^^^^^
45 |         CreateDatabaseConnectionPoolRequest, UpdateDatabaseConnectionPoolRequest,
   |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
46 |         VultrDatabaseAdvancedOptions, VultrDatabaseVersions, StartDatabaseVersionUpgradeRequest,
   |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
...
49 |         VultrOS, VultrPlan, VultrMetalPlan, VultrInference, CreateInferenceRequest,
   |         ^^^^^^^  ^^^^^^^^^
...
53 |         VultrRegion, VultrRegionAvailablePlans, VultrSnapshot, CreateSnapshotRequest,
   |         ^^^^^^^^^^^
54 |         CreateSnapshotFromUrlRequest, UpdateSnapshotRequest, VultrSubaccount,
55 |         CreateSubaccountRequest, VultrSSHKey, CreateSSHKeyRequest, UpdateSSHKeyRequest,
   |                                  ^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^
56 |         VultrStartupScript, CreateStartupScriptRequest, UpdateStartupScriptRequest,
   |         ^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^^^^^
57 |         VultrStorageGateway, CreateStorageGatewayRequest, UpdateStorageGatewayRequest,
   |         ^^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^^^^^^
58 |         VultrStorageGatewayExport, AddStorageGatewayExportRequest, VultrUser,
   |         ^^^^^^^^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^  ^^^^^^^^^
59 |         CreateUserRequest, UpdateUserRequest, VultrVFS, CreateVFSRequest,
   |         ^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^  ^^^^^^^^  ^^^^^^^^^^^^^^^^
60 |         UpdateVFSRequest, VultrVFSAttachment, CreateVFSAttachmentRequest,
   |         ^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `serde_json::json`
  --> src\controllers\mod.rs:15:5
   |
15 | use serde_json::json;
   |     ^^^^^^^^^^^^^^^^

warning: unused import: `error`
  --> src\middleware\auth.rs:14:15
   |
14 | use tracing::{error, warn};
   |               ^^^^^

warning: unused imports: `HeaderValue` and `response::Response`
 --> src\middleware\cors.rs:2:20
  |
2 |     http::{header, HeaderValue, Method},
  |                    ^^^^^^^^^^^
3 |     response::Response,
  |     ^^^^^^^^^^^^^^^^^^

warning: unused import: `StatusCode`
 --> src\middleware\mod.rs:7:21
  |
7 |     http::{Request, StatusCode},
  |                     ^^^^^^^^^^

warning: unused import: `bson::oid::ObjectId`
 --> src\models\mod.rs:1:5
  |
1 | use bson::oid::ObjectId;
  |     ^^^^^^^^^^^^^^^^^^^

warning: unused imports: `DateTime` and `Utc`
 --> src\models\mod.rs:2:14
  |
2 | use chrono::{DateTime, Utc};
  |              ^^^^^^^^  ^^^

warning: unused import: `uuid::Uuid`
 --> src\models\mod.rs:4:5
  |
4 | use uuid::Uuid;
  |     ^^^^^^^^^^

warning: unused import: `global`
 --> src\observability\mod.rs:4:5
  |
4 |     global,
  |     ^^^^^^

warning: unused import: `Level`
  --> src\observability\mod.rs:12:21
   |
12 | use tracing::{info, Level};
   |                     ^^^^^

warning: unused import: `error`
  --> src\services\auth.rs:15:15
   |
15 | use tracing::{error, info, instrument};
   |               ^^^^^

warning: unused import: `error`
  --> src\services\instance.rs:14:15
   |
14 | use tracing::{error, info, instrument};
   |               ^^^^^

warning: unused import: `DateTime`
 --> src\utils\mod.rs:3:14
  |
3 | use chrono::{DateTime, Utc};
  |              ^^^^^^^^

warning: unused import: `Response`
 --> src\vultr\mod.rs:3:42
  |
3 | use reqwest::{header::HeaderMap, Client, Response};
  |                                          ^^^^^^^^

warning: unused imports: `Deserialize` and `Serialize`
 --> src\vultr\mod.rs:4:13
  |
4 | use serde::{Deserialize, Serialize};
  |             ^^^^^^^^^^^  ^^^^^^^^^

warning: unused import: `futures::TryStreamExt`
 --> src\vultr\mod.rs:7:5
  |
7 | use futures::TryStreamExt;
  |     ^^^^^^^^^^^^^^^^^^^^^

warning: unused imports: `DateTime` and `Utc`
 --> src\vultr\models.rs:1:14
  |
1 | use chrono::{DateTime, Utc};
  |              ^^^^^^^^  ^^^

warning: unused variable: `state`
  --> src\controllers\auth.rs:46:11
   |
46 |     State(state): State<Arc<AppState>>,
   |           ^^^^^ help: if this is intentional, prefix it with an underscore: `_state`
   |
   = note: `#[warn(unused_variables)]` on by default

error[E0599]: no method named `start_bare_metal` found for struct `VultrClient` in the current scope
   --> src\controllers\vultr.rs:389:10
    |
387 | /     state
388 | |         .vultr_client
389 | |         .start_bare_metal(&baremetal_id)
    | |_________-^^^^^^^^^^^^^^^^
    |
   ::: src\vultr\client.rs:8:1
    |
8   |   pub struct VultrClient {
    |   ---------------------- method `start_bare_metal` not found for this struct
    |
help: there is a method `get_bare_metal` with a similar name
    |
389 -         .start_bare_metal(&baremetal_id)
389 +         .get_bare_metal(&baremetal_id)
    |

error[E0599]: no method named `reboot_bare_metal` found for struct `VultrClient` in the current scope
   --> src\controllers\vultr.rs:403:10
    |
401 | /     state
402 | |         .vultr_client
403 | |         .reboot_bare_metal(&baremetal_id)
    | |_________-^^^^^^^^^^^^^^^^^
    |
   ::: src\vultr\client.rs:8:1
    |
8   |   pub struct VultrClient {
    |   ---------------------- method `reboot_bare_metal` not found for this struct
    |
help: there is a method `get_bare_metal` with a similar name
    |
403 -         .reboot_bare_metal(&baremetal_id)
403 +         .get_bare_metal(&baremetal_id)
    |

error[E0599]: no method named `reinstall_bare_metal` found for struct `VultrClient` in the current scope
   --> src\controllers\vultr.rs:417:10
    |
415 |       let bare_metal = state
    |  ______________________-
416 | |         .vultr_client
417 | |         .reinstall_bare_metal(&baremetal_id)
    | |_________-^^^^^^^^^^^^^^^^^^^^
    |
   ::: src\vultr\client.rs:8:1
    |
8   |   pub struct VultrClient {
    |   ---------------------- method `reinstall_bare_metal` not found for this struct
    |
help: there is a method `list_bare_metal` with a similar name, but with different arguments
   --> src\vultr\client.rs:85:5
    |
85  |     pub async fn list_bare_metal(&self) -> Result<Vec<VultrBareMetal>> {
    |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

error[E0599]: no method named `halt_bare_metal` found for struct `VultrClient` in the current scope
   --> src\controllers\vultr.rs:431:10
    |
429 | /     state
430 | |         .vultr_client
431 | |         .halt_bare_metal(&baremetal_id)
    | |_________-^^^^^^^^^^^^^^^
    |
   ::: src\vultr\client.rs:8:1
    |
8   |   pub struct VultrClient {
    |   ---------------------- method `halt_bare_metal` not found for this struct
    |
help: there is a method `get_bare_metal` with a similar name
    |
431 -         .halt_bare_metal(&baremetal_id)
431 +         .get_bare_metal(&baremetal_id)
    |

error[E0599]: no method named `halt_bare_metals` found for struct `VultrClient` in the current scope
   --> src\controllers\vultr.rs:445:10
    |
443 | /     state
444 | |         .vultr_client
445 | |         .halt_bare_metals(instance_ids)
    | |_________-^^^^^^^^^^^^^^^^
    |
   ::: src\vultr\client.rs:8:1
    |
8   |   pub struct VultrClient {
    |   ---------------------- method `halt_bare_metals` not found for this struct
    |
help: there is a method `get_bare_metal` with a similar name
    |
445 -         .halt_bare_metals(instance_ids)
445 +         .get_bare_metal(instance_ids)
    |

error[E0599]: no method named `reboot_bare_metals` found for struct `VultrClient` in the current scope
   --> src\controllers\vultr.rs:459:10
    |
457 | /     state
458 | |         .vultr_client
459 | |         .reboot_bare_metals(instance_ids)
    | |_________-^^^^^^^^^^^^^^^^^^
    |
   ::: src\vultr\client.rs:8:1
    |
8   |   pub struct VultrClient {
    |   ---------------------- method `reboot_bare_metals` not found for this struct
    |
help: there is a method `get_bare_metal` with a similar name
    |
459 -         .reboot_bare_metals(instance_ids)
459 +         .get_bare_metal(instance_ids)
    |

error[E0599]: no method named `start_bare_metals` found for struct `VultrClient` in the current scope
   --> src\controllers\vultr.rs:473:10
    |
471 | /     state
472 | |         .vultr_client
473 | |         .start_bare_metals(instance_ids)
    | |_________-^^^^^^^^^^^^^^^^^
    |
   ::: src\vultr\client.rs:8:1
    |
8   |   pub struct VultrClient {
    |   ---------------------- method `start_bare_metals` not found for this struct
    |
help: there is a method `get_bare_metal` with a similar name
    |
473 -         .start_bare_metals(instance_ids)
473 +         .get_bare_metal(instance_ids)
    |

warning: unused variable: `user_id`
   --> src\services\billing.rs:259:54
    |
259 |     async fn calculate_estimated_monthly_cost(&self, user_id: ObjectId) -> ServiceResult<f64> {
    |                                                      ^^^^^^^ help: if this is intentional, prefix it with an underscore: `_user_id`

warning: unused variable: `response`
   --> src\vultr\mod.rs:249:13
    |
249 |         let response = self.client.delete(&url).await?;
    |             ^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_response`

error[E0308]: mismatched types
  --> src\vultr\client.rs:37:9
   |
37 |         self.api_client.list_instances().await
   |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ expected `Result<Vec<VultrInstance>, Error>`, found `Result<Vec<VultrInstanceDetailed>, ...>`
   |
   = note: expected enum `Result<Vec<vultr::models::VultrInstance>, _>`
              found enum `Result<Vec<vultr::models::VultrInstanceDetailed>, _>`

error[E0599]: no method named `attach_bare_metal_vpc` found for struct `Arc<VultrApiClient>` in the current scope
   --> src\vultr\client.rs:198:25
    |
198 |         self.api_client.attach_bare_metal_vpc(baremetal_id, vpc_id).await
    |                         ^^^^^^^^^^^^^^^^^^^^^
    |
help: there is a method `get_bare_metal_vnc` with a similar name, but with different arguments
   --> src\vultr\mod.rs:299:5
    |
299 |     pub async fn get_bare_metal_vnc(&self, baremetal_id: &str) -> Result<BareMetalVncInfo> {
    |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

error[E0599]: no method named `detach_bare_metal_vpc` found for struct `Arc<VultrApiClient>` in the current scope
   --> src\vultr\client.rs:203:25
    |
203 |         self.api_client.detach_bare_metal_vpc(baremetal_id, vpc_id).await
    |                         ^^^^^^^^^^^^^^^^^^^^^
    |
help: there is a method `get_bare_metal_vnc` with a similar name, but with different arguments
   --> src\vultr\mod.rs:299:5
    |
299 |     pub async fn get_bare_metal_vnc(&self, baremetal_id: &str) -> Result<BareMetalVncInfo> {
    |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

error[E0277]: the trait bound `fn(State<Arc<...>>, ..., ...) -> ... {update_profile}: Handler<_, _>` is not satisfied
   --> src\routes\mod.rs:33:38
    |
33  |         .route("/users/profile", put(controllers::users::update_profile))
    |                                  --- ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ the trait `Handler<_, _>` is not implemented for fn item `fn(State<Arc<AppState>>, Request<Body>, ...) -> ... {update_profile}`
    |                                  |
    |                                  required by a bound introduced by this call
    |
    = note: Consider using `#[axum::debug_handler]` to improve the error message
    = help: the following other types implement trait `Handler<T, S>`:
              `MethodRouter<S>` implements `Handler<(), S>`
              `axum::handler::Layered<L, H, T, S>` implements `Handler<T, S>`
note: required by a bound in `put`
   --> C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\axum-0.7.9\src\routing\method_routing.rs:444:1
    |
444 | top_level_handler_fn!(put, PUT);
    | ^^^^^^^^^^^^^^^^^^^^^^---^^^^^^
    | |                     |
    | |                     required by a bound in this function
    | required by this bound in `put`
    = note: the full name for the type has been written to 'D:\workspace\.rust\achidas\target\debug\deps\achidas-4d377389ccdf1153.long-type-3632704293373722357.txt'
    = note: consider using `--verbose` to print the full type name to the console
    = note: this error originates in the macro `top_level_handler_fn` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0277]: the trait bound `fn(State<Arc<...>>, ..., ...) -> ... {create_instance}: Handler<_, _>` is not satisfied
   --> src\routes\mod.rs:39:35
    |
39  |         .route("/instances", post(controllers::instances::create_instance))
    |                              ---- ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ the trait `Handler<_, _>` is not implemented for fn item `fn(State<Arc<AppState>>, Request<Body>, ...) -> ... {create_instance}`
    |                              |
    |                              required by a bound introduced by this call
    |
    = note: Consider using `#[axum::debug_handler]` to improve the error message
    = help: the following other types implement trait `Handler<T, S>`:
              `MethodRouter<S>` implements `Handler<(), S>`
              `axum::handler::Layered<L, H, T, S>` implements `Handler<T, S>`
note: required by a bound in `post`
   --> C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\axum-0.7.9\src\routing\method_routing.rs:443:1
    |
443 | top_level_handler_fn!(post, POST);
    | ^^^^^^^^^^^^^^^^^^^^^^----^^^^^^^
    | |                     |
    | |                     required by a bound in this function
    | required by this bound in `post`
    = note: the full name for the type has been written to 'D:\workspace\.rust\achidas\target\debug\deps\achidas-4d377389ccdf1153.long-type-16333145041716333457.txt'
    = note: consider using `--verbose` to print the full type name to the console
    = note: this error originates in the macro `top_level_handler_fn` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0277]: the trait bound `fn(State<...>, ..., ..., ...) -> ... {update_instance}: Handler<_, _>` is not satisfied
   --> src\routes\mod.rs:41:38
    |
41  |         .route("/instances/:id", put(controllers::instances::update_instance))
    |                                  --- ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ the trait `Handler<_, _>` is not implemented for fn item `fn(State<Arc<AppState>>, Path<...>, ..., ...) -> ... {update_instance}`
    |                                  |
    |                                  required by a bound introduced by this call
    |
    = note: Consider using `#[axum::debug_handler]` to improve the error message
    = help: the following other types implement trait `Handler<T, S>`:
              `MethodRouter<S>` implements `Handler<(), S>`
              `axum::handler::Layered<L, H, T, S>` implements `Handler<T, S>`
note: required by a bound in `put`
   --> C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\axum-0.7.9\src\routing\method_routing.rs:444:1
    |
444 | top_level_handler_fn!(put, PUT);
    | ^^^^^^^^^^^^^^^^^^^^^^---^^^^^^
    | |                     |
    | |                     required by a bound in this function
    | required by this bound in `put`
    = note: the full name for the type has been written to 'D:\workspace\.rust\achidas\target\debug\deps\achidas-4d377389ccdf1153.long-type-169736000664712291.txt'
    = note: consider using `--verbose` to print the full type name to the console
    = note: this error originates in the macro `top_level_handler_fn` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0277]: the trait bound `FromFn<..., (), ..., _>: Service<...>` is not satisfied
   --> src\routes\mod.rs:369:16
    |
369 |         .layer(middleware::from_fn(auth_middleware))
    |          ----- ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ unsatisfied trait bound
    |          |
    |          required by a bound introduced by this call
    |
    = help: the trait `Service<axum::http::Request<axum::body::Body>>` is not implemented for `FromFn<fn(..., ..., ..., ...) -> ... {auth_middleware}, (), ..., _>`
    = help: the following other types implement trait `Service<Request>`:
              axum::middleware::FromFn<F, S, I, (T1, T2)>
              axum::middleware::FromFn<F, S, I, (T1, T2, T3)>
              axum::middleware::FromFn<F, S, I, (T1, T2, T3, T4)>
              axum::middleware::FromFn<F, S, I, (T1, T2, T3, T4, T5)>
              axum::middleware::FromFn<F, S, I, (T1, T2, T3, T4, T5, T6)>
              axum::middleware::FromFn<F, S, I, (T1, T2, T3, T4, T5, T6, T7)>
              axum::middleware::FromFn<F, S, I, (T1, T2, T3, T4, T5, T6, T7, T8)>
              axum::middleware::FromFn<F, S, I, (T1, T2, T3, T4, T5, T6, T7, T8, T9)>
            and 8 others
note: required by a bound in `Router::<S>::layer`
   --> C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\axum-0.7.9\src\routing\mod.rs:284:21
    |
281 |     pub fn layer<L>(self, layer: L) -> Router<S>
    |            ----- required by a bound in this associated function
...
284 |         L::Service: Service<Request> + Clone + Send + 'static,
    |                     ^^^^^^^^^^^^^^^^ required by this bound in `Router::<S>::layer`
    = note: the full name for the type has been written to 'D:\workspace\.rust\achidas\target\debug\deps\achidas-4d377389ccdf1153.long-type-2015363889294888911.txt'
    = note: consider using `--verbose` to print the full type name to the console

warning: use of deprecated method `chrono::DateTime::<Tz>::timestamp_nanos`: use `timestamp_nanos_opt()` instead
  --> src\utils\mod.rs:40:9
   |
40 |     now.timestamp_nanos().hash(&mut hasher);
   |         ^^^^^^^^^^^^^^^
   |
   = note: `#[warn(deprecated)]` on by default

Some errors have detailed explanations: E0252, E0277, E0308, E0412, E0425, E0432, E0433, E0599.
For more information about an error, try `rustc --explain E0252`.
warning: `achidas` (lib) generated 22 warnings
error: could not compile `achidas` (lib) due to 54 previous errors; 22 warnings emitted
