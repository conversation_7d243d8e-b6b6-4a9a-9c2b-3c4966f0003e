cargo :    Compiling achidas v0.1.0 (D:\workspace\.rust\achidas)
At line:1 char:1
+ cargo build 2>&1 | tee build_log.txt
+ ~~~~~~~~~~~~~~~~
    + CategoryInfo          : NotSpecified: (   Compiling ac...\.rust\achidas):String) [], RemoteException
    + FullyQualifiedErrorId : NativeCommandError
 
error[E0433]: failed to resolve: use of unresolved module or unlinked crate `trace`
  --> src\observability\mod.rs:31:17
   |
31 |                 trace::config()
   |                 ^^^^^ use of unresolved module or unlinked crate `trace`
   |
   = help: if you wanted to use a crate named `trace`, use `cargo add trace` to add it to your `Cargo.toml`
help: consider importing this module
   |
1  + use opentelemetry_sdk::trace;
   |

error[E0433]: failed to resolve: use of undeclared type `Sampler`
  --> src\observability\mod.rs:32:35
   |
32 |                     .with_sampler(Sampler::AlwaysOn)
   |                                   ^^^^^^^ use of undeclared type `Sampler`
   |
help: consider importing this enum
   |
1  + use opentelemetry_sdk::trace::Sam<PERSON>;
   |

error[E0433]: failed to resolve: use of undeclared type `RandomIdGenerator`
  --> src\observability\mod.rs:33:40
   |
33 |                     .with_id_generator(RandomIdGenerator::default())
   |                                        ^^^^^^^^^^^^^^^^^ use of undeclared type `RandomIdGenerator`
   |
help: consider importing this struct
   |
1  + use opentelemetry_sdk::trace::RandomIdGenerator;
   |

error[E0433]: failed to resolve: use of undeclared type `Resource`
  --> src\observability\mod.rs:34:36
   |
34 |                     .with_resource(Resource::new(vec![
   |                                    ^^^^^^^^ use of undeclared type `Resource`
   |
help: consider importing this struct
   |
1  + use opentelemetry_sdk::Resource;
   |

error[E0433]: failed to resolve: use of undeclared type `KeyValue`
  --> src\observability\mod.rs:35:25
   |
35 |                         KeyValue::new("service.name", "achidas-backend"),
   |                         ^^^^^^^^ use of undeclared type `KeyValue`
   |
help: consider importing this struct
   |
1  + use opentelemetry::KeyValue;
   |

error[E0433]: failed to resolve: use of undeclared type `KeyValue`
  --> src\observability\mod.rs:36:25
   |
36 |                         KeyValue::new("service.version", env!("CARGO_PKG_VERSION")),
   |                         ^^^^^^^^ use of undeclared type `KeyValue`
   |
help: consider importing this struct
   |
1  + use opentelemetry::KeyValue;
   |

warning: unused import: `billing::BillingService`
 --> src\controllers\admin.rs:4:37
  |
4 |     services::{admin::AdminService, billing::BillingService},
  |                                     ^^^^^^^^^^^^^^^^^^^^^^^
  |
  = note: `#[warn(unused_imports)]` on by default

warning: unused imports: `AddDatabaseReadReplicaRequest`, `AddStorageGatewayExportRequest`, `BareMetalBandwidth`, 
`BareMetalIpv4Info`, `BareMetalIpv6Info`, `BareMetalUpgrades`, `BareMetalUserData`, `BareMetalVncInfo`, `BareMetalVpcInfo`, 
`CreateBareMetalRequest`, `CreateDatabaseConnectionPoolRequest`, `CreateDatabaseConnectorRequest`, 
`CreateDatabaseDBRequest`, `CreateDatabaseQuotaRequest`, `CreateDatabaseTopicRequest`, `CreateSSHKeyRequest`, 
`CreateStartupScriptRequest`, `CreateStorageGatewayRequest`, `CreateUserRequest`, `CreateVFSAttachmentRequest`, 
`CreateVFSRequest`, `ForkDatabaseRequest`, `RestoreDatabaseFromBackupRequest`, `StartDatabaseMigrationRequest`, 
`StartDatabaseVersionUpgradeRequest`, `UpdateBareMetalRequest`, `UpdateDatabaseConnectionPoolRequest`, 
`UpdateDatabaseConnectorRequest`, `UpdateDatabaseTopicRequest`, `UpdateSSHKeyRequest`, `UpdateStartupScriptRequest`, 
`UpdateStorageGatewayRequest`, `UpdateUserRequest`, `UpdateVFSRequest`, `VultrBareMetal`, `VultrDatabaseAdvancedOptions`, 
`VultrDatabaseBackupInfo`, `VultrDatabaseConnectionPool`, `VultrDatabaseConnectorStatus`, `VultrDatabaseConnector`, 
`VultrDatabaseDB`, `VultrDatabaseMaintenanceUpdate`, `VultrDatabaseMigrationStatus`, `VultrDatabaseQuota`, 
`VultrDatabaseReadReplica`, `VultrDatabaseServiceAlert`, `VultrDatabaseTopic`, `VultrDatabaseVersions`, `VultrOS`, 
`VultrPlan`, `VultrRegion`, `VultrStartupScript`, `VultrStorageGatewayExport`, `VultrStorageGateway`, `VultrUser`, 
`VultrVFSAttachment`, and `VultrVFS`
  --> src\controllers\vultr.rs:4:9
   |
4  |         BareMetalBandwidth, BareMetalIpv4Info, BareMetalIpv6Info, BareMetalUpgrades,
   |         ^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^
5  |         BareMetalUserData, BareMetalVncInfo, BareMetalVpcInfo, CreateBareMetalRequest,
   |         ^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^
6  |         UpdateBareMetalRequest, VultrOS, VultrPlan, VultrRegion, VultrBareMetal,
   |         ^^^^^^^^^^^^^^^^^^^^^^  ^^^^^^^  ^^^^^^^^^  ^^^^^^^^^^^  ^^^^^^^^^^^^^^
...
37 |         SetDatabaseUserACLRequest, VultrDatabaseDB, CreateDatabaseDBRequest,
   |                                    ^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^^
38 |         VultrDatabaseTopic, CreateDatabaseTopicRequest, UpdateDatabaseTopicRequest,
   |         ^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^^^^^
39 |         VultrDatabaseQuota, CreateDatabaseQuotaRequest, VultrDatabaseConnector,
   |         ^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^
40 |         CreateDatabaseConnectorRequest, UpdateDatabaseConnectorRequest,
   |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
41 |         VultrDatabaseConnectorStatus, VultrDatabaseMaintenanceUpdate,
   |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
42 |         VultrDatabaseServiceAlert, VultrDatabaseMigrationStatus, StartDatabaseMigrationRequest,
   |         ^^^^^^^^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
43 |         AddDatabaseReadReplicaRequest, VultrDatabaseReadReplica, VultrDatabaseBackupInfo,
   |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^^
44 |         RestoreDatabaseFromBackupRequest, ForkDatabaseRequest, VultrDatabaseConnectionPool,
   |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^^^^^^
45 |         CreateDatabaseConnectionPoolRequest, UpdateDatabaseConnectionPoolRequest,
   |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
46 |         VultrDatabaseAdvancedOptions, VultrDatabaseVersions, StartDatabaseVersionUpgradeRequest,
   |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
...
55 |         CreateSubaccountRequest, CreateSSHKeyRequest, UpdateSSHKeyRequest,
   |                                  ^^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^
56 |         VultrStartupScript, CreateStartupScriptRequest, UpdateStartupScriptRequest,
   |         ^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^^^^^
57 |         VultrStorageGateway, CreateStorageGatewayRequest, UpdateStorageGatewayRequest,
   |         ^^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^^^^^^
58 |         VultrStorageGatewayExport, AddStorageGatewayExportRequest, VultrUser,
   |         ^^^^^^^^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^  ^^^^^^^^^
59 |         CreateUserRequest, UpdateUserRequest, VultrVFS, CreateVFSRequest,
   |         ^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^  ^^^^^^^^  ^^^^^^^^^^^^^^^^
60 |         UpdateVFSRequest, VultrVFSAttachment, CreateVFSAttachmentRequest,
   |         ^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `serde_json::json`
  --> src\controllers\mod.rs:15:5
   |
15 | use serde_json::json;
   |     ^^^^^^^^^^^^^^^^

warning: unused import: `error`
  --> src\middleware\auth.rs:14:15
   |
14 | use tracing::{error, warn};
   |               ^^^^^

warning: unused imports: `HeaderValue` and `response::Response`
 --> src\middleware\cors.rs:2:20
  |
2 |     http::{header, HeaderValue, Method},
  |                    ^^^^^^^^^^^
3 |     response::Response,
  |     ^^^^^^^^^^^^^^^^^^

warning: unused import: `StatusCode`
 --> src\middleware\mod.rs:7:21
  |
7 |     http::{Request, StatusCode},
  |                     ^^^^^^^^^^

warning: unused import: `bson::oid::ObjectId`
 --> src\models\mod.rs:1:5
  |
1 | use bson::oid::ObjectId;
  |     ^^^^^^^^^^^^^^^^^^^

warning: unused imports: `DateTime` and `Utc`
 --> src\models\mod.rs:2:14
  |
2 | use chrono::{DateTime, Utc};
  |              ^^^^^^^^  ^^^

warning: unused import: `uuid::Uuid`
 --> src\models\mod.rs:4:5
  |
4 | use uuid::Uuid;
  |     ^^^^^^^^^^

warning: unused import: `Level`
  --> src\observability\mod.rs:13:21
   |
13 | use tracing::{info, Level};
   |                     ^^^^^

warning: unused import: `UserStatus`
 --> src\services\admin.rs:5:9
  |
5 |         UserStatus,
  |         ^^^^^^^^^^

warning: unused import: `error`
  --> src\services\auth.rs:15:15
   |
15 | use tracing::{error, info, instrument};
   |               ^^^^^

warning: unused import: `error`
  --> src\services\instance.rs:14:15
   |
14 | use tracing::{error, info, instrument};
   |               ^^^^^

warning: unused import: `crate::controllers::ControllerError`
 --> src\services\mod.rs:7:5
  |
7 | use crate::controllers::ControllerError;
  |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `DateTime`
 --> src\utils\mod.rs:3:14
  |
3 | use chrono::{DateTime, Utc};
  |              ^^^^^^^^

warning: unused import: `Response`
 --> src\vultr\mod.rs:3:42
  |
3 | use reqwest::{header::HeaderMap, Client, Response};
  |                                          ^^^^^^^^

warning: unused imports: `Deserialize` and `Serialize`
 --> src\vultr\mod.rs:4:13
  |
4 | use serde::{Deserialize, Serialize};
  |             ^^^^^^^^^^^  ^^^^^^^^^

warning: unused import: `futures::TryStreamExt`
 --> src\vultr\mod.rs:7:5
  |
7 | use futures::TryStreamExt;
  |     ^^^^^^^^^^^^^^^^^^^^^

warning: unused imports: `DateTime` and `Utc`
 --> src\vultr\models.rs:1:14
  |
1 | use chrono::{DateTime, Utc};
  |              ^^^^^^^^  ^^^

warning: unused variable: `state`
  --> src\controllers\auth.rs:46:11
   |
46 |     State(state): State<Arc<AppState>>,
   |           ^^^^^ help: if this is intentional, prefix it with an underscore: `_state`
   |
   = note: `#[warn(unused_variables)]` on by default

warning: unused variable: `method`
   --> src\observability\mod.rs:101:9
    |
101 |     let method = req.method().clone();
    |         ^^^^^^ help: if this is intentional, prefix it with an underscore: `_method`

warning: unused variable: `path`
   --> src\observability\mod.rs:102:9
    |
102 |     let path = req.uri().path().to_string();
    |         ^^^^ help: if this is intentional, prefix it with an underscore: `_path`

warning: unused variable: `duration`
   --> src\observability\mod.rs:106:9
    |
106 |     let duration = start.elapsed();
    |         ^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_duration`

warning: unused variable: `status`
   --> src\observability\mod.rs:107:9
    |
107 |     let status = response.status().as_u16();
    |         ^^^^^^ help: if this is intentional, prefix it with an underscore: `_status`

error[E0392]: lifetime parameter `'a` is never used
  --> src\services\admin.rs:16:25
   |
16 | pub struct AdminService<'a> {
   |                         ^^ unused lifetime parameter
   |
   = help: consider removing `'a`, referring to it in a field, or using a marker such as `PhantomData`

error[E0599]: no method named `sort` found for opaque type `impl Future<Output = Result<Cursor<User>, Error>>` in the 
current scope
  --> src\services\admin.rs:45:14
   |
42 |           let users: Vec<User> = self
   |  ________________________________-
43 | |             .users
44 | |             .find(doc! {}, None)
45 | |             .sort(doc! { "created_at": -1 })
   | |             -^^^^ method not found in `impl Future<Output = Result<Cursor<User>, Error>>`
   | |_____________|
   |

error[E0599]: no method named `sort` found for opaque type `impl Future<Output = Result<Cursor<Instance>, Error>>` in the 
current scope
   --> src\services\admin.rs:140:14
    |
137 |           let instances: Vec<Instance> = self
    |  ________________________________________-
138 | |             .instances
139 | |             .find(doc! {}, None)
140 | |             .sort(doc! { "created_at": -1 })
    | |             -^^^^ method not found in `impl Future<Output = Result<Cursor<Instance>, Error>>`
    | |_____________|
    |

warning: unused variable: `user_id`
   --> src\services\billing.rs:259:54
    |
259 |     async fn calculate_estimated_monthly_cost(&self, user_id: ObjectId) -> ServiceResult<f64> {
    |                                                      ^^^^^^^ help: if this is intentional, prefix it with an 
underscore: `_user_id`

warning: unused variable: `response`
   --> src\vultr\mod.rs:249:13
    |
249 |         let response = self.client.delete(&url).await?;
    |             ^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_response`

error[E0277]: the trait bound `SocketAddr: From<()>` is not satisfied
   --> src\observability\mod.rs:76:29
    |
76  |         .with_http_listener(config.prometheus_endpoint.parse()?)
    |          ------------------ ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ the trait `From<()>` is not implemented for 
`SocketAddr`
    |          |
    |          required by a bound introduced by this call
    |
    = help: the following other types implement trait `From<T>`:
              `SocketAddr` implements `From<(I, u16)>`
              `SocketAddr` implements `From<SocketAddrV4>`
              `SocketAddr` implements `From<SocketAddrV6>`
    = note: required for `()` to implement `Into<SocketAddr>`
note: required by a bound in `PrometheusBuilder::with_http_listener`
   --> 
C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\metrics-exporter-prometheus-0.13.1\src\builder.rs:144:52
    |
144 |     pub fn with_http_listener(mut self, addr: impl Into<SocketAddr>) -> Self {
    |                                                    ^^^^^^^^^^^^^^^^ required by this bound in 
`PrometheusBuilder::with_http_listener`

error[E0277]: the trait bound `(): FromStr` is not satisfied
    --> src\observability\mod.rs:76:56
     |
76   |         .with_http_listener(config.prometheus_endpoint.parse()?)
     |                                                        ^^^^^ the trait `FromStr` is not implemented for `()`
     |
     = help: the following other types implement trait `FromStr`:
               Algorithm
               AuthMechanism
               Authority
               Braced
               ByteString
               CString
               CollationAlternate
               CollationCaseFirst
             and 152 others
note: required by a bound in `core::str::<impl str>::parse`
    --> 
C:\Users\<USER>\.rustup\toolchains\stable-x86_64-pc-windows-gnu\lib/rustlib/src/rust\library\core\src\str\mod.rs:2607:21
     |
2607 |     pub fn parse<F: FromStr>(&self) -> Result<F, F::Err> {
     |                     ^^^^^^^ required by this bound in `core::str::<impl str>::parse`

warning: use of deprecated method `chrono::DateTime::<Tz>::timestamp_nanos`: use `timestamp_nanos_opt()` instead
  --> src\utils\mod.rs:40:9
   |
40 |     now.timestamp_nanos().hash(&mut hasher);
   |         ^^^^^^^^^^^^^^^
   |
   = note: `#[warn(deprecated)]` on by default

Some errors have detailed explanations: E0277, E0392, E0433, E0599.
For more information about an error, try `rustc --explain E0277`.
warning: `achidas` (lib) generated 27 warnings
error: could not compile `achidas` (lib) due to 11 previous errors; 27 warnings emitted
