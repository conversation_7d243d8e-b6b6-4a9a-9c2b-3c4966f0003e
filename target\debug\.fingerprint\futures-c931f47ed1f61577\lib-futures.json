{"rustc": 2830703817519440116, "features": "[\"alloc\", \"async-await\", \"default\", \"executor\", \"futures-executor\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"bilock\", \"cfg-target-has-atomic\", \"compat\", \"default\", \"executor\", \"futures-executor\", \"io-compat\", \"std\", \"thread-pool\", \"unstable\", \"write-all-vectored\"]", "target": 7465627196321967167, "profile": 17467636112133979524, "path": 15486086225682889473, "deps": [[5103565458935487, "futures_io", false, 14764076604848365451], [1811549171721445101, "futures_channel", false, 8930785311309996714], [7013762810557009322, "futures_sink", false, 5564455281536599080], [7620660491849607393, "futures_core", false, 9910657298933926885], [10629569228670356391, "futures_util", false, 10549570323539430975], [12779779637805422465, "futures_executor", false, 6179543211601776828], [16240732885093539806, "futures_task", false, 1190188428776250535]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\futures-c931f47ed1f61577\\dep-lib-futures", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}