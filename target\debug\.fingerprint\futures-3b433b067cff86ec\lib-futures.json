{"rustc": 2830703817519440116, "features": "[\"alloc\", \"async-await\", \"default\", \"executor\", \"futures-executor\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"bilock\", \"cfg-target-has-atomic\", \"compat\", \"default\", \"executor\", \"futures-executor\", \"io-compat\", \"std\", \"thread-pool\", \"unstable\", \"write-all-vectored\"]", "target": 7465627196321967167, "profile": 13318305459243126790, "path": 15486086225682889473, "deps": [[5103565458935487, "futures_io", false, 9469164592304382141], [1811549171721445101, "futures_channel", false, 11181523239334338133], [7013762810557009322, "futures_sink", false, 9894630746963026132], [7620660491849607393, "futures_core", false, 9385414846926858188], [10629569228670356391, "futures_util", false, 5228039834430210376], [12779779637805422465, "futures_executor", false, 334891656143791583], [16240732885093539806, "futures_task", false, 8342288194159562261]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\futures-3b433b067cff86ec\\dep-lib-futures", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}