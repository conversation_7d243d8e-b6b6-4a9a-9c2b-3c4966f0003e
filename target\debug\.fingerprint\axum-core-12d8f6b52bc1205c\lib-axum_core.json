{"rustc": 2830703817519440116, "features": "[\"tracing\"]", "declared_features": "[\"__private_docs\", \"tracing\"]", "target": 2565713999752801252, "profile": 2241668132362809309, "path": 10600795123700551178, "deps": [[784494742817713399, "tower_service", false, 13980336227272009489], [1906322745568073236, "pin_project_lite", false, 13886474930693114932], [2517136641825875337, "sync_wrapper", false, 12463273242852007676], [7712452662827335977, "tower_layer", false, 12375612145001944731], [7858942147296547339, "rustversion", false, 13285475538935970327], [8606274917505247608, "tracing", false, 13822962143594336692], [9010263965687315507, "http", false, 15833340190285319200], [10229185211513642314, "mime", false, 16750090556777528361], [10629569228670356391, "futures_util", false, 10549570323539430975], [11946729385090170470, "async_trait", false, 3254960072444411179], [14084095096285906100, "http_body", false, 17926065028124312500], [16066129441945555748, "bytes", false, 2617022058346697638], [16900715236047033623, "http_body_util", false, 6146872258357789516]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\axum-core-12d8f6b52bc1205c\\dep-lib-axum_core", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}