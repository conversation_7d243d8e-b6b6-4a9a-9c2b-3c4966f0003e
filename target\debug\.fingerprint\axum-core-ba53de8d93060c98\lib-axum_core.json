{"rustc": 2830703817519440116, "features": "[\"tracing\"]", "declared_features": "[\"__private_docs\", \"tracing\"]", "target": 2565713999752801252, "profile": 15657897354478470176, "path": 10600795123700551178, "deps": [[784494742817713399, "tower_service", false, 10334702719707753232], [1906322745568073236, "pin_project_lite", false, 5735289483764551673], [2517136641825875337, "sync_wrapper", false, 11444608990833253723], [7712452662827335977, "tower_layer", false, 9331745602861664882], [7858942147296547339, "rustversion", false, 13285475538935970327], [8606274917505247608, "tracing", false, 17320339988540626530], [9010263965687315507, "http", false, 14954775231676038152], [10229185211513642314, "mime", false, 18301127011126638926], [10629569228670356391, "futures_util", false, 5228039834430210376], [11946729385090170470, "async_trait", false, 3254960072444411179], [14084095096285906100, "http_body", false, 9957014415733306369], [16066129441945555748, "bytes", false, 14215611657469732184], [16900715236047033623, "http_body_util", false, 2880669227747316702]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\axum-core-ba53de8d93060c98\\dep-lib-axum_core", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}