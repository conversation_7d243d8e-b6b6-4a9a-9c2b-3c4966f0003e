{"rustc": 2830703817519440116, "features": "[\"__rustls\", \"__tls\", \"default\", \"default-tls\", \"hyper-rustls\", \"hyper-tls\", \"json\", \"native-tls-crate\", \"rustls\", \"rustls-tls\", \"rustls-tls-webpki-roots\", \"serde_json\", \"tokio-native-tls\", \"tokio-rustls\", \"webpki-roots\"]", "declared_features": "[\"__internal_proxy_sys_no_cache\", \"__rustls\", \"__tls\", \"async-compression\", \"blocking\", \"brotli\", \"cookie_crate\", \"cookie_store\", \"cookies\", \"default\", \"default-tls\", \"deflate\", \"futures-channel\", \"gzip\", \"h3\", \"h3-quinn\", \"hickory-dns\", \"hickory-resolver\", \"http3\", \"hyper-rustls\", \"hyper-tls\", \"json\", \"mime_guess\", \"multipart\", \"native-tls\", \"native-tls-alpn\", \"native-tls-crate\", \"native-tls-vendored\", \"quinn\", \"rustls\", \"rustls-native-certs\", \"rustls-tls\", \"rustls-tls-manual-roots\", \"rustls-tls-native-roots\", \"rustls-tls-webpki-roots\", \"serde_json\", \"socks\", \"stream\", \"tokio-native-tls\", \"tokio-rustls\", \"tokio-socks\", \"tokio-util\", \"trust-dns\", \"wasm-streams\", \"webpki-roots\"]", "target": 16585426341985349207, "profile": 15657897354478470176, "path": 6712895673869365022, "deps": [[40386456601120721, "percent_encoding", false, 6627269539827169883], [95042085696191081, "ipnet", false, 14225013059394677478], [264090853244900308, "sync_wrapper", false, 6922644747238109128], [784494742817713399, "tower_service", false, 10334702719707753232], [1044435446100926395, "hyper_rustls", false, 12582748403532215330], [1906322745568073236, "pin_project_lite", false, 5735289483764551673], [3150220818285335163, "url", false, 2818567344134284166], [3722963349756955755, "once_cell", false, 15293267452495451070], [4405182208873388884, "http", false, 2696871656096564511], [5986029879202738730, "log", false, 14377744429261528332], [7414427314941361239, "hyper", false, 15474622988467499590], [7620660491849607393, "futures_core", false, 9385414846926858188], [8405603588346937335, "winreg", false, 10436857078242394237], [8915503303801890683, "http_body", false, 16967756622179640550], [9538054652646069845, "tokio", false, 8568442550004330865], [9689903380558560274, "serde", false, 17239657888703164653], [10229185211513642314, "mime", false, 18301127011126638926], [10629569228670356391, "futures_util", false, 5228039834430210376], [11295624341523567602, "rustls", false, 6472615969078877582], [12186126227181294540, "tokio_native_tls", false, 12482580820618727291], [12367227501898450486, "hyper_tls", false, 3435100102515184031], [13809605890706463735, "h2", false, 12586311100461003020], [14564311161534545801, "encoding_rs", false, 1200868389652484398], [15367738274754116744, "serde_json", false, 4289499926161375560], [16066129441945555748, "bytes", false, 14215611657469732184], [16311359161338405624, "rustls_pemfile", false, 3675849496777339019], [16542808166767769916, "serde_urlencoded", false, 15688169463282040872], [16622232390123975175, "tokio_rustls", false, 10537559061395996278], [16785601910559813697, "native_tls_crate", false, 2487288803102195829], [17652733826348741533, "webpki_roots", false, 5092035178562647879], [18066890886671768183, "base64", false, 8310951401715748559]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\reqwest-20b4d84f1f1feaf6\\dep-lib-reqwest", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}