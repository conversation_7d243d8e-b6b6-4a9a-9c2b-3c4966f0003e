D:\workspace\.rust\achidas\target\debug\deps\libopentelemetry_semantic_conventions-e355f117940409ef.rmeta: C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\opentelemetry-semantic-conventions-0.13.0\src\lib.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\opentelemetry-semantic-conventions-0.13.0\src\resource.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\opentelemetry-semantic-conventions-0.13.0\src\trace.rs

D:\workspace\.rust\achidas\target\debug\deps\libopentelemetry_semantic_conventions-e355f117940409ef.rlib: C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\opentelemetry-semantic-conventions-0.13.0\src\lib.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\opentelemetry-semantic-conventions-0.13.0\src\resource.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\opentelemetry-semantic-conventions-0.13.0\src\trace.rs

D:\workspace\.rust\achidas\target\debug\deps\opentelemetry_semantic_conventions-e355f117940409ef.d: C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\opentelemetry-semantic-conventions-0.13.0\src\lib.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\opentelemetry-semantic-conventions-0.13.0\src\resource.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\opentelemetry-semantic-conventions-0.13.0\src\trace.rs

C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\opentelemetry-semantic-conventions-0.13.0\src\lib.rs:
C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\opentelemetry-semantic-conventions-0.13.0\src\resource.rs:
C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\opentelemetry-semantic-conventions-0.13.0\src\trace.rs:
