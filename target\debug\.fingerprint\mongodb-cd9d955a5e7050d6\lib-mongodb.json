{"rustc": 2830703817519440116, "features": "[\"default\", \"serde_bytes\", \"tokio-runtime\"]", "declared_features": "[\"async-executor\", \"async-std\", \"async-std-resolver\", \"async-std-runtime\", \"aws-auth\", \"azure-kms\", \"bson-chrono-0_4\", \"bson-serde_with\", \"bson-uuid-0_8\", \"bson-uuid-1\", \"default\", \"flate2\", \"gcp-kms\", \"in-use-encryption-unstable\", \"log\", \"mongocrypt\", \"num_cpus\", \"openssl\", \"openssl-probe\", \"openssl-tls\", \"rayon\", \"reqwest\", \"serde_bytes\", \"snap\", \"snappy-compression\", \"sync\", \"tokio-openssl\", \"tokio-runtime\", \"tokio-sync\", \"tracing\", \"tracing-unstable\", \"zlib-compression\", \"zstd\", \"zstd-compression\"]", "target": 6467422678098242930, "profile": 15657897354478470176, "path": 15581766931217037629, "deps": [[5103565458935487, "futures_io", false, 9469164592304382141], [40386456601120721, "percent_encoding", false, 6627269539827169883], [501918078635137462, "take_mut", false, 5654619986322638176], [530211389790465181, "hex", false, 12173332956928825589], [1211321333142909612, "socket2", false, 14783440350397757160], [1288403060204016458, "tokio_util", false, 3703528974518153916], [1469478705076669426, "trust_dns_proto", false, 6179798007897486158], [1526817731016152233, "stringprep", false, 4273964468132757469], [2283771217451780507, "serde_with", false, 2019936301004983668], [4258399515347749257, "pbkdf2", false, 16268534961868939710], [5841926810058920975, "strsim", false, 540653054463474813], [7051825882133757896, "md5", false, 10633389092850431784], [7620660491849607393, "futures_core", false, 9385414846926858188], [7845877979469732493, "trust_dns_resolver", false, 13702976209282703281], [8008191657135824715, "thiserror", false, 3265526716396010181], [8319709847752024821, "uuid", false, 14312790758878167002], [9209347893430674936, "hmac", false, 9793666561722611541], [9504753771229857410, "derive_more", false, 9435938209292261815], [9538054652646069845, "tokio", false, 8568442550004330865], [9689903380558560274, "serde", false, 17239657888703164653], [9751220522286876350, "rustc_version_runtime", false, 2507199316112138820], [9767387392190177945, "typed_builder", false, 2421315702730453325], [9857275760291862238, "sha2", false, 3373326198591540768], [9897246384292347999, "chrono", false, 1048984997933943669], [10435729446543529114, "bitflags", false, 2612007380683144660], [10629569228670356391, "futures_util", false, 5228039834430210376], [10889494155287625682, "serde_bytes", false, 8146115688222961642], [11295624341523567602, "rustls", false, 6472615969078877582], [11946729385090170470, "async_trait", false, 3254960072444411179], [12779779637805422465, "futures_executor", false, 334891656143791583], [13208667028893622512, "rand", false, 9577676731279368259], [13859769749131231458, "derivative", false, 8040178850294843001], [13886384384316890372, "bson", false, 7481129588041256079], [16311359161338405624, "rustls_pemfile", false, 3675849496777339019], [16622232390123975175, "tokio_rustls", false, 10537559061395996278], [17282734725213053079, "base64", false, 17948649425109078400], [17348166850176847349, "sha1", false, 61291265079641970], [17652733826348741533, "webpki_roots", false, 5092035178562647879], [17917672826516349275, "lazy_static", false, 1043064615545582854]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\mongodb-cd9d955a5e7050d6\\dep-lib-mongodb", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}