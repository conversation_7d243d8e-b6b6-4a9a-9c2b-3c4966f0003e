use crate::{
    models::{<PERSON><PERSON><PERSON>, UserRole},
    services::auth::AuthService,
    AppState,
};
use axum::{
    extract::{Request, State},
    http::{HeaderMap, StatusCode},
    middleware::Next,
    response::Response,
    body::Body,
};
use std::sync::Arc;
use tracing::{error, warn};

// Authentication middleware
pub async fn auth_middleware(
    State(state): State<Arc<AppState>>,
    headers: HeaderMap,
    mut req: Request<Body>,
    next: Next,
) -> Result<Response, StatusCode> {
    // Extract token from Authorization header
    let token = extract_token_from_headers(&headers)?;
    
    // Verify token
    let auth_service = AuthService::new(&state.database, &state.config);
    let claims = auth_service
        .verify_token(&token)
        .map_err(|e| {
            warn!("Token verification failed: {}", e);
            StatusCode::UNAUTHORIZED
        })?;
    
    // Add claims to request extensions
    req.extensions_mut().insert(claims);
    
    Ok(next.run(req).await)
}

// Admin-only middleware
pub async fn admin_middleware(
    req: Request,
    next: Next,
) -> Result<Response, StatusCode> {
    // Get claims from request extensions
    let claims = req
        .extensions()
        .get::<Claims>()
        .ok_or(StatusCode::UNAUTHORIZED)?;
    
    // Check if user is admin
    match claims.role {
        UserRole::Admin => Ok(next.run(req).await),
        _ => {
            warn!("Non-admin user attempted to access admin endpoint: {}", claims.email);
            Err(StatusCode::FORBIDDEN)
        }
    }
}

// Extract token from Authorization header
fn extract_token_from_headers(headers: &HeaderMap) -> Result<String, StatusCode> {
    let auth_header = headers
        .get("authorization")
        .ok_or(StatusCode::UNAUTHORIZED)?
        .to_str()
        .map_err(|_| StatusCode::UNAUTHORIZED)?;
    
    if !auth_header.starts_with("Bearer ") {
        return Err(StatusCode::UNAUTHORIZED);
    }
    
    Ok(auth_header[7..].to_string())
}

// Helper function to get current user claims from request
pub fn get_current_user(req: &Request) -> Result<&Claims, StatusCode> {
    req.extensions()
        .get::<Claims>()
        .ok_or(StatusCode::UNAUTHORIZED)
}
