{"rustc": 2830703817519440116, "features": "[\"cors\", \"default\", \"timeout\", \"tokio\", \"trace\", \"tracing\"]", "declared_features": "[\"add-extension\", \"async-compression\", \"auth\", \"base64\", \"catch-panic\", \"compression-br\", \"compression-deflate\", \"compression-full\", \"compression-gzip\", \"compression-zstd\", \"cors\", \"decompression-br\", \"decompression-deflate\", \"decompression-full\", \"decompression-gzip\", \"decompression-zstd\", \"default\", \"follow-redirect\", \"fs\", \"full\", \"futures-core\", \"futures-util\", \"httpdate\", \"iri-string\", \"limit\", \"map-request-body\", \"map-response-body\", \"metrics\", \"mime\", \"mime_guess\", \"normalize-path\", \"percent-encoding\", \"propagate-header\", \"redirect\", \"request-id\", \"sensitive-headers\", \"set-header\", \"set-status\", \"timeout\", \"tokio\", \"tokio-util\", \"tower\", \"trace\", \"tracing\", \"util\", \"uuid\", \"validate-request\"]", "target": 14850331575045365232, "profile": 15657897354478470176, "path": 3526564220616807389, "deps": [[784494742817713399, "tower_service", false, 10334702719707753232], [1906322745568073236, "pin_project_lite", false, 5735289483764551673], [7712452662827335977, "tower_layer", false, 9331745602861664882], [7896293946984509699, "bitflags", false, 184042552076502769], [8606274917505247608, "tracing", false, 17320339988540626530], [9010263965687315507, "http", false, 14954775231676038152], [9538054652646069845, "tokio", false, 8568442550004330865], [14084095096285906100, "http_body", false, 9957014415733306369], [16066129441945555748, "bytes", false, 14215611657469732184], [16900715236047033623, "http_body_util", false, 2880669227747316702]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tower-http-5cc58b577d2e9164\\dep-lib-tower_http", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}