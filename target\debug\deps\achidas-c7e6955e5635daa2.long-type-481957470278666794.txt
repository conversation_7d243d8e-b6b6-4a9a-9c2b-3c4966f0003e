fn(axum::extract::State<Arc<AppState>>, axum::http::Request<axum::body::Body>, axum::Json<models::instance::CreateInstanceRequest>) -> impl futures::Future<Output = Result<axum::Json<ApiResponse<models::instance::InstanceResponse>>, ControllerError>> {create_instance}
fn(axum::extract::State<Arc<AppState>>, axum::http::Request<axum::body::Body>, axum::Json<models::instance::CreateInstanceRequest>) -> impl futures::Future<Output = Result<axum::Json<ApiResponse<models::instance::InstanceResponse>>, ControllerError>> {create_instance}: Handler<_, _>
