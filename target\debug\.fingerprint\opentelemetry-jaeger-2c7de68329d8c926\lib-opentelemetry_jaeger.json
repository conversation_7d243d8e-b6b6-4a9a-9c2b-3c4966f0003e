{"rustc": 2830703817519440116, "features": "[\"default\", \"rt-tokio\", \"tokio\"]", "declared_features": "[\"async-std\", \"base64\", \"collector_client\", \"default\", \"full\", \"futures-executor\", \"headers\", \"http\", \"hyper\", \"hyper-tls\", \"hyper_collector_client\", \"hyper_tls_collector_client\", \"integration_test\", \"isahc\", \"isahc_collector_client\", \"js-sys\", \"opentelemetry-http\", \"pin-project-lite\", \"prost\", \"prost-types\", \"reqwest\", \"reqwest_blocking_collector_client\", \"reqwest_collector_client\", \"reqwest_rustls_collector_client\", \"rt-async-std\", \"rt-tokio\", \"rt-tokio-current-thread\", \"surf\", \"surf_collector_client\", \"tokio\", \"tonic\", \"wasm-bindgen\", \"wasm-bindgen-futures\", \"wasm_collector_client\", \"web-sys\"]", "target": 5286705727763485303, "profile": 15657897354478470176, "path": 17854228615920802553, "deps": [[1337443197003760792, "opentelemetry", false, 14874685377148721533], [6747019202424031207, "thrift", false, 14909990164196415501], [7009435218076268184, "opentelemetry_sdk", false, 11892036574281279352], [7620660491849607393, "futures_core", false, 9385414846926858188], [9538054652646069845, "tokio", false, 8568442550004330865], [9752316479938937174, "opentelemetry_semantic_conventions", false, 508235890817607478], [10629569228670356391, "futures_util", false, 5228039834430210376], [11946729385090170470, "async_trait", false, 3254960072444411179]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\opentelemetry-jaeger-2c7de68329d8c926\\dep-lib-opentelemetry_jaeger", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}