{"rustc": 2830703817519440116, "features": "[\"ahash\", \"allocator-api2\", \"default\", \"inline-more\"]", "declared_features": "[\"ahash\", \"alloc\", \"allocator-api2\", \"compiler_builtins\", \"core\", \"default\", \"equivalent\", \"inline-more\", \"nightly\", \"raw\", \"rayon\", \"rkyv\", \"rustc-dep-of-std\", \"rustc-internal-api\", \"serde\"]", "target": 9101038166729729440, "profile": 2241668132362809309, "path": 15251944583066559886, "deps": [[966925859616469517, "ahash", false, 4094955611283264222], [9150530836556604396, "allocator_api2", false, 3451347688227704903]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\hashbrown-176abc79a43df4c8\\dep-lib-hashbrown", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}