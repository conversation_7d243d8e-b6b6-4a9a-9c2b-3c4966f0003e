use crate::{
    database::Database,
    models::{
        BillingAccount, BillingAccountResponse, BillingResponse, Invoice, InvoiceResponse,
        PaginatedResponse, PaginationQuery, UsageRecord,
    },
    services::{ServiceError, ServiceResult},
    utils::Pagination,
};
use bson::{doc, oid::ObjectId};
use mongodb::Collection;
use tracing::instrument;
use futures::TryStreamExt;

pub struct BillingService {
    billing_accounts: Collection<BillingAccount>,
    invoices: Collection<Invoice>,
    usage_records: Collection<UsageRecord>,
}

impl BillingService {
    pub fn new(database: &Database) -> Self {
        Self {
            billing_accounts: database.collection("billing_accounts"),
            invoices: database.collection("invoices"),
            usage_records: database.collection("usage_records"),
        }
    }


    #[instrument(skip(self))]
    pub async fn get_billing_info(&self, user_id: &str) -> ServiceResult<BillingResponse> {
        let user_object_id = ObjectId::parse_str(user_id)
            .map_err(|_| ServiceError::Validation("Invalid user ID".to_string()))?;

        // Get or create billing account
        let billing_account = self
            .billing_accounts
            .find_one(doc! { "user_id": user_object_id }, None)
            .await?
            .unwrap_or_else(|| BillingAccount {
                id: None,
                user_id: user_object_id,
                balance: 0.0,
                pending_charges: 0.0,
                last_payment_date: None,
                created_at: chrono::Utc::now(),
                updated_at: chrono::Utc::now(),
            });

        // Calculate current usage
        let current_usage = self.calculate_current_usage(user_object_id).await?;

        // Get estimated monthly cost
        let estimated_monthly_cost = self.calculate_estimated_monthly_cost(user_object_id).await?;

        // Get recent invoices (last 5)
        let cursor = self
            .invoices
            .find(doc! { "user_id": user_object_id }, None)
            .await?;

        let recent_invoices: Vec<Invoice> = cursor
            .try_collect()
            .await?;

        let recent_invoice_responses: Vec<InvoiceResponse> = recent_invoices
            .into_iter()
            .map(|invoice| invoice.into())
            .collect();

        Ok(BillingResponse {
            account: BillingAccountResponse {
                balance: billing_account.balance,
                pending_charges: billing_account.pending_charges,
                last_payment_date: billing_account.last_payment_date,
            },
            current_usage,
            estimated_monthly_cost,
            recent_invoices: recent_invoice_responses,
        })
    }

    #[instrument(skip(self))]
    pub async fn list_user_invoices(
        &self,
        user_id: &str,
        pagination: PaginationQuery,
    ) -> ServiceResult<PaginatedResponse<InvoiceResponse>> {
        let user_object_id = ObjectId::parse_str(user_id)
            .map_err(|_| ServiceError::Validation("Invalid user ID".to_string()))?;

        let page = pagination.page.unwrap_or(1);
        let per_page = pagination.per_page.unwrap_or(20);

        // Get total count
        let total = self
            .invoices
            .count_documents(doc! { "user_id": user_object_id }, None)
            .await?;

        // Get paginated results
        let pagination_info = Pagination::new(page, per_page, total);
        let cursor = self
            .invoices
            .find(doc! { "user_id": user_object_id }, None)
            .await?;

        let invoices: Vec<Invoice> = cursor
            .try_collect()
            .await?;

        let invoice_responses: Vec<InvoiceResponse> = invoices
            .into_iter()
            .map(|invoice| invoice.into())
            .collect();

        Ok(PaginatedResponse {
            data: invoice_responses,
            meta: crate::models::PaginationMeta {
                page: pagination_info.page,
                per_page: pagination_info.per_page,
                total: pagination_info.total,
                total_pages: pagination_info.total_pages,
            },
        })
    }

    #[instrument(skip(self))]
    pub async fn get_user_invoice(
        &self,
        user_id: &str,
        invoice_id: &str,
    ) -> ServiceResult<InvoiceResponse> {
        let user_object_id = ObjectId::parse_str(user_id)
            .map_err(|_| ServiceError::Validation("Invalid user ID".to_string()))?;
        let invoice_object_id = ObjectId::parse_str(invoice_id)
            .map_err(|_| ServiceError::Validation("Invalid invoice ID".to_string()))?;

        let invoice = self
            .invoices
            .find_one(
                doc! { 
                    "_id": invoice_object_id,
                    "user_id": user_object_id 
                },
                None,
            )
            .await?
            .ok_or_else(|| ServiceError::NotFound("Invoice not found".to_string()))?;

        Ok(invoice.into())
    }

    #[instrument(skip(self, params))]
    pub async fn get_usage_data(
        &self,
        user_id: &str,
        params: serde_json::Value,
    ) -> ServiceResult<serde_json::Value> {
        let user_object_id = ObjectId::parse_str(user_id)
            .map_err(|_| ServiceError::Validation("Invalid user ID".to_string()))?;

        // Extract date range from params
        let start_date = params
            .get("start_date")
            .and_then(|v| v.as_str())
            .unwrap_or("2024-01-01");
        let end_date = params
            .get("end_date")
            .and_then(|v| v.as_str())
            .unwrap_or("2024-12-31");

        // Get usage records for the period
        let usage_records: Vec<UsageRecord> = self
            .usage_records
            .find(
                doc! { 
                    "user_id": user_object_id,
                    "recorded_at": {
                        "$gte": start_date,
                        "$lte": end_date
                    }
                },
                None,
            )
            .await?
            .try_collect()
            .await?;

        // Aggregate usage data
        let mut total_cost = 0.0;
        let mut usage_by_service = std::collections::HashMap::new();

        for record in usage_records {
            total_cost += record.cost;
            let service_usage = usage_by_service
                .entry(record.service_type)
                .or_insert_with(|| serde_json::json!({
                    "quantity": 0.0,
                    "cost": 0.0
                }));

            if let Some(obj) = service_usage.as_object_mut() {
                if let Some(quantity) = obj.get_mut("quantity") {
                    *quantity = serde_json::Value::Number(
                        serde_json::Number::from_f64(
                            quantity.as_f64().unwrap_or(0.0) + record.quantity
                        ).unwrap()
                    );
                }
                if let Some(cost) = obj.get_mut("cost") {
                    *cost = serde_json::Value::Number(
                        serde_json::Number::from_f64(
                            cost.as_f64().unwrap_or(0.0) + record.cost
                        ).unwrap()
                    );
                }
            }
        }

        Ok(serde_json::json!({
            "total_cost": total_cost,
            "usage_by_service": usage_by_service,
            "period": {
                "start_date": start_date,
                "end_date": end_date
            }
        }))
    }

    async fn calculate_current_usage(&self, user_id: ObjectId) -> ServiceResult<f64> {
        // Calculate current month usage
        let current_month = chrono::Utc::now().format("%Y-%m").to_string();
        
        let pipeline = vec![
            doc! {
                "$match": {
                    "user_id": user_id,
                    "billing_period": current_month
                }
            },
            doc! {
                "$group": {
                    "_id": null,
                    "total_cost": { "$sum": "$cost" }
                }
            }
        ];

        let mut cursor = self.usage_records.aggregate(pipeline, None).await?;
        if let Some(result) = cursor.try_next().await? {
            Ok(result.get_f64("total_cost").unwrap_or(0.0))
        } else {
            Ok(0.0)
        }
    }

    async fn calculate_estimated_monthly_cost(&self, user_id: ObjectId) -> ServiceResult<f64> {
        // This would calculate based on current instances and their monthly costs
        // For now, return a placeholder
        Ok(0.0)
    }
}
