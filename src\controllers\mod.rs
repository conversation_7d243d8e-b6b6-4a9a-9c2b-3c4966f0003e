pub mod admin;
pub mod auth;
pub mod billing;
pub mod health;
pub mod instances;
pub mod users;
pub mod vultr;

use crate::models::ApiResponse;
use axum::{
    http::StatusCode,
    response::{IntoResponse, Response},
    Json,
};
use serde_json::json;
use thiserror::Error;
use tracing::error;

#[derive(Error, Debug)]
pub enum ControllerError {
    #[error("Database error: {0}")]
    Database(#[from] mongodb::error::Error),
    
    #[error("Validation error: {0}")]
    Validation(String),
    
    #[error("Authentication error: {0}")]
    Authentication(String),
    
    #[error("Authorization error: {0}")]
    Authorization(String),
    
    #[error("Not found: {0}")]
    NotFound(String),
    
    #[error("Conflict: {0}")]
    Conflict(String),
    
    #[error("External API error: {0}")]
    ExternalApi(String),
    
    #[error("Internal server error: {0}")]
    Internal(String),
}

impl IntoResponse for ControllerError {
    fn into_response(self) -> Response {
        let (status, error_message) = match &self {
            ControllerError::Database(_) => {
                error!("Database error: {}", self);
                (StatusCode::INTERNAL_SERVER_ERROR, "Internal server error")
            }
            ControllerError::Validation(msg) => (StatusCode::BAD_REQUEST, msg.as_str()),
            ControllerError::Authentication(msg) => (StatusCode::UNAUTHORIZED, msg.as_str()),
            ControllerError::Authorization(msg) => (StatusCode::FORBIDDEN, msg.as_str()),
            ControllerError::NotFound(msg) => (StatusCode::NOT_FOUND, msg.as_str()),
            ControllerError::Conflict(msg) => (StatusCode::CONFLICT, msg.as_str()),
            ControllerError::ExternalApi(_) => {
                error!("External API error: {}", self);
                (StatusCode::BAD_GATEWAY, "External service error")
            }
            ControllerError::Internal(_) => {
                error!("Internal error: {}", self);
                (StatusCode::INTERNAL_SERVER_ERROR, "Internal server error")
            }
        };

        let body = Json(ApiResponse::<()>::error(error_message.to_string()));
        (status, body).into_response()
    }
}

pub type ControllerResult<T> = Result<T, ControllerError>;

// Helper function to create success responses
pub fn success_response<T>(data: T) -> Json<ApiResponse<T>> {
    Json(ApiResponse::success(data))
}

// Helper function to create message responses
pub fn message_response(message: String) -> Json<ApiResponse<()>> {
    Json(ApiResponse::message(message))
}

impl From<services::ServiceError> for ControllerError {
    fn from(err: services::ServiceError) -> Self {
        ControllerError::Service(err)
    }
}

impl From<axum::http::StatusCode> for ControllerError {
    fn from(status: axum::http::StatusCode) -> Self {
        ControllerError::Unauthorized(format!("HTTP error: {}", status))
    }
}


