fn(axum::extract::State<Arc<{type error}, {type error}>>, axum::http::Request<axum::body::Body>, axum::Json<serde_json::Value>) -> impl futures::Future<Output = Result<axum::Json<ApiResponse<models::user::UserProfile>>, ControllerError>> {update_profile}
fn(axum::extract::State<Arc<{type error}, {type error}>>, axum::http::Request<axum::body::Body>, axum::Json<serde_json::Value>) -> impl futures::Future<Output = Result<axum::Json<ApiResponse<models::user::UserProfile>>, ControllerError>> {update_profile}: Handler<_, _>
