fn(axum::extract::State<Arc<AppState>>, axum::extract::Path<std::string::String>, axum::http::Request<axum::body::Body>, axum::Json<serde_json::Value>) -> impl futures::Future<Output = Result<axum::Json<ApiResponse<models::instance::InstanceResponse>>, ControllerError>> {update_instance}
fn(axum::extract::State<Arc<AppState>>, axum::extract::Path<std::string::String>, axum::http::Request<axum::body::Body>, axum::Json<serde_json::Value>) -> impl futures::Future<Output = Result<axum::Json<ApiResponse<models::instance::InstanceResponse>>, ControllerError>> {update_instance}: Handler<_, _>
