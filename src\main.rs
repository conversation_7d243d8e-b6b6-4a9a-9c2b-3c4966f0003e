use achidas::{
    config::Config,
    database::Database,
    observability::setup_tracing,
    routes::create_router,
    vultr::VultrClient,
    AppState,
};
use anyhow::Result;
use std::sync::Arc;
use tokio::net::TcpListener;
use tower::ServiceBuilder;
use tower_http::{
    cors::CorsLayer,
    timeout::TimeoutLayer,
    trace::TraceLayer,
};
use tracing::{info, instrument};

#[tokio::main]
#[instrument]
async fn main() -> Result<()> {
    // Load configuration
    let config = Config::load()?;
    
    // Setup tracing and observability
    setup_tracing(&config).await?;
    
    info!("Starting Achidas hosting platform backend");
    
    // Initialize database connection
    let database = Database::new(&config.database_url).await?;
    info!("Database connection established");
    
    // Initialize Vultr API client
    let vultr_client = VultrClient::new(&config.vultr_api_key, &config)?;
    info!("Vultr API client initialized");
    
    // Create shared application state
    let app_state = Arc::new(AppState {
        config: config.clone(),
        database,
        vultr_client,
    });
    
    // Create router with middleware
    let app = create_router(app_state)
        .layer(
            ServiceBuilder::new()
                .layer(TraceLayer::new_for_http())
                .layer(CorsLayer::permissive())
                .layer(TimeoutLayer::from_secs(30))
        );
    
    // Start server
    let listener = TcpListener::bind(&config.server_address).await?;
    info!("Server listening on {}", config.server_address);
    
    axum::serve(listener, app).await?;
    
    Ok(())
}

#[derive(Clone)]
pub struct AppState {
    pub config: Config,
    pub database: Database,
    pub vultr_client: VultrClient,
}
