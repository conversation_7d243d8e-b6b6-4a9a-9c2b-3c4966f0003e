use crate::config::RetryConfig;
use backoff::{future::retry, ExponentialBackoff};
use reqwest::{Client, Request, Response};
use std::time::Duration;
use thiserror::Error;
use tracing::{error, warn, instrument};

#[derive(Error, Debug)]
pub enum RetryError {
    #[error("HTTP request failed: {0}")]
    Http(#[from] reqwest::Error),
    
    #[error("Max retries exceeded")]
    MaxRetriesExceeded,
    
    #[error("Request timeout")]
    Timeout,
}

#[derive(Debug, Clone)]
pub struct RetryClient {
    client: Client,
    config: RetryConfig,
}

impl RetryClient {
    pub fn new(client: Client, config: RetryConfig) -> Self {
        Self { client, config }
    }

    #[instrument(skip(self, url))]
    pub async fn get(&self, url: &str) -> Result<Response, RetryError> {
        let request = self.client.get(url).build()?;
        self.execute_with_retry(request).await
    }

    #[instrument(skip(self, url))]
    pub async fn post(&self, url: &str) -> RequestBuilder {
        RequestBuilder {
            client: self.clone(),
            request_builder: self.client.post(url),
        }
    }

    #[instrument(skip(self, url))]
    pub async fn put(&self, url: &str) -> RequestBuilder {
        RequestBuilder {
            client: self.clone(),
            request_builder: self.client.put(url),
        }
    }

    #[instrument(skip(self, url))]
    pub async fn patch(&self, url: &str) -> RequestBuilder {
        RequestBuilder {
            client: self.clone(),
            request_builder: self.client.patch(url),
        }
    }

    #[instrument(skip(self, url))]
    pub async fn delete(&self, url: &str) -> Result<Response, RetryError> {
        let request = self.client.delete(url).build()?;
        self.execute_with_retry(request).await
    }

    #[instrument(skip(self, request))]
    async fn execute_with_retry(&self, request: Request) -> Result<Response, RetryError> {
        let backoff = ExponentialBackoff {
            initial_interval: Duration::from_millis(self.config.initial_delay_ms),
            max_interval: Duration::from_millis(self.config.max_delay_ms),
            multiplier: self.config.multiplier,
            max_elapsed_time: Some(Duration::from_secs(300)), // 5 minutes total
            ..Default::default()
        };

        let operation = || async {
            let cloned_request = request.try_clone()
                .ok_or_else(|| backoff::Error::permanent(RetryError::Http(
                    reqwest::Error::from(reqwest::ErrorKind::Request)
                )))?;

            match self.client.execute(cloned_request).await {
                Ok(response) => {
                    if response.status().is_server_error() {
                        warn!("Server error {}, retrying...", response.status());
                        Err(backoff::Error::transient(RetryError::Http(
                            reqwest::Error::from(reqwest::ErrorKind::Request)
                        )))
                    } else if response.status().is_client_error() {
                        // Don't retry client errors
                        Ok(response)
                    } else {
                        Ok(response)
                    }
                }
                Err(e) => {
                    if e.is_timeout() || e.is_connect() {
                        warn!("Network error, retrying: {}", e);
                        Err(backoff::Error::transient(RetryError::Http(e)))
                    } else {
                        error!("Permanent error: {}", e);
                        Err(backoff::Error::permanent(RetryError::Http(e)))
                    }
                }
            }
        };

        retry(backoff, operation).await
            .map_err(|e| match e {
                backoff::Error::Permanent(err) => err,
                backoff::Error::Transient { err, .. } => err,
            })
    }
}

pub struct RequestBuilder {
    client: RetryClient,
    request_builder: reqwest::RequestBuilder,
}

impl RequestBuilder {
    pub fn json<T: serde::Serialize + ?Sized>(self, json: &T) -> Self {
        Self {
            client: self.client,
            request_builder: self.request_builder.json(json),
        }
    }

    pub fn header<K, V>(self, key: K, value: V) -> Self
    where
        K: AsRef<str>,
        V: AsRef<str>,
    {
        Self {
            client: self.client,
            request_builder: self.request_builder.header(key, value),
        }
    }

    pub async fn send(self) -> Result<Response, RetryError> {
        let request = self.request_builder.build()?;
        self.client.execute_with_retry(request).await
    }
}
