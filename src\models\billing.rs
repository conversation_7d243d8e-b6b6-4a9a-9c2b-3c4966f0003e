use bson::oid::ObjectId;
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BillingAccount {
    #[serde(rename = "_id", skip_serializing_if = "Option::is_none")]
    pub id: Option<ObjectId>,
    pub user_id: ObjectId,
    pub balance: f64,
    pub pending_charges: f64,
    pub last_payment_date: Option<DateTime<Utc>>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Invoice {
    #[serde(rename = "_id", skip_serializing_if = "Option::is_none")]
    pub id: Option<ObjectId>,
    pub user_id: ObjectId,
    pub invoice_number: String,
    pub amount: f64,
    pub status: InvoiceStatus,
    pub billing_period_start: DateTime<Utc>,
    pub billing_period_end: DateTime<Utc>,
    pub due_date: DateTime<Utc>,
    pub paid_date: Option<DateTime<Utc>>,
    pub line_items: Vec<LineItem>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum InvoiceStatus {
    Draft,
    Pending,
    Paid,
    Overdue,
    Cancelled,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LineItem {
    pub description: String,
    pub quantity: f64,
    pub unit_price: f64,
    pub total: f64,
    pub instance_id: Option<String>,
    pub service_type: ServiceType,
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq, Hash)]
pub enum ServiceType {
    Instance,
    Storage,
    Bandwidth,
    LoadBalancer,
    Database,
    Other,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UsageRecord {
    #[serde(rename = "_id", skip_serializing_if = "Option::is_none")]
    pub id: Option<ObjectId>,
    pub user_id: ObjectId,
    pub instance_id: Option<ObjectId>,
    pub service_type: ServiceType,
    pub usage_type: String,
    pub quantity: f64,
    pub unit: String,
    pub rate: f64,
    pub cost: f64,
    pub recorded_at: DateTime<Utc>,
    pub billing_period: String, // YYYY-MM format
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BillingResponse {
    pub account: BillingAccountResponse,
    pub current_usage: f64,
    pub estimated_monthly_cost: f64,
    pub recent_invoices: Vec<InvoiceResponse>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BillingAccountResponse {
    pub balance: f64,
    pub pending_charges: f64,
    pub last_payment_date: Option<DateTime<Utc>>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct InvoiceResponse {
    pub id: String,
    pub invoice_number: String,
    pub amount: f64,
    pub status: InvoiceStatus,
    pub billing_period_start: DateTime<Utc>,
    pub billing_period_end: DateTime<Utc>,
    pub due_date: DateTime<Utc>,
    pub paid_date: Option<DateTime<Utc>>,
    pub line_items: Vec<LineItem>,
}

impl From<Invoice> for InvoiceResponse {
    fn from(invoice: Invoice) -> Self {
        Self {
            id: invoice.id.map(|id| id.to_hex()).unwrap_or_default(),
            invoice_number: invoice.invoice_number,
            amount: invoice.amount,
            status: invoice.status,
            billing_period_start: invoice.billing_period_start,
            billing_period_end: invoice.billing_period_end,
            due_date: invoice.due_date,
            paid_date: invoice.paid_date,
            line_items: invoice.line_items,
        }
    }
}
