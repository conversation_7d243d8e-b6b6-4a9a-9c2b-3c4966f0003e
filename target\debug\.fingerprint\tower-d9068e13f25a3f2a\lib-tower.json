{"rustc": 2830703817519440116, "features": "[\"__common\", \"balance\", \"buffer\", \"default\", \"discover\", \"filter\", \"full\", \"futures-core\", \"futures-util\", \"hdrhistogram\", \"hedge\", \"indexmap\", \"limit\", \"load\", \"load-shed\", \"log\", \"make\", \"pin-project\", \"pin-project-lite\", \"rand\", \"ready-cache\", \"reconnect\", \"retry\", \"slab\", \"spawn-ready\", \"steer\", \"timeout\", \"tokio\", \"tokio-util\", \"tracing\", \"util\"]", "declared_features": "[\"__common\", \"balance\", \"buffer\", \"default\", \"discover\", \"filter\", \"full\", \"futures-core\", \"futures-util\", \"hdrhistogram\", \"hedge\", \"indexmap\", \"limit\", \"load\", \"load-shed\", \"log\", \"make\", \"pin-project\", \"pin-project-lite\", \"rand\", \"ready-cache\", \"reconnect\", \"retry\", \"slab\", \"spawn-ready\", \"steer\", \"timeout\", \"tokio\", \"tokio-stream\", \"tokio-util\", \"tracing\", \"util\"]", "target": 3486700084251681313, "profile": 15657897354478470176, "path": 15123001293515347867, "deps": [[784494742817713399, "tower_service", false, 10334702719707753232], [1288403060204016458, "tokio_util", false, 3703528974518153916], [1906322745568073236, "pin_project_lite", false, 5735289483764551673], [5451793922601807560, "slab", false, 7921673834338829864], [6264115378959545688, "pin_project", false, 391029249718820998], [7620660491849607393, "futures_core", false, 9385414846926858188], [7712452662827335977, "tower_layer", false, 9331745602861664882], [8153389937262086537, "hdrhistogram", false, 8828431015059146389], [8606274917505247608, "tracing", false, 17320339988540626530], [9538054652646069845, "tokio", false, 8568442550004330865], [10629569228670356391, "futures_util", false, 5228039834430210376], [13208667028893622512, "rand", false, 9577676731279368259], [14923790796823607459, "indexmap", false, 894619622242010043]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tower-d9068e13f25a3f2a\\dep-lib-tower", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}